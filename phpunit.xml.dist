<?xml version="1.0" encoding="UTF-8"?>
<!-- https://phpunit.readthedocs.io/en/latest/configuration.html -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.1/phpunit.xsd" backupGlobals="false" colors="true" bootstrap="tests/bootstrap.php" cacheDirectory=".phpunit.cache">
  <php>
    <ini name="display_errors" value="1"/>
    <ini name="error_reporting" value="-1"/>
    <server name="APP_ENV" value="test" force="true"/>
    <server name="SHELL_VERBOSITY" value="-1"/>
    <server name="SYMFONY_PHPUNIT_REMOVE" value=""/>
    <server name="SYMFONY_PHPUNIT_VERSION" value="9.5"/>
  </php>
  <testsuites>
    <testsuite name="Project Test Suite">
      <directory>tests</directory>
    </testsuite>
  </testsuites>
  <coverage/>
  <!-- Run `composer require symfony/panther` before enabling this extension -->
  <!--
    <extensions>
        <extension class="Symfony\Component\Panther\ServerExtension" />
    </extensions>
    -->
  <source>
    <include>
      <directory suffix=".php">src</directory>
    </include>
    <exclude>
      <directory suffix="Exception.php">src</directory>
      <directory suffix=".php">src/DataFixtures</directory>
      <directory suffix=".php">src/DTO</directory>
      <directory suffix=".php">src/Entity</directory>
      <directory suffix=".php">src/Enum</directory>
      <file>src/Kernel.php</file>
      <file>src/Documentation/OpenApi.php</file>
    </exclude>
  </source>
</phpunit>
