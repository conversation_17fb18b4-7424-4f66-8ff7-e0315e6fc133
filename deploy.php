<?php
namespace Deployer;

require 'recipe/symfony.php';
require 'contrib/slack.php';
require 'contrib/sentry.php';
require 'contrib/cachetool.php';

// Config
set('application', 'Mailer');
set('repository', '**************:demvsystems/mailer.git');
set('allow_anonymous_stats', false);

add('shared_files', ['.Halite.key', 'config/jwt/pw_public.pem']);
add('shared_dirs', []);
add('writable_dirs', []);

set('short_rev', function () {
    $repository = get('repository');
    try {
        if (input()->hasOption('revision') && input()->getOption('revision') !== null) {
            $revision = input()->getOption('revision');
        } elseif (($branch = get('branch')) !== null) {
            $revision = runLocally("git ls-remote --heads {$repository} {$branch} | cut -c1-40", shell: 'sh');
        } elseif (input()->hasOption('tag') && input()->getOption('tag') !== '') {
            $tag      = input()->getOption('tag');
            $revision = runLocally("git ls-remote --tags {$repository} {$tag} | cut -c1-40", shell: 'sh');
        }
    } catch (\Throwable $exception) {
        return null;
    }

    try {
        $shortRev = runLocally("git rev-parse --short {{target}}", shell: 'sh');
    } catch (\Throwable $exception) {
        $shortRev = $revision;
    }

    return $shortRev;
});

set('user', function () {
    try {
        return runLocally('git config --get user.name', shell: 'sh');
    } catch (\Throwable $exception) {
        return 'Developer';
    }
});

set('stage', function () {
    return get('labels')['stage'] ?? 'unknown';
});

// Hosts
host('srv01.dev.mailer.demv.systems')
    ->set('labels', ['stage' => 'dev'])
    ->set('remote_user', 'deployer')
    ->set('deploy_path', '/var/www/mailer');

host('srv02.dev.mailer.demv.systems')
    ->set('labels', ['stage' => 'staging'])
    ->set('remote_user', 'deployer')
    ->set('deploy_path', '/var/www/mailer');

host('srv[01:02].live.mailer.demv.systems')
    ->set('labels', ['stage' => 'live'])
    ->set('target', 'main')
    ->set('remote_user', 'deployer')
    ->set('deploy_path', '/var/www/mailer');

// run database operations only once to avoid race conditions in parallel deployment
task('database:migrate')->once();
task('doctrine:schema:validate')->once();

// Tasks
// This task is only for reference. We currently don't need a separate build task.
//task('build', function () {
//    cd('{{release_path}}');
//    run('npm run build');
//});

// If deploy fails automatically unlock.
after('deploy:failed', 'deploy:unlock');

// Migrate database before symlink new release.
before('deploy:symlink', 'database:migrate');

// Slack notifications
set('slack_webhook', '*****************************************************************************');
set('slack_success_text', ':white_check_mark: _{{user}}_ deployed `{{target}}` at `{{short_rev}}` on *{{stage}}* successfully');
set('slack_text', ':rocket: _{{user}}_ deploying `{{target}}` at `{{short_rev}}` on *{{stage}}*');

// Sentry Config
set('sentry', [
    'organization' => 'demv-systems',
    'projects' => [
        'mailer'
    ],
    'token' => '59b364b292b54b5084ae0816ba059196960fb05ff29141d8adf37253392a21ce',
    'sentry_server' => 'https://sentry.demv-systems.de'
]);

before('deploy', 'slack:notify');
after('deploy:symlink', 'cachetool:clear:opcache');
after('deploy:success', 'slack:notify:success');

task('deploy:sentry')->once();
after('deploy:success', 'deploy:sentry');
