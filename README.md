# Demv Mailer

## Setup
1. `make setup`
2. `127.0.0.1 mailer.demv.internal` zur `/etc/hosts` hinzuf<PERSON>gen

(<PERSON><PERSON><PERSON> beim `composer install` <PERSON><PERSON><PERSON>, der vendor-Ordner nicht die richtigen Rechte besitzen ("does not exist and could not be created") einmal `sudo chown <ownUser> vendor`)

Der Mailer sollte jetzt über http://mailer.demv.internal verfügbar sein.

### Datenbanken in PHPStorm einbinden

Um die **lokale Datenbank** einzubinden, Username und Passwort aus `.env.dist` bzw. `.env` verwenden. Den Port jedoch aus `docker-compose.yml` verwenden, da der interne Port 3306 nach außen hin auf 33330 gemappt wird.

Um die **Live-Datenbank** (aktuell `db01.live.mailer.demv.systems`) in PHPStorm einzubinden, muss auf dem Server dein SSH-Key hinterlegt sein. Unter `Data Sources and Drivers > SSH/SSL` check `Use SSH Tunnel` und SSH Config einrichten. Username und Passwort für die Datenbank liegen in Bitwarden. In `Data Sources and Drivers > General > Host` die IP `**********` verwenden. Mit der URL funktioniert es nicht, weil Hetzner nochmal ein internes Netzwerk hat, in dem unsere Server liegen. Die IP gehört zum DB-Server in dem internen Netzwerk.

Um **Dev-Datenbanken** einzubinden, ebenfalls SSH-Keys hinterlegen lassen, SSH Verbindungen einrichten und wieder Username und Passwort aus Bitwarden verwenden. Nur muss jetzt der Host `localhost` bleiben, weil die Datenbank auf dem gleichen Server liegt.

### XDebug für PHPStorm einrichten
1. Settings > PHP > Debug > Debug Port: `9003`
2. Settings > PHP > Servers > Add
   - Name: `mailer.demv.internal` (muss mit Host übereinstimmen)
   - Host: `mailer.demv.internal` Port:`80` Debugger:`XDebug`
   - Use path mapping: `/home/<USER>/projects/mailer` > `/var/www/html`

## Documentation
Viewing the OpenApi Documentation rendered with ReDoc is possible.
1. `127.0.0.1 openapi.mailer.demv.internal` zur `/etc/hosts` hinzufügen
2. `docker compose up -d` ausführen

Die Api-Dokumentation sollte jetzt über http://openapi.mailer.demv.internal verfügbar sein.

## Testing Configuration

Zu Testzwecken können alle Emails an eine Testadresse umgeleitet werden. Dazu auf dem betreffenden System folgende Umgebungsvariable setzen:
```dotenv
FORCED_MAILER_RECIPIENT="<EMAIL>"
```
Außerdem können alle Emails immer über denselben Versandweg verschickt werden, um nicht die tatsächlichen SMTP Daten der Nutzer zu verwenden. Dafür einfach `ALWAYS_SEND_WITH_SYSTEM_TRANSPORT=true` und eine passend `SYSTEM_MAILER_DSN_LIST` setzen.

### HTTP Request Files

Es gibt unter `http/` vorgefertigte Request Files mit denen die Endpunkte getestet werden können. Sie verwenden env-spezifische Variablen die in `http-client.env.json` hinterlegt sind.

Deine Login-Daten für Dev und Staging, sowie das Passwort für die verwendete SMTP-Verbindung kannst du in einer `http-client.private.env.json` hinterlegen.

```json
{
    "dev": {
        "pw-username": "",
        "pw-password": "",
        "emailsettings_smtp_password": ""
    },
    "staging": {
        "pw-username": "",
        "pw-password": "",
        "emailsettings_smtp_password": ""
    }
}
```

## Deployment

Es gibt Make-Targets für die einzelnen Zielsysteme. Dabei wird als Default immer der aktuelle Stand von `main` ausgerollt. Wenn ein bestimmter Branch ausgerollt werden: `.docker/deploy/deployer.sh deploy stage={dev|staging} --branch=<branch name>`

Wenn das Deployment irgendwie angepasst werden muss: Die `deploy.php` konfiguriert den [Deployer](https://deployer.org), der mit den Make-Targets gestartet wird.
