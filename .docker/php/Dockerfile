# syntax=docker/dockerfile:experimental
FROM composer:2 as COMPOSER
ENV COMPOSER_HOME=/usr/config/composer
ARG GITHUB_TOKEN
RUN composer config -g github-oauth.github.com $GITHUB_TOKEN

FROM php:8.2-fpm

ENV COMPOSER_HOME=/usr/config/composer
ARG USER_ID
ARG GROUP_ID
RUN usermod -u $USER_ID www-data && groupmod -g $GROUP_ID www-data

RUN mkdir /tmp/profile; chmod 777 /tmp/profile

# Install php-src extensions
RUN apt-get -qq update && apt-get -qq install \
         libzip-dev \
         php*-mbstring  \
         php*-mcrypt \
         unzip \
         git > /dev/null && \
     pecl install xdebug-3.2.0 > /dev/null && \
     docker-php-ext-enable xdebug > /dev/null && \
     docker-php-ext-install \
         -j$(nproc) \
         zip \
         pdo_mysql > /dev/null

# IMAP - PHP Extension
RUN apt-get -qq install libc-client-dev libkrb5-dev
RUN docker-php-ext-configure imap --with-kerberos --with-imap-ssl && docker-php-ext-install imap

# Install php redis
RUN pecl install redis && docker-php-ext-enable redis

# Clean up apt data and temporary build files
RUN rm -rf /tmp/pear /var/lib/apt

# Install composer and dependencies
COPY --chown=www-data:www-data --from=COMPOSER /usr/bin/composer /usr/bin/composer
COPY --chown=www-data:www-data --from=COMPOSER /usr/config/composer /usr/config/composer

# Make sure composer can checkout github
RUN mkdir -p /var/www/.ssh/ && \
    touch /var/www/.ssh/known_hosts && \
    ssh-keyscan github.com >> /var/www/.ssh/known_hosts

WORKDIR /var/www/html

USER www-data
