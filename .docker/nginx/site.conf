server {
    listen 80;
    server_name mailer.demv.internal;
    # Prevents gateway timeout during xdebug session
    fastcgi_read_timeout 1d;
    proxy_read_timeout 1d;

    # Make sure this matches the php-fpm project path
    # and this directory exists on the system (can be empty)
    root /var/www/html/public;
    index index.html index.htm index.php;

    charset utf-8;
    client_max_body_size    40M;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass mailer:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
