FROM php:8.1-cli-alpine
COPY --from=composer:2 /usr/bin/composer /usr/local/bin/composer

ARG UID
ARG GID

ENV USER=deployer
ENV GROUP=deployer

RUN apk add --no-cache \
    libzip-dev \
    openssh \
    git && \
    docker-php-ext-configure zip && \
    docker-php-ext-install zip > /dev/null

RUN addgroup -S ${GROUP} -g ${GID} && \
    adduser -S ${USER} -u ${UID} -G ${GROUP} && \
    mkdir -p /home/<USER>/.composer && \
    chown -R ${USER}:${GROUP} /home/<USER>

USER ${USER}:${GROUP}

ENV COMPOSER_HOME=/home/<USER>/.composer
RUN composer global require deployer/deployer:^v7.0.0
ENV PATH="${COMPOSER_HOME}/vendor/bin:${PATH}"

VOLUME ["/project", "/home/<USER>/.ssh"]
WORKDIR /project
