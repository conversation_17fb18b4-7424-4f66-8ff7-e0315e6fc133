#!/bin/bash

set -euo pipefail

# Find current folder
DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"

# Use a checksum of the dockerfile as the image version
TAG_NAME="demvsystems/deployer_7"
TAG_VERSION="$(shasum "$DIR/Dockerfile" | cut -d ' ' -f 1)"
TAG="$TAG_NAME:$TAG_VERSION"

USER_ID="$(id -u)"
GROUP_ID="$(id -g)"

# Build a new image if we pass --build or the image is outdated
if [ $# -ge 1 ] && [ "$1" = "--build" ] || [ "$(docker images -q "$TAG" 2>/dev/null)" == "" ]; then
  docker build \
    --build-arg UID="$USER_ID" \
    --build-arg GID="$GROUP_ID" \
    "--tag=$TAG" \
    "$DIR"

  # Remove --build if present
  if [ $# -ge 1 ] && [ "$1" = "--build" ]; then
    shift
  fi
fi

GIT_OPTS=""
if [ -d $HOME/.config/git ]; then
  GIT_OPTS="$GIT_OPTS -v $HOME/.config/git:/home/<USER>/.config/git"
fi
if [ -f $HOME/.gitconfig ]; then
  GIT_OPTS="$GIT_OPTS -v $HOME/.gitconfig:/home/<USER>/.gitconfig"
fi

AGENT_OPTS=""
if [ -S "$SSH_AUTH_SOCK" ]; then
  AGENT_OPTS="-v ${SSH_AUTH_SOCK}:/ssh-agent -e SSH_AUTH_SOCK=/ssh-agent"
fi

# Pass arguments to new temporary container
docker run --rm -it \
  -u "$USER_ID:$GROUP_ID" \
  -v "$DIR/../..:/project" \
  -v "$HOME/.ssh:/home/<USER>/.ssh" \
  $GIT_OPTS \
  $AGENT_OPTS \
  "$TAG" \
  dep "$@"
