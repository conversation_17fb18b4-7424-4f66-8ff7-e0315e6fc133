# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    app.system_mailer_dsn: '%env(SYSTEM_MAILER_DSN)%'
    app.fallback_mailer_sender: '%env(FALLBACK_MAILER_SENDER)%'
    app.fallback_mailer_recipient: '%env(FALLBACK_MAILER_RECIPIENT)%'
    app.always_send_with_system_transport: '%env(bool:ALWAYS_SEND_WITH_SYSTEM_TRANSPORT)%'
    app.batch_timeout: '%env(float:BATCH_TIMEOUT)%'
    app.queue_send_timeout: '%env(float:QUEUE_SEND_TIMEOUT)%'
    professionalworks.base_url: https://crm.deutscher-maklerverbund.de
    professionalworks.admin_token: '%env(PW_ADMIN_TOKEN)%'

when@dev:
    parameters:
        professionalworks.base_url: http://professionalworks.demv.internal

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            Ambta\DoctrineEncryptBundle\Encryptors\EncryptorInterface: '@ambta_doctrine_encrypt.encryptor'
    _instanceof:
        App\Email\Provider\ProviderConfigInterface:
            tags: [ 'app.provider_config' ]

#    Symfony\Component\Serializer\Normalizer\BackedEnumNormalizer: ~
#    Symfony\Component\Serializer\Normalizer\ObjectNormalizer: ~

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/DTO/'
            - '../src/Logging/'
            - '../src/Entity/'
            - '../src/Kernel.php'
    app.system_transport:
        class: App\Transport\TransportInterface
        factory: [ '@app.transport_factory', 'fromDsnString' ]
        arguments: [ '%app.system_mailer_dsn%' ]
    app.fallback_mailer_sender_address:
        class: Symfony\Component\Mime\Address
        factory: [ 'Symfony\Component\Mime\Address', 'create' ]
        arguments: [ '%app.fallback_mailer_sender%' ]
    app.fallback_mailer_recipient_address:
        class: Symfony\Component\Mime\Address
        factory: [ 'Symfony\Component\Mime\Address', 'create' ]
        arguments: [ '%app.fallback_mailer_recipient%' ]
    App\Service\EmailTransportService:
        arguments:
            $transportFactory: '@app.transport_factory'
            $systemTransport: '@app.system_transport'
            $fallbackTransport: '@app.fallback_transport'
            $alwaysSendWithSystemTransport: '%app.always_send_with_system_transport%'
    app.transport_factory:
        class: App\Transport\TransportFactoryAdapter
        arguments:
            $transportFactory: '@mailer.transport_factory'
    App\Transport\LoggedTransportFactory:
        decorates: 'app.transport_factory'
    app.fallback_transport:
        class: App\Transport\SenderAdjustingTransport
        arguments:
            $inner: '@app.system_transport'
            $newSender: '@app.fallback_mailer_sender_address'
    App\Gateway\ProfessionalworksGatewayFactory:
        class: App\Gateway\ProfessionalworksGatewayFactory
        arguments:
            $baseUrl: '%professionalworks.base_url%'
            $origin: demv-mailer
            $apiToken: '%professionalworks.admin_token%'
    Demv\ProfessionalworksSdk\Gateway\ProfessionalworksGateway:
        class: Demv\ProfessionalworksSdk\Gateway\ProfessionalworksGateway
        arguments:
            $baseUrl: '%professionalworks.base_url%'
            $token: ''
            $origin: demv-mailer
    mailer.transport_factory.api:
        class: App\Email\ApiTransportFactory
        tags:
            - { name: mailer.transport_factory }
    Symfony\Component\Mailer\Transport\Smtp\EsmtpTransportFactory:
        class: Symfony\Component\Mailer\Transport\Smtp\EsmtpTransportFactory
    mailer.transport_factory.smtp:
        class: App\Email\SmtpTransportFactory
        arguments:
            $providerConfig: !tagged_iterator app.provider_config
            $esmtpTransportFactory: '@Symfony\Component\Mailer\Transport\Smtp\EsmtpTransportFactory'
        tags:
            - { name: mailer.transport_factory }
    Symfony\Component\Mailer\Bridge\Amazon\Transport\SesTransportFactory:
        class: Symfony\Component\Mailer\Bridge\Amazon\Transport\SesTransportFactory
    mailer.transport_factory.amazon:
        class: App\Email\SesSmtpTransportFactory
        arguments:
            $sesSmtpTransportFactory: '@Symfony\Component\Mailer\Bridge\Amazon\Transport\SesTransportFactory'
        tags:
            - { name: mailer.transport_factory }
    App\Service\SendEmailService:
        class: App\Service\SendEmailService
        arguments:
            $fallbackRecipient: '@app.fallback_mailer_recipient_address'
    App\OAuth\MicrosoftProvider:
        class: App\OAuth\MicrosoftProvider
        arguments:
            $clientId: '%env(MICROSOFT_OAUTH_CLIENT_ID)%'
            $clientSecret: '%env(MICROSOFT_OAUTH_CLIENT_SECRET)%'
    App\OAuth\GoogleProvider:
        class: App\OAuth\GoogleProvider
        arguments:
            $clientId: '%env(GOOGLE_CLIENT_ID)%'
            $clientSecret: '%env(GOOGLE_CLIENT_SECRET)%'
    App\Factory\MailboxService: ~
    App\Mailbox\MailboxInterface:
        factory: [ '@App\Mailbox\MailboxService', 'createForUserFromDb' ]
    App\Service\UserInfoServiceInterface:
        class: App\Service\UserInfoService
    League\HTMLToMarkdown\HtmlConverter:
        class: 'League\HTMLToMarkdown\HtmlConverter'
    League\HTMLToMarkdown\HtmlConverterInterface: '@League\HTMLToMarkdown\HtmlConverter'

#    ambta_doctrine_encrypt.orm_subscriber:
#        class: App\EventSubscriber\EncryptionSubscriber
#        arguments: [ "@ambta_doctrine_attribute_reader", "@ambta_doctrine_encrypt.encryptor" ]
#        tags:
#            - { name: doctrine.event_subscriber }

    App\Service\QueueStatsService:
        arguments:
            $receiverLocator: '@messenger.receiver_locator'

    App\EventListener\ExceptionListener:
        tags:
            - { name: kernel.event_listener, event: kernel.exception }
            - { name: kernel.event_listener, event: console.error }

    ### Logging:
    App\Logging\ServerProcessor:
        tags:
            - { name: monolog.processor }
    email_history_handler:
        class: App\Logging\EmailHistoryHandler
    Monolog\Processor\UidProcessor:
        tags:
            - { name: monolog.processor }
    App\Logging\UserIdProcessor:
        tags:
            - { name: monolog.processor }
    App\Logging\EmailUuidProcessor:
        tags:
            - { name: monolog.processor }
    App\Logging\LogFormatter:
    RateLimitRedis:
        class: Redis
        factory: [ 'Symfony\Component\Cache\Adapter\RedisAdapter', 'createConnection' ]
        arguments:
            - '%env(MESSENGER_TRANSPORT_DSN)%/1'
    App\Service\RateLimitService:
        arguments:
            $redis: '@RateLimitRedis'
    App\Serializer\AttachmentDenormalizer:
        arguments:
            $normalizer: '@serializer.normalizer.object'
        tags:
            - { name: serializer.denormalizer }
