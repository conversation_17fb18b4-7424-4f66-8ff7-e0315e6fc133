services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
    App\Tests\Util\Factory\:
        resource: '../tests/util/factory/'
    App\Tests\Util\transports\DelayedTransportFactory:
        decorates: 'app.transport_factory'
        decoration_priority: 5
    App\Tests\Util\transports\TestTransportFactory:
        decorates: 'app.transport_factory'
        decoration_priority: 10
    App\Security\GoogleOAuthClientProvider:
        public: true
    App\Email\ApiTransportFactory:
        public: true
    Google\Client:
        public: true
    App\Service\UserInfoServiceInterface:
        class: App\Tests\Util\Service\TestUserInfoService
    App\Service\EmailFileService:
        class: App\Tests\Util\Service\TestEmailFileService
