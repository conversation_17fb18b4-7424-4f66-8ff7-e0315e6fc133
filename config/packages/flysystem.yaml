# Read the documentation at https://github.com/thephpleague/flysystem-bundle/blob/master/docs/1-getting-started.md
flysystem:
    storages:
        default.storage:
            adapter: 'aws'
            options:
                client: Aws\S3\S3Client
                bucket: '%env(resolve:AWS_BUCKET)%'

when@test:
    flysystem:
        storages:
            default.storage:
                adapter: 'memory'
