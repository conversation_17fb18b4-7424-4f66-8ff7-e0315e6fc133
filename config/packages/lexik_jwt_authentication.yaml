lexik_jwt_authentication:
    # as long as the mailer doesn't use its own, we have to set the public key of PW as its public key as the lexik
    # bundle expects at least a public or a secret key (normally it would belong in the "additional_public_keys" section)
    public_key: '%env(resolve:PW_PUBLIC_KEY)%'
    user_id_claim: service_id

    encoder:
        # token encoder/decoder service - default implementation based on the lcobucci/jwt library
        service: lexik_jwt_authentication.encoder.lcobucci
        signature_algorithm: RS512

    token_extractors:
            authorization_header:
                enabled: true
                prefix: Bearer
                name: Authorization
