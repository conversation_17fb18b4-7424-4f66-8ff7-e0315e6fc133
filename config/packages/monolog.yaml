monolog:
    channels:
        - deprecation # Deprecations are logged in the dedicated "deprecation" channel when it exists

    handlers:
        EmailHistoryHandler:
            type: service
            id: email_history_handler

when@dev:
    monolog:
        handlers:
            main:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: debug
                channels: ["!event"]
                formatter: App\Logging\LogFormatter
            console:
                type: console
                process_psr_3_messages: false
                channels: ["!event", "!doctrine", "!console"]
            deprecation:
                type: stream
                channels: [ deprecation ]
                path: "%kernel.logs_dir%/%kernel.environment%-deprecation.log"

when@test:
    monolog:
        handlers:
            main:
                type: fingers_crossed
                action_level: error
                handler: nested
                excluded_http_codes: [404, 405]
                channels: ["!event"]
            nested:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: debug

when@prod:
    monolog:
        handlers:
            main:
                type: fingers_crossed
                action_level: error
                handler: nested
                excluded_http_codes: [404, 405]
                buffer_size: 2000 # How many messages should be saved? Prevent memory leaks
                passthru_level: info
            nested:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: debug
                formatter: App\Logging\LogFormatter
            console:
                type: console
                process_psr_3_messages: false
                channels: ["!event", "!doctrine"]
            deprecation:
                type: stream
                channels: [deprecation]
                path: php://stderr
