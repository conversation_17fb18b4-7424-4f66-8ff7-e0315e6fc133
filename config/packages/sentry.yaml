sentry:
    dsn: '%env(SENTRY_DSN)%'
    register_error_listener: false
    register_error_handler: false
    options:
        # only trace 1% of requests (to not overwhelm the Servers as they are at their limits already)
        # We can increase this value when we are shure that the servers can handle it
        traces_sample_rate: 0.01
    tracing:
        enabled: true
        dbal: # DB queries
            enabled: true
        cache: # we dont have any caches yet
            enabled: false
        twig: # we have no frontend
            enabled: false
        http_client: # Symfony HTTP client
            enabled: true
