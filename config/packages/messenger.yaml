framework:
    messenger:
        failure_transport: failed

        transports:
            # https://symfony.com/doc/current/messenger.html#transport-configuration
            sync: 'sync://'
            persist_email:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                failure_transport: failed
                retry_strategy:
                    max_retries: 3
                    delay: 1000
                options:
                    stream: 'persist_email'
            send_email:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                failure_transport: failed
                retry_strategy:
                    max_retries: 3
                    delay: 60000
                options:
                    stream: 'send_email'
            failed:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                retry_strategy:
                    service: 'App\Infrastructure\FailedTransportRetryStrategy'

        routing:
            'App\Message\PersistEmailMessage': persist_email
            'App\Message\SendEmailMessage': send_email
