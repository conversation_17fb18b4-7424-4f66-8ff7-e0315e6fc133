#!/bin/bash
PW_PATH_ARG="$1"

# Get the current directory
CURRENT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
# Get the parent directory
PARENT_DIR="$(dirname "$CURRENT_DIR")"

# Construct the potential Professionalworks path
PW_PATH="$PARENT_DIR/professionalworks"

# Check if the Professionalworks directory exists
if [ -d "$PW_PATH" ]; then
  echo "Professionalworks directory found at $PW_PATH"
elif [ -d "$PW_PATH_ARG" ]; then
  PW_PATH="$PW_PATH_ARG"
  echo "Professionalworks directory found at $PW_PATH"
else
  echo "Professionalworks directory not found. Please specify the path."
  exit 1
fi

# setup mailer
echo "Generiere Professionalworks Api-Token..."
while true; do
  if [ -d "$PW_PATH" ]; then
      pw_db_token=$(cd "$PW_PATH" && docker compose exec php-fpm php console.php createdbauthtoken createForAdmin --serviceId=beratung --name="Beratung$(date +%s)" | grep -oP 'token=\K[^ ]+')
  fi
  if [ ${#pw_db_token} = 0 ]; then
    read -r -p "Der Token konnte nicht generiert werden. Bitte hier http://professionalworks.demv.internal/auth/token/admin manuell generieren und eingeben: " pw_db_token
    break;
  else
    read -r -p "Folgenden Token übernehmen?: " -e -i "$pw_db_token" pw_db_token
    break;
  fi
done

echo "PW_ADMIN_TOKEN=$pw_db_token" >> "$CURRENT_DIR/.env"

echo "Kopiere Professionalworks PublicKey..."
PW_ENV_AUTH_FILE="$PW_PATH/.env.auth"

# Zielverzeichnis für den öffentlichen Schlüssel
TARGET_DIR="config/jwt"
TARGET_FILE="$TARGET_DIR/pw_public.pem"

# Überprüfen, ob die .env.auth Datei existiert
if [ -f "$PW_ENV_AUTH_FILE" ]; then
  # Extrahieren des öffentlichen Schlüssels und Schreiben in die Zieldatei
  sed -n '/-----BEGIN PUBLIC KEY-----/,/-----END PUBLIC KEY-----/p' "$PW_ENV_AUTH_FILE" | sed 's/AUTH_PUBLIC_KEY="//' | sed 's/"$//' > "$TARGET_FILE"

  echo "Der PublicKey wurde in $TARGET_FILE übertragen."
else
  echo "Die Datei $PW_ENV_AUTH_FILE existiert nicht."
  exit
fi
