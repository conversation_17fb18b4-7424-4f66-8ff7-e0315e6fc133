<?php

declare(strict_types=1);

use <PERSON>\Config\RectorConfig;
use <PERSON>\Set\ValueObject\LevelSetList;
use <PERSON>\Symfony\Set\SymfonySetList;

return RectorConfig::configure()
    ->withPaths([
        __DIR__ . '/config',
        __DIR__ . '/public',
        __DIR__ . '/src',
        __DIR__ . '/tests',
    ])
    // uncomment to reach your current PHP version
//     ->withPhpSets()
//    ->withTypeCoverageLevel(0)
//    ->withRules([
//        TypedPropertyFromStrictConstructorRector::class
//    ])
    // here we can define, what prepared sets of rules will be applied
//    ->withPreparedSets(
//        deadCode: true,
//        codeQuality: true
//    )
    ->withSets([
        SymfonySetList::SYMFONY_70,
        LevelSetList::UP_TO_PHP_83
    ]);
