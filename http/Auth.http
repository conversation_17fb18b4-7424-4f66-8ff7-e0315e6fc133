### Authenticate
POST {{pw-url}}/home/<USER>
Content-Type: application/json

{
  "username": "{{pw-username}}",
  "password": "{{pw-password}}",
  "rememberMe": 0
}

> {%
    client.assert(response.status === 200, "Status-code must be 200");
 %}

### Get Authtoken
# @no-redirect
GET {{pw-url}}/auth/auth/token

> {%
    client.assert(response.body.data.token !== null, "there must be a token");
    client.global.set("access_token", response.body.data.token)
 %}
