### Post SES SubscriptionConfirmation
POST {{url}}/v1/bounces/ses
x-amz-sns-message-type: SubscriptionConfirmation
Accept: application/json
Content-Type: application/json

{
  "Type" : "SubscriptionConfirmation",
  "MessageId" : "8eb4e297-ef7e-485e-8612-abca2524351e",
  "Token" : "2a9f39e8fc8ee6a2fecfb06e2c5bcfd897b906329315f2863ad29c682c92",
  "TopicArn" : "arn:aws:sns:eu-central-1:123456789098:mailer",
  "Message" : "You have chosen to subscribe to the topic arn:aws:sns:eu-central-1:123456789098:mailer.\nTo confirm the subscription, visit the SubscribeURL included in this message.",
  "SubscribeURL" : "https://sns.eu-central-1.amazonaws.com/?Action=ConfirmSubscription&TopicArn=arn:aws:sns:eu-central-1:123456789098:mailer&Token=2a9f39e8fc8ee6a2fecfb06e2c5bcfd897b906329315f2863ad29c682c92",
  "Timestamp" : "2024-01-31T16:01:22.135Z",
  "SignatureVersion" : "1",
  "Signature" : "WjNPAMzX8en24rwYWgS8js8lEi4hd4eqxvxwiBY+mKO6GUxHr22PVWRzKJWYxrHvsHq2X/qUBNNhq05M7EKU1XQF5lXmKg==",
  "SigningCertURL" : "https://sns.eu-central-1.amazonaws.com/SimpleNotificationService-5f2863ad29c682c92.pem"
}

### Post SES Bounce
POST {{url}}/v1/bounces/ses
x-amz-sns-message-type: Notification
Accept: application/json
Content-Type: application/json

{
  "Type" : "Notification",
  "MessageId" : "82e2c308-68fa-5ebd-b0c9-9d32af76f210",
  "TopicArn" : "arn:aws:sns:eu-central-1:************:mailer",
  "Message" : "{\"notificationType\":\"Bounce\",\"bounce\":{\"feedbackId\":\"0107018d7902b04a-6fdd330a-d585-464c-b056-5f3cb7752475-000000\",\"bounceType\":\"Permanent\",\"bounceSubType\":\"General\",\"bouncedRecipients\":[{\"emailAddress\":\"<EMAIL>\",\"action\":\"failed\",\"status\":\"5.1.1\",\"diagnosticCode\":\"smtp; 550 5.1.1 user unknown\"}],\"timestamp\":\"2024-02-05T11:23:55.000Z\",\"remoteMtaIp\":\"************\",\"reportingMTA\":\"dns; b224-11.smtp-out.eu-central-1.amazonses.com\"},\"mail\":{\"timestamp\":\"2024-02-05T11:23:55.471Z\",\"source\":\"<EMAIL>\",\"sourceArn\":\"arn:aws:ses:eu-central-1:************:identity/demv.de\",\"sourceIp\":\"*************\",\"callerIdentity\":\"UserAccess\",\"sendingAccountId\":\"************\",\"messageId\":\"0107018d7902aecf-351025d2-8cd4-43fb-a614-ca5ca49bdbc9-000000\",\"destination\":[\"<EMAIL>\"],\"headersTruncated\":false,\"headers\":[{\"name\":\"From\",\"value\":\"<EMAIL>\"},{\"name\":\"To\",\"value\":\"<EMAIL>\"},{\"name\":\"Subject\",\"value\":\"test\"},{\"name\":\"MIME-Version\",\"value\":\"1.0\"},{\"name\":\"Content-Type\",\"value\":\"multipart/alternative;  boundary=\\\"----=_Part_4129194_606026113.*************\\\"\"}],\"commonHeaders\":{\"from\":[\"<EMAIL>\"],\"to\":[\"<EMAIL>\"],\"subject\":\"test\"}}}",
  "Timestamp" : "2024-02-05T11:23:56.167Z",
  "SignatureVersion" : "1",
  "Signature" : "b5w/TjHt02T31ulTk9V/AJIUWiPOk3MuuoEGDoEkZNf28RflEQaXJoYSzHdMZdES+bNxfXTZT/4p4csmhjXwSVepS3mHKUHfIJb9MlTzd4Z1TDUhUKl0Ju22EXqPAxcOvCO1LPk9dVK4tjLMC7EXi/OiWSaQ+KKdg89YDghCQs4qq1uYLIF4owOk7efhgPeXXBO6cpruPdVIbKDppCg+6HX6C3awJviaU+4yQiSckwmDToEQfvuYGzuJPwl5Gr9ijxpN8+UQ8IxiACodJhkkHUyaFBGE/dGB7SV6errJvzdqWkmmuwlhIengeJWwi06OpiKFLQBi/KR32gJzN7Ht8w==",
  "SigningCertURL" : "https://sns.eu-central-1.amazonaws.com/SimpleNotificationService-60eadc530605d63b8e62a523676ef735.pem",
  "UnsubscribeURL" : "https://sns.eu-central-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-central-1:************:mailer:e7c4214b-737f-4983-a282-8ee9e1351f0e"
}
