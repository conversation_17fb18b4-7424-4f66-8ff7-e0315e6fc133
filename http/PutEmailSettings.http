### EmailSettingsCheck (SMTP)
PUT {{url}}/v1/users/me/email-settings
Accept: application/json
Content-Type: application/json
Authorization: Bearer {{access_token}}

{
  "email": "{{emailsettings_email}}",
  "scheme": "smtp",
  "senderName": "{{emailsettings_sender_name}}",
  "sendingStatus": "active",
  "receivingStatus": "inactive",
  "smtpImapConnection": {
    "smtpHost": "{{emailsettings_smtp_host}}",
    "smtpPort": {{emailsettings_smtp_port}},
    "imapHost": "{{emailsettings_imap_host}}",
    "imapPort": {{emailsettings_imap_port}},
    "username": "{{emailsettings_smtp_username}}",
    "password": "{{emailsettings_smtp_password}}"
  }
}
