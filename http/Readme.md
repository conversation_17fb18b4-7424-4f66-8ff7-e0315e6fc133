# Jetbrains IDE Http-Requests

Die `*.http` files in diesem Ordner könne von den Jetbrains IDEs genutzt werden, um HTTP/S Requests auszuführen.

Für die Authentifizierung bitte zuerst `Auth.http` ausführen.

Falls die Request gegen den DEV-Server ausgeführt werden sollen, muss folgende Konfiguration gemacht werden.
Die Konfiguration für Dev-Environment sollte in der `http-client.private.env.json` hinterlegt werden.
```json
{
  "dev": {
    "pw-username": "###---YOUR_PW_USER---###",
    "pw-password": "###---YOUR_PW_PASSWORD---###",
    "emailsettings_smtp_password": "###---TESTMAIL_SMTP_PASSWORD---###"
  }
}
```

In der `http-client.private.env.json` können auch Einstellungen anderer Environments überschrieben werden.
