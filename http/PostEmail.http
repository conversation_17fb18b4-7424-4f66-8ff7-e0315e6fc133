### SendEmails
POST {{url}}/v1/emails
Accept: application/json
Content-Type: application/json
Authorization: Bear<PERSON> {{access_token}}

{
  "mode": "synchronous",
  "useFailsafeTransport": true,
  "emails": [
    {
      "from": {
        "address": "{{emailsettings_email}}",
        "name": "{{emailsettings_sender_name}}"
      },
      "to": [
        {
          "address": "<EMAIL>",
          "name": "Tester 1"
        }
      ],
      "subject": "Subject (Email 1)",
      "text": "Text (Email 1)",
      "html": "Html (Email 1)"
    },
    {
      "from": {
        "address": "{{emailsettings_email}}",
        "name": "{{emailsettings_sender_name}}"
      },
      "to": [
        {
          "address": "<EMAIL>",
          "name": "Tester 2"
        }
      ],
      "subject": "Subject (Email 2)",
      "text": "Text (Email 2)",
      "html": "Html (Email 2)"
    }
  ]
}

### SendEmailWithInlineAttachment
POST {{url}}/v1/emails
Accept: application/json
Content-Type: application/json
Authorization: Bearer {{access_token}}

{
  "mode": "synchronous",
  "useFailsafeTransport": true,
  "emails": [
    {
      "from": {
        "address": "{{emailsettings_email}}",
        "name": "{{emailsettings_sender_name}}"
      },
      "to": [
        {
          "address": "<EMAIL>",
          "name": "Tester 1"
        }
      ],
      "subject": "Subject",
      "html": "Html with inline image: <img src=\"cid:fc6030e3bbddbc630e91338b688306cb\">",
      "attachments": [
        {
          "name": "demv-logo.png",
          "body": "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",
          "contentType": "image/png",
          "contentDisposition": "inline",
          "contentId": "fc6030e3bbddbc630e91338b688306cb"
        }
      ]
    }
  ]
}
