<?php

declare(strict_types=1);

namespace App\Tests\Integration\EventListener;

use App\Converter\EmailEntityConverter;
use App\DataFixtures\EmailFixtures;
use App\DataFixtures\EmailSettingsFixtures;
use App\Enum\EmailStatus;
use App\EventListener\EmailListener;
use App\Repository\EmailRepository;
use App\Service\EmailFileService;
use App\Util\EmailHistoryLogger;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class EmailListenerTest extends WebTestCase
{
    /**
     * @test
     */
    public function it_calls_callback_link_if_status_is_updated(): void
    {
        $container = self::getContainer();

        $newStatus = EmailStatus::DELIVERY_FAILED_PERMANENTLY;
        $statusCallbackUrl = 'test://uid';

        $client = $this->createMock(HttpClientInterface::class);
        $logger = $this->createMock(EmailHistoryLogger::class);
        $emailEntityConverter = new EmailEntityConverter($this->createMock(EmailFileService::class));

        $emailListener = new EmailListener($client, $logger, $emailEntityConverter);
        $container->set(EmailListener::class, $emailListener);

        $container
            ->get(DatabaseToolCollection::class)
            ->get()
            ->loadFixtures([EmailSettingsFixtures::class, EmailFixtures::class]);

        $em = $container->get('doctrine.orm.entity_manager');

        /** @var EmailRepository $emailRepo */
        $emailRepo = $container->get(EmailRepository::class);
        $email = $emailRepo->findOneBy(['status' => EmailStatus::SENT]);

        self::assertNotNull($email);

        $email->setStatusCallbackUrl($statusCallbackUrl);
        $email->setStatus($newStatus);
        $expectedSentEmail = $emailEntityConverter->toResponseDto($email);

        $client->expects(self::once())->method('request')->with(
            self::equalTo('POST'),
            self::equalTo($statusCallbackUrl),
            self::equalTo(['json' => $expectedSentEmail])
        );

        $em->flush();
    }
}
