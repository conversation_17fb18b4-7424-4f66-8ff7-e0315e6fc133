<?php

declare(strict_types=1);

namespace App\Tests\Integration\Controller;

use App\Mailbox\ImapMailbox;
use App\Mailbox\MailboxService;
use App\Tests\Util\Factory\UserFactory;
use Psr\Log\LoggerInterface;
use stdClass;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Webklex\PHPIMAP\Client;
use Webklex\PHPIMAP\Folder;
use Webklex\PHPIMAP\Query\WhereQuery;
use Webklex\PHPIMAP\Support\MessageCollection;

class InboxControllerTest extends WebTestCase
{
    private const BASE_URL = '/v1/inbox';
    private const UIDVALIDITY = 1690794008;

    private KernelBrowser $client;

    private ContainerInterface $container;

    public function setUp(): void
    {
        $this->client = static::createClient();
        $this->client->loginUser(UserFactory::createPwUser());

        $this->container = self::getContainer();
    }

    /**
     * @test
     */
    public function it_returns_200_and_status_success_on_success(): void
    {
        $this->setStandardMailboxMock();
        $response = $this->makeJsonRequest();

        self::assertSame('success', $response->status);
        self::assertResponseStatusCodeSame(200);
    }

    /**
     * @test
     */
    public function it_denies_access_if_user_has_not_inbox_role(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser());
        $this->setStandardMailboxMock();
        $this->makeJsonRequest();

        self::assertResponseStatusCodeSame(403);
    }

    /**
     * @test
     */
    public function it_checks_uidvalidity_when_fetching_single_emails(): void
    {
        $this->setStandardMailboxMock(1);

        $emailId = (self::UIDVALIDITY + 1) . '-' . 1;

        $url = self::BASE_URL . '/' . $emailId;

        $response = $this->makeJsonRequest(uri: $url);

        self::assertResponseStatusCodeSame(409);
        self::assertSame('error', $response->status);
        self::assertSame('Uidvalidity of INBOX changed. Uid of mail #1 need to be fetched again.', $response->error);
    }

    private function setStandardMailboxMock(int $numberOfEmails = 0): void
    {
        /**
         * @phpstan-ignore-next-line
         * todo: fix deprecation notice: https://github.com/sebastianbergmann/phpunit/issues/5320
         */
        $whereQueryMock = $this->getMockBuilder(WhereQuery::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['get', 'fetchBody', 'leaveUnread'])
            ->addMethods(['since'])
            ->getMock();
        $whereQueryMock->method('get')->willReturn(new MessageCollection([]));
        $whereQueryMock->method('fetchBody')->willReturn($whereQueryMock);
        $whereQueryMock->method('leaveUnread')->willReturn($whereQueryMock);
        $whereQueryMock->method('since')->willReturn($whereQueryMock);

        $folderMock = $this->getMockBuilder(Folder::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['query', 'overview'])
            ->getMock();
        $folderMock->path = 'INBOX';
        $folderMock->method('query')->willReturn($whereQueryMock);

        $mailboxMock = $this->getMockBuilder(Client::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['checkFolder', 'getFolder', 'connect', 'isConnected'])
            ->getMock();
        $mailboxMock->method('checkFolder')->willReturn(['uidvalidity' => self::UIDVALIDITY]);
        $mailboxMock->method('getFolder')->willReturn($folderMock);
        $mailboxMock->method('connect')->willReturnSelf();
        $mailboxMock->method('isConnected')->willReturn(true);


        $mailInfos = [];

        for ($i = 1; $i <= $numberOfEmails; $i++) {
            $mailInfos[] = (object) [
                'subject' => 'test subject',
                'from' => 'testsender@emai.l',
                'sender' => 'Test Sender',
                'to' => 'testreceiver@emai.l',
                'cc' => 'testreceiver@emai.l',
                'bcc' => '',
                'date' => '2023-07-31',
                'message_id' => "$i",
                'uid' => $i,
            ];
        }

        $folderMock->method('overview')->willReturn($mailInfos);

        $imapMailbox = new ImapMailbox($mailboxMock, self::createStub(LoggerInterface::class));

        $mailboxServiceMock = $this->createMock(MailboxService::class);
        $mailboxServiceMock->method('createForUserFromDb')->willReturn($imapMailbox);

        $this->container->set('App\Mailbox\MailboxService', $mailboxServiceMock);
    }

    private function makeJsonRequest(string $method = 'GET', string $uri = self::BASE_URL): stdClass
    {
        $this->client->request($method, $uri);
        $content = $this->client->getResponse()->getContent();
        self::assertIsString($content);

        $decoded = json_decode($content);
        self::assertInstanceOf(stdClass::class, $decoded);

        return $decoded;
    }
}
