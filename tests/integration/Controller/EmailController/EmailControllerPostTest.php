<?php

declare(strict_types=1);

namespace App\Tests\Integration\Controller\EmailController;

use App\Converter\EmailEntityConverter;
use App\DataFixtures\EmailSettingsFixtures;
use App\DTO\UserInfo;
use App\Entity\Email;
use App\Entity\EmailSettings;
use App\Entity\SmtpImapConnection;
use App\Enum\EmailStatus;
use App\Security\UserStatus;
use App\SendEmailStateMachine\StatesWithTransitions\Posted;
use App\SendEmailStateMachine\StatesWithTransitions\RetryAt;
use App\SendEmailStateMachine\StatesWithTransitions\SendingFailed;
use App\SendEmailStateMachine\StatesWithTransitions\Sent;
use App\SendEmailStateMachine\StatesWithTransitions\UserTransportFailedOrNull;
use App\Service\EmailTransportService;
use App\Service\UserInfoService;
use App\Service\UserInfoServiceInterface;
use App\Tests\EmailHistoryAssertionsTrait;
use App\Tests\Util\Factory\UserFactory;
use App\Tests\Util\transports\TestTransport;
use App\Util\Result;
use Doctrine\Common\DataFixtures\ReferenceRepository;
use Doctrine\ORM\EntityManager;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use PHPUnit\Framework\Attributes\Test;
use stdClass;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBus;
use Symfony\Component\Messenger\MessageBusInterface;

class EmailControllerPostTest extends WebTestCase
{
    use EmailHistoryAssertionsTrait;

    private const BASE_URL = '/v1/emails';
    private const BASE_URL_SYNCHRONOUS = self::BASE_URL . '/synchronous';

    private ReferenceRepository $fixtures;
    private KernelBrowser $client;
    private EntityManager $em;
    private Container $container;

    public function setUp(): void
    {
        $this->client = static::createClient();
        $this->client->loginUser(UserFactory::createPwUser());

        $this->container = self::getContainer();

        $this->fixtures = $this->container
            ->get(DatabaseToolCollection::class)
            ->get()
            ->loadFixtures([EmailSettingsFixtures::class])
            ->getReferenceRepository();

        $this->em = $this->container->get('doctrine.orm.entity_manager');
        EmailTransportService::emptyTransportCache();
        TestTransport::setTestConnectionResult(null);
    }

    protected function getEntityManager(): EntityManager
    {
        return $this->em;
    }

    #[Test]
    public function it_reports_an_error_if_there_is_no_email(): void
    {
        $payload = <<<EOJSON
            {
                "emails": []
            }
        EOJSON;

        $response = $this->makeJsonRequest('POST', self::BASE_URL, $payload);

        self::assertResponseStatusCodeSame(400);
        self::assertSame('error', $response->status);
        self::assertSame('Keine Emails zum Versenden angegeben', $response->error);
    }

    #[Test]
    public function it_reports_an_error_if_there_is_no_content(): void
    {
        $payload = <<<EOJSON
            {
                "emails": [
                    {
                        "to": [
                            { "address": "<EMAIL>" }
                        ],
                        "subject": "Hello"
                    }
                ]
            }
        EOJSON;

        $response = $this->makeJsonRequest('POST', self::BASE_URL, $payload);

        self::assertResponseStatusCodeSame(200);
        self::assertSame(0, $response->sentEmailCount);
        self::assertSame(1, $response->skippedEmailCount);
        self::assertSame('has no content', $response->skippedEmails[0]->reason);
    }

    #[Test]
    public function it_reports_an_error_if_there_is_no_recipient(): void
    {
        $payload = <<<EOJSON
            {
                "emails": [
                    {
                        "subject": "Hello",
                        "text": "Hi text"
                    }
                ]
            }
        EOJSON;

        $response = $this->makeJsonRequest('POST', self::BASE_URL, $payload);

        self::assertResponseStatusCodeSame(200);
        self::assertSame(0, $response->sentEmailCount);
        self::assertSame(1, $response->skippedEmailCount);
        self::assertSame('has no recipients', $response->skippedEmails[0]->reason);
    }


    #[Test]
    public function it_reports_an_invalid_email_address(): void
    {
        $invalidEmailAddress = 'totest.com';
        $payload = <<<EOJSON
             {
                "emails": [
                    {
                        "to": [
                            { "address": "{$invalidEmailAddress}" }
                        ],
                        "subject": "Hello",
                        "text": "Hi text"
                    }
                ]
            }
        EOJSON;

        $response = $this->makeJsonRequest('POST', self::BASE_URL, $payload);

        self::assertResponseStatusCodeSame(200);
        self::assertSame(0, $response->sentEmailCount);
        self::assertSame(1, $response->skippedEmailCount);
        self::assertSame(
            sprintf('has invalid recipient: %s', $invalidEmailAddress),
            $response->skippedEmails[0]->reason
        );
    }

    #[Test]
    public function it_sends_a_synchronous_email_for_the_current_user(): void
    {
        $body1 = base64_encode('body 1');
        $body2 = base64_encode('body 2');
        $payload = <<<EOJSON
            {
                "email": {
                    "to": [
                        { "address": "<EMAIL>", "name": "To 1" },
                        { "address": "<EMAIL>", "name": "To 2" }
                    ],
                    "cc": [
                        { "address": "<EMAIL>", "name": "Cc 1" },
                        { "address": "<EMAIL>", "name": "Cc 2" }
                    ],
                    "bcc": [
                        { "address": "<EMAIL>", "name": "Bcc 1" },
                        { "address": "<EMAIL>", "name": "Bcc 2" }
                    ],
                    "subject": "Hello",
                    "text": "Hi text!",
                    "html": "Hi HTML!",
                    "attachments": [
                        { "name": "Attachment 1", "body": "$body1" },
                        { "name": "Attachment 2", "body": "$body2", "contentType": "application/json" }
                    ]
                }
            }
        EOJSON;

        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        /** @var SmtpImapConnection $emailSettings */
        $emailSettings = $this->fixtures->getReference('user1_smtp_imap_connection', SmtpImapConnection::class);

        // Check response
        self::assertResponseStatusCodeSame(200);

        // Check sent email
        self::assertEmailCount(1);
        $email = self::getMailerMessage();
        self::assertNotNull($email);
        self::assertEmailHeaderSame($email, 'Subject', EmailEntityConverter::rfc20247Utf8Encode('Hello'));
        self::assertEmailTextBodyContains($email, 'Hi text!');
        self::assertEmailHtmlBodyContains($email, 'Hi HTML!');
        self::assertEmailAddressContains($email, 'From', $emailSettings->getEmail());
        self::assertEmailAddressContains($email, 'To', '<EMAIL>');
        self::assertEmailAddressContains($email, 'To', '<EMAIL>');
        self::assertEmailAddressContains($email, 'Cc', '<EMAIL>');
        self::assertEmailAddressContains($email, 'Cc', '<EMAIL>');
        self::assertEmailAddressContains($email, 'Bcc', '<EMAIL>');
        self::assertEmailAddressContains($email, 'Bcc', '<EMAIL>');
        self::assertEmailAttachmentCount($email, 2);

        // Logs errors correctly:
        $this->assertEmailHistoryErrorForEmailId($response->id, []);
        // Logs state changes correctly:
        $this->assertEmailHistoryTransitionForEmailId(
            $response->id,
            ['queued', Posted::getIdentifier()],
            [Posted::getIdentifier(), Sent::getIdentifier()]
        );
    }

    #[Test]
    public function it_ignores_the_provided_from_address_for_pw_users(): void
    {
        $payload = <<<EOJSON
            {
                "email": {
                    "to": [
                        { "address": "<EMAIL>", "name": "To" }
                    ],
                    "from": { "address": "<EMAIL>", "name": "From" },
                    "subject": "Hello",
                    "text": "Hi text!"
                }
            }
        EOJSON;

        $this->client->loginUser(UserFactory::createPwUser(userId: 1));

        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        // Check response
        self::assertResponseStatusCodeSame(200);

        $emailSettings = $this->em->getRepository(SmtpImapConnection::class)->find(1);

        // Check sent email
        self::assertEmailCount(1);
        $email1 = self::getMailerMessage();
        self::assertNotNull($email1);
        self::assertEmailHeaderSame($email1, 'Subject', 'Hello');
        self::assertEmailTextBodyContains($email1, 'Hi text!');
        self::assertEmailAddressContains($email1, 'From', $emailSettings?->getEmail() ?? '');
        self::assertEmailAddressContains($email1, 'To', '<EMAIL>');

        // Logs errors correctly:
        self::assertEmailHistoryErrorForEmailId($response->id, []);
        // Logs state changes correctly:
        self::assertEmailHistoryTransitionForEmailId(
            $response->id,
            ['queued', Posted::getIdentifier()],
            [Posted::getIdentifier(), Sent::getIdentifier()]
        );
    }

    #[Test]
    public function it_adds_a_reply_to_header(): void
    {
        $payload = <<<EOJSON
            {
                "email": {
                    "to": [
                        { "address": "<EMAIL>", "name": "To" }
                    ],
                    "replyTo": { "address": "<EMAIL>", "name": "ReplyTo" },
                    "subject": "Hello",
                    "text": "Hi text!"
                }
            }
        EOJSON;

        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        /** @var SmtpImapConnection $emailSettings */
        $emailSettings = $this->fixtures->getReference('user1_smtp_imap_connection', SmtpImapConnection::class);
        // Check response
        self::assertResponseStatusCodeSame(200);

        // Check sent email
        self::assertEmailCount(1);
        $email1 = self::getMailerMessage();
        self::assertNotNull($email1);
        self::assertEmailHeaderSame($email1, 'Subject', 'Hello');
        self::assertEmailTextBodyContains($email1, 'Hi text!');
        self::assertEmailAddressContains($email1, 'From', $emailSettings->getEmail());
        self::assertEmailAddressContains($email1, 'To', '<EMAIL>');
        self::assertEmailAddressContains($email1, 'Reply-To', '<EMAIL>');

        // Logs errors correctly:
        self::assertEmailHistoryErrorForEmailId($response->id, []);
        // Logs state changes correctly:
        self::assertEmailHistoryTransitionForEmailId(
            $response->id,
            ['queued', Posted::getIdentifier()],
            [Posted::getIdentifier(), Sent::getIdentifier()]
        );
    }

    #[Test]
    public function it_sends_if_emailsettings_missing_with_failsafe(): void
    {
        // useFailsafeTransport defaults to true, so no need to provide in payload
        $payload = <<<EOJSON
            {
                "email": {
                    "to": [
                        {
                          "address": "<EMAIL>",
                          "name": "Tester 1"
                        }
                    ],
                    "subject": "Subject (Email 1)",
                    "text": "Text (Email 1)",
                    "html": "Html (Email 1)"
                }
            }
        EOJSON;

        // Login as a user without smtp settings
        $this->client->loginUser(UserFactory::createPwUser(userId: 2));
        $this->mockUserInfoService(2);

        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        // Logs state changes correctly:
        $this->assertEmailHistoryTransitionForEmailId(
            $response->id,
            [EmailStatus::QUEUED->value, Posted::getIdentifier()],
            [Posted::getIdentifier(), UserTransportFailedOrNull::getIdentifier()],
            [UserTransportFailedOrNull::getIdentifier(), Sent::getIdentifier()],
        );
    }

    #[Test]
    public function it_does_not_sends_if_emailsettings_missing_when_usefailsafe_false(): void
    {
        $payload = <<<EOJSON
            {
                "useFailsafeTransport": false,
                "email": {
                    "to": [
                        {
                          "address": "<EMAIL>",
                          "name": "Tester 1"
                        }
                    ],
                    "subject": "Subject (Email 1)",
                    "text": "Text (Email 1)",
                    "html": "Html (Email 1)"
                }
            }
        EOJSON;

        // Login as a user without smtp settings
        $this->client->loginUser(UserFactory::createPwUser(userId: 2));
        $this->mockUserInfoService(2);

        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        self::assertSame('sending_failed', $response->status);

        // Logs errors correctly:
        $this->assertEmailHistoryErrorForEmailId($response->id, ['User has no valid transport']);
        // Logs state changes correctly:
        $this->assertEmailHistoryTransitionForEmailId(
            $response->id,
            [EmailStatus::QUEUED->value, Posted::getIdentifier()],
            [Posted::getIdentifier(), UserTransportFailedOrNull::getIdentifier()],
            [UserTransportFailedOrNull::getIdentifier(), SendingFailed::getIdentifier()],
        );
    }


    #[Test]
    public function it_does_not_send_emails_for_users_that_dont_have_the_right_to_send_emails(): void
    {
        $payload = <<<EOJSON
            {
                "emails": [
                    {
                        "from": {
                            "address": "<EMAIL>",
                            "name": "Tester 1"
                        },
                        "to": [
                            {
                              "address": "<EMAIL>",
                              "name": "Tester 1"
                            }
                        ],
                        "subject": "Subject (Email 1)",
                        "text": "Text (Email 1)"
                    }
                ]
            }
        EOJSON;

        // Login as a user without smtp settings
        $this->client->loginUser(UserFactory::createPwUser(userId: 2, rights: []));

        $this->makeJsonRequest('POST', self::BASE_URL, $payload);

        self::assertResponseStatusCodeSame(403);
    }

    #[Test]
    public function it_does_not_send_emails_for_users_that_dont_have_the_right_status_to_send_emails(): void
    {
        $payload = <<<EOJSON
            {
                "emails": [
                    {
                        "from": {
                            "address": "<EMAIL>",
                            "name": "Tester 1"
                        },
                        "to": [
                            {
                              "address": "<EMAIL>",
                              "name": "Tester 1"
                            }
                        ],
                        "subject": "Subject (Email 1)",
                        "text": "Text (Email 1)"
                    }
                ]
            }
        EOJSON;

        // Login as a user without smtp settings
        $this->client->loginUser(UserFactory::createPwUser(userId: 2, status: UserStatus::CANCELLED->value));

        $this->makeJsonRequest('POST', self::BASE_URL, $payload);

        self::assertResponseStatusCodeSame(403);
    }

    #[Test]
    public function it_does_send_emails_for_users_that_have_a_status_that_is_specifically_allowed_to_send_emails(): void
    {
        $payload = <<<EOJSON
            {
                "useFailsafeTransport": true,
                "alsoSendIfUserStatusIsIn": ["passive"],
                "email": {
                    "from": {
                        "address": "<EMAIL>",
                        "name": "Tester 1"
                    },
                    "to": [
                        {
                          "address": "<EMAIL>",
                          "name": "Tester 1"
                        }
                    ],
                    "subject": "Subject (Email 1)",
                    "text": "Text (Email 1)"
                }
            }
        EOJSON;

        // Login as a user without smtp settings
        $this->client->loginUser(UserFactory::createPwUser(status: UserStatus::PASSIVE->value));

        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        self::assertResponseStatusCodeSame(200);

        $emailEntity = $this->em->find(Email::class, $response->id);
        self::assertSame(EmailStatus::SENT->value, $emailEntity?->getStatus()->value);
    }

    #[Test]
    public function it_fails_if_emailsettings_missing_and_usefailsafetransport_false(): void
    {
        $payload = <<<EOJSON
            {
                "useFailsafeTransport": false,
                "email": {
                    "to": [
                        {
                          "address": "<EMAIL>",
                          "name": "Tester 1"
                        }
                    ],
                    "subject": "Subject (Email 1)",
                    "text": "Text (Email 1)",
                    "html": "Html (Email 1)"
                }
            }
        EOJSON;

        // Login as a user without smtp settings
        $this->client->loginUser(UserFactory::createPwUser(userId: 2));
        $this->mockUserInfoService(2);

        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        self::assertResponseStatusCodeSame(200);
        self::assertSame(EmailStatus::SENDING_FAILED->value, $response->status);

        // Logs errors correctly:
        $this->assertEmailHistoryErrorForEmailId($response->id, ['User has no valid transport']);
        // Logs state changes correctly:
        $this->assertEmailHistoryTransitionForEmailId(
            $response->id,
            ['queued', Posted::getIdentifier()],
            [Posted::getIdentifier(), UserTransportFailedOrNull::getIdentifier()],
            [UserTransportFailedOrNull::getIdentifier(), SendingFailed::getIdentifier()]
        );
    }

    #[Test]
    public function it_uses_parent_user_emailsettings_as_fallback(): void
    {
        $payload = <<<EOJSON
            {
                "useFailsafeTransport": false,
                "email": {
                    "to": [
                        {
                          "address": "<EMAIL>",
                          "name": "Tester 1"
                        }
                    ],
                    "subject": "Subject (Email 1)",
                    "text": "Text (Email 1)"
                }
            }
        EOJSON;

        // Login as a user without smtp settings
        $this->client->loginUser(UserFactory::createPwUser(userId: 2));

        $userInfoServiceMock = $this->createMock(UserInfoServiceInterface::class);
        $userInfoServiceMock
            ->expects(self::exactly(2))->method('getUserInfo')
            ->willReturn(new UserInfo(2, [3, 5, 1], '<EMAIL>'));
        $this->container->set(UserInfoServiceInterface::class, $userInfoServiceMock);

        self::assertNull($this->em->find(EmailSettings::class, 2));

        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        self::assertSame('sent', $response->status);
        self::assertSame('<EMAIL>', $response->actuallySentFrom->address);

        $emailEntity = $this->em->find(Email::class, $response->id);
        self::assertSame(EmailStatus::SENT->value, $emailEntity?->getStatus()->value);
    }

    #[Test]
    public function it_uses_the_fallback_transport_if_emailsettings_fail_and_usefailsafetransport_true(): void
    {

        $payload = <<<EOJSON
            {
                "useFailsafeTransport": true,
                "email": {
                    "from": {
                        "address": "<EMAIL>",
                        "name": "Tester 1"
                    },
                    "to": [
                        {
                          "address": "<EMAIL>",
                          "name": "Tester 1"
                        }
                    ],
                    "subject": "Subject (Email 1)",
                    "text": "Text (Email 1)",
                    "html": "Html (Email 1)"
                }
            }
        EOJSON;


        // Login as a user with active smtp settings
        $this->client->loginUser(UserFactory::createPwUser());
        TestTransport::setTestConnectionResult(Result::err('Connection Error'));
        $this->mockUserInfoService();

        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        self::assertSame(EmailStatus::SENT->value, $response->status);

        // Logs errors correctly:
        $this->assertEmailHistoryErrorForEmailId($response->id, ['Connection Error', 'User has no valid transport']);
        // Logs state changes correctly:
        $this->assertEmailHistoryTransitionForEmailId(
            $response->id,
            [EmailStatus::QUEUED->value, Posted::getIdentifier()],
            [Posted::getIdentifier(), UserTransportFailedOrNull::getIdentifier()],
            [UserTransportFailedOrNull::getIdentifier(), Sent::getIdentifier()],
        );
    }

    #[Test]
    public function it_sends_emails_for_system_user(): void
    {
        $payload = <<<EOJSON
            {
                "useFailsafeTransport": true,
                "email": {
                    "from": {
                        "address": "<EMAIL>",
                        "name": "Tester 1"
                    },
                    "to": [
                        {
                          "address": "<EMAIL>",
                          "name": "Tester 1"
                        }
                    ],
                    "subject": "Subject (Email 1)",
                    "text": "Text (Email 1)",
                    "html": "Html (Email 1)"
                }
            }
        EOJSON;

        $this->client->loginUser(UserFactory::createSystemUser());
        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);
        $emailEntity = $this->em->find(Email::class, $response->id);
        self::assertNotNull($emailEntity);
        // currently there is only an integer user id for actual users in the email entity table
        self::assertNull($emailEntity->getUserId());
        self::assertEquals(EmailStatus::SENT->value, $emailEntity->getStatus()->value);
    }

    #[Test]
    public function it_rejects_attachments_that_contain_characters_from_outside_the_base64_alphabet(): void
    {
        $payload = <<<EOJSON
             {
                "email": {
                    "to": [
                        { "address": "<EMAIL>", "name": "To 1" }
                    ],
                    "subject": "Hello",
                    "text": "Hi text!",
                    "attachments": [
                        {
                            "name": "Attachment 2",
                            "body": "Not base64 encoded body 1",
                            "contentType": "application/json"
                        }
                    ]
                }
            }
        EOJSON;

        $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        // Check response
        self::assertResponseStatusCodeSame(422);
    }


    #[Test]
    public function it_handles_inline_images_correctly(): void
    {
        $payload = <<<EOJSON
            {
                "email": {
                    "to": [
                        { "address": "<EMAIL>", "name": "To 1" }
                    ],
                    "subject": "Hello World",
                    "html": "Delayed Content <img src=\"cid:cid1\">",
                    "attachments": [
                        {
                            "name": "Attachment 1",
                            "body": "123",
                            "contentDisposition": "inline",
                            "contentId": "cid1"
                        }
                    ]
                }
            }
        EOJSON;

        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        self::assertResponseStatusCodeSame(200);
        self::assertSame('inline', $response->attachments[0]->contentDisposition);
        self::assertSame('<EMAIL>', $response->attachments[0]->contentId);
    }

    #[Test]
    public function it_retries_emails_timed_out_to_ses(): void
    {
        $messageBusMock = $this->createMock(MessageBus::class);
        $messageBusMock->expects(self::once())
            ->method('dispatch')
            ->willReturn(new Envelope(new stdClass()));
        self::getContainer()->set(MessageBusInterface::class, $messageBusMock);

        TestTransport::setExceptionDuringSending(new \Exception('451 4.4.2 Timeout waiting for data from client'));

        $payload = <<<EOJSON
            {
                "email": {
                    "to": [
                        { "address": "<EMAIL>", "name": "To" }
                    ],
                    "from": { "address": "<EMAIL>", "name": "From" },
                    "subject": "Hello",
                    "text": "Hi text!"
                }
            }
        EOJSON;

        $this->client->loginUser(UserFactory::createSystemUser());
        $response = $this->makeJsonRequest('POST', self::BASE_URL_SYNCHRONOUS, $payload);

        // Check response
        self::assertResponseStatusCodeSame(200);

        // Check sent email
        $emailRepository = $this->em->getRepository(Email::class);
        /** @var Email $emailEntity */
        $emailEntity = $emailRepository->find($response->id);
        self::assertNotNull($emailEntity->getRetryAt());
        self::assertEquals(EmailStatus::QUEUED->value, $emailEntity->getStatus()->value);

        // Logs errors correctly:
        $this->assertEmailHistoryErrorForEmailId(
            $response->id,
            ['451 4.4.2 Timeout waiting for data from client']
        );
        // Logs state changes correctly:
        $this->assertEmailHistoryTransitionForEmailId(
            $response->id,
            [EmailStatus::QUEUED->value, Posted::getIdentifier()],
            [Posted::getIdentifier(), RetryAt::getIdentifier()],
            [RetryAt::getIdentifier(), EmailStatus::QUEUED->value],
        );
        TestTransport::setExceptionDuringSending(null);
    }

    private function makeJsonRequest(string $method, string $url, ?string $payload = null): stdClass
    {
        $this->client->request($method, $url, server: ['CONTENT_TYPE' => 'application/json'], content: $payload);

        $content = $this->client->getResponse()->getContent();
        assert(is_string($content));
        $result = json_decode($content);
        self::assertInstanceOf(stdClass::class, $result);

        return $result;
    }

    public function mockUserInfoService(int $expectedCalls = 1): void
    {
        $userInfoServiceMock = $this->createMock(UserInfoService::class);
        $userInfoServiceMock
            ->expects(self::exactly($expectedCalls))
            ->method('getUserInfo')
            ->willReturn(new UserInfo(42, [], '<EMAIL>'));
        self::getContainer()->set(UserInfoServiceInterface::class, $userInfoServiceMock);
    }
}
