<?php

declare(strict_types=1);

namespace App\Tests\Integration\Controller\EmailController;

use App\DataFixtures\EmailFixtures;
use App\DataFixtures\EmailSettingsFixtures;
use App\Entity\Email;
use App\Enum\EmailStatus;
use App\Tests\Util\Factory\UserFactory;
use Doctrine\ORM\EntityManager;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use stdClass;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class EmailControllerGetTest extends WebTestCase
{
    private const BASE_URL = '/v1/emails';

    private KernelBrowser $client;
    private EntityManager $em;

    public function setUp(): void
    {
        $this->client = static::createClient();
        $this->client->loginUser(UserFactory::createPwUser());

        $container = self::getContainer();

        $container
            ->get(DatabaseToolCollection::class)
            ->get()
            ->loadFixtures([EmailSettingsFixtures::class, EmailFixtures::class]);

        $this->em = $container->get('doctrine.orm.entity_manager');
    }

    #[Test]
    public function it_lists_sent_mails_of_logged_in_user(): void
    {
        // Login as a user without smtp settings
        $this->client->loginUser(UserFactory::createPwUser(userId: 2));

        $result = $this->makeJsonRequest('GET', self::BASE_URL);

        self::assertSame('success', $result->status);
        self::assertSame(9, $result->response->total);
        self::assertCount(9, $result->response->resources);
    }

    #[Test]
    public function it_returns_emails_of_all_pw_users_for_an_admin_system_user(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest('GET', self::BASE_URL . '?resultsPerPage=30');
        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertSame(30, $result->response->total);
    }

    #[Test]
    public function it_doesnt_return_emails_of_pw_users_for_a_non_admin_system_user(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser());

        $result = $this->makeJsonRequest('GET', self::BASE_URL);
        self::assertResponseStatusCodeSame(403);
        self::assertSame('error', $result->status);
        self::assertSame('Fetching emails is only allowed for pw users or admin system users.', $result->error);
    }

    #[Test]
    #[DataProvider('userIdsFromEmailFixturesProvider')]
    public function it_returns_any_single_email_of_any_user_for_an_admin_system_user(int $userId): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));
        $email = $this->em->getRepository(Email::class)->findOneBy(['userId' => $userId]);
        self::assertIsInt($email?->getId());

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "/{$email->getId()}");

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertSame($email->getSubject(), $result->response->subject);
    }

    #[Test]
    #[DataProvider('userIdsFromEmailFixturesProvider')]
    public function it_doesnt_return_any_single_email_of_any_user_for_a_non_admin_system_user(int $userId): void
    {
        $this->client->loginUser(UserFactory::createSystemUser());
        $email = $this->em->getRepository(Email::class)->findOneBy(['userId' => $userId]);
        self::assertIsInt($email?->getId());

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "/{$email->getId()}");

        self::assertResponseStatusCodeSame(403);
        self::assertSame('error', $result->status);
        self::assertSame('Fetching emails is only allowed for pw users or admin system users.', $result->error);
    }

    #[Test]
    public function it_only_returns_resultsPerPage_number_of_emails(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "?resultsPerPage=5");

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertSame(5, $result->response->total);
    }

    #[Test]
    public function it_only_returns_a_page_according_to_startAtId_and_resultsPerPage(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $startAtId = 15;
        $resultsPerPage = 5;

        $result = $this->makeJsonRequest(
            'GET',
            self::BASE_URL . "?startAtId={$startAtId}&resultsPerPage={$resultsPerPage}"
        );

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertSame(5, $result->response->total);
        self::assertSame($startAtId, $result->response->firstEmailId);
        self::assertSame($startAtId - $resultsPerPage + 1, $result->response->lastEmailId);
    }

    #[Test]
    public function it_filters_out_system_mails(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "?type=system");

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertGreaterThan(0, $result->response->total);

        foreach ($result->response->resources as $email) {
            self::assertNull($email->userId);
        }
    }

    #[Test]
    public function it_filters_out_user_mails(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "?type=user");

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertGreaterThan(0, $result->response->total);

        foreach ($result->response->resources as $email) {
            self::assertNotNull($email->userId);
        }
    }

    #[Test]
    public function it_responds_with_404_if_you_try_to_filter_for_system_mails_and_an_userId(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "?type=system&userId=1");

        self::assertResponseStatusCodeSame(404);
        self::assertSame('error', $result->status);
        self::assertSame('It is not possible to query for system user emails and an userId', $result->error);
    }

    #[Test]
    public function it_filters_out_mails_of_a_specific_user(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "?userId=1");

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertGreaterThan(0, $result->response->total);

        foreach ($result->response->resources as $email) {
            self::assertSame(1, $email->userId);
        }
    }

    #[Test]
    public function it_filters_by_fromCreatedAt_excluding(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest(
            'GET',
            self::BASE_URL . "?resultsPerPage=50&fromCreatedAt=2023-02-01 09:30:00"
        );

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertSame(26, $result->response->total);

        foreach ($result->response->resources as $email) {
            self::assertGreaterThan(
                \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', '2023-02-01 09:30:00'),
                \DateTimeImmutable::createFromFormat('Y-m-d H:i:s.u', $email->createdAt->date)
            );
        }
    }

    #[Test]
    public function it_filters_by_untilCreatedAt_excluding(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest(
            'GET',
            self::BASE_URL . "?resultsPerPage=50&untilCreatedAt=2023-04-01 00:00:00"
        );

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertSame(27, $result->response->total);

        foreach ($result->response->resources as $email) {
            self::assertLessThan(
                \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', '2023-03-31 00:00:00'),
                \DateTimeImmutable::createFromFormat('Y-m-d H:i:s.u', $email->createdAt->date)
            );
        }
    }

    #[Test]
    public function it_filters_by_fromSentAt_excluding(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest(
            'GET',
            self::BASE_URL . "?resultsPerPage=50&fromSentAt=2023-02-01 09:30:00"
        );

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertSame(26, $result->response->total);

        foreach ($result->response->resources as $email) {
            self::assertGreaterThan(
                \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', '2023-02-01 09:30:00'),
                \DateTimeImmutable::createFromFormat('Y-m-d H:i:s.u', $email->sentAt->date)
            );
        }
    }

    #[Test]
    #[DataProvider('wronglyFormattedDateTimeProvider')]
    public function it_responds_with_404_if_fromCreatedAt_is_wrongly_formatted(string $wronglyFormattedDateTime): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest(
            'GET',
            self::BASE_URL . "?fromCreatedAt={$wronglyFormattedDateTime}"
        );

        self::assertResponseStatusCodeSame(404);
        self::assertStringContainsString('Invalid query parameter', $result->detail);
    }

    #[Test]
    #[DataProvider('wronglyFormattedDateTimeProvider')]
    public function it_responds_with_404_if_untilCreatedAt_is_wrongly_formatted(string $wronglyFormattedDateTime): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest(
            'GET',
            self::BASE_URL . "?untilCreatedAt={$wronglyFormattedDateTime}"
        );

        self::assertResponseStatusCodeSame(404);
        self::assertStringContainsString('Invalid query parameter', $result->detail);
    }

    #[Test]
    #[DataProvider('wronglyFormattedDateTimeProvider')]
    public function it_responds_with_404_if_fromSentAt_is_wrongly_formatted(string $wronglyFormattedDateTime): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest(
            'GET',
            self::BASE_URL . "?fromSentAt={$wronglyFormattedDateTime}"
        );

        self::assertResponseStatusCodeSame(404);
        self::assertStringContainsString('Invalid query parameter', $result->detail);
    }

    #[Test]
    #[DataProvider('wronglyFormattedDateTimeProvider')]
    public function it_responds_with_404_if_untilSentAt_is_wrongly_formatted(string $wronglyFormattedDateTime): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest(
            'GET',
            self::BASE_URL . "?untilSentAt={$wronglyFormattedDateTime}"
        );

        self::assertResponseStatusCodeSame(404);
        self::assertStringContainsString('Invalid query parameter', $result->detail);
    }

    #[Test]
    public function it_filters_by_to_address(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "?toAddress=<EMAIL>");

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertGreaterThan(0, $result->response->total);

        foreach ($result->response->resources as $email) {
            self::assertSame('<EMAIL>', $email->toAddress[0]->address);
        }
    }

    #[Test]
    public function it_filters_by_from_address(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "?fromAddress=<EMAIL>");

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertGreaterThan(0, $result->response->total);

        foreach ($result->response->resources as $email) {
            self::assertSame('<EMAIL>', $email->fromAddress->address);
        }
    }

    #[Test]
    #[DataProvider('statusProvider')]
    public function it_filters_by_status(string $status): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "?status={$status}");

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertGreaterThan(0, $result->response->total);

        foreach ($result->response->resources as $email) {
            self::assertSame($status, $email->status);
        }
    }

    #[Test]
    public function it_filters_by_subject(): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "?subject=TestUserEmailSubject1");

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertGreaterThan(0, $result->response->total);

        foreach ($result->response->resources as $email) {
            self::assertStringContainsString('TestUserEmailSubject1', $email->subject);
        }
    }

    /**
     * @param array<string> $params
     */
    #[Test]
    #[DataProvider('getFilterParameters')]
    public function it_filters_by_multiple_parameters(array $params, int $expected): void
    {
        $this->client->loginUser(UserFactory::createSystemUser(payload: ['admin' => true]));

        $paramString = implode('&', $params);

        $result = $this->makeJsonRequest('GET', self::BASE_URL . "?{$paramString}");

        self::assertResponseStatusCodeSame(200);
        self::assertSame('success', $result->status);
        self::assertSame($expected, $result->response->total);
    }

    private function makeJsonRequest(string $method, string $url, ?string $payload = null): stdClass
    {
        $this->client->request($method, $url, server: ['CONTENT_TYPE' => 'application/json'], content: $payload);

        $content = $this->client->getResponse()->getContent();
        assert(is_string($content));
        $result = json_decode($content);
        self::assertInstanceOf(stdClass::class, $result);

        return $result;
    }

    /**
     * @return iterable<array{int}>
     */
    public static function userIdsFromEmailFixturesProvider(): iterable
    {
        return [
            [1],
            [2],
            [3],
        ];
    }

    /**
     * @return iterable<array<string>>
     */
    public static function wronglyFormattedDateTimeProvider(): iterable
    {
        return [
            ['2023-1-1 00:00:00'],
            ['23-01-01 00:00:00'],
            ['2023-01-01 0:0:00'],
            ['2023-01-0100:00:00'],
        ];
    }

    /**
     * @return array<int, array{array<int, string>, int}>
     */
    public static function getFilterParameters(): array
    {
        return [
            [['subject=TestUserEmailSubject1', 'toAddress=<EMAIL>'], 3],
            [['subject=TestUserEmailSubject1', 'fromAddress=<EMAIL>'], 1],
            [['subject=TestUserEmailSubject1', 'fromAddress=<EMAIL>', 'status=sent'], 0],
            [['fromAddress=<EMAIL>', 'toAddress=<EMAIL>'], 1],
            [['fromAddress=<EMAIL>', 'userId=1', 'status=sent'], 1],
            [['fromAddress=<EMAIL>', 'userId=1', 'status=delivery_failed_temporarily'], 1],
            [['type=system', 'fromSentAt=2023-09-06+00:00:00'], 4],
            [['type=user', 'fromSentAt=2023-02-01+00:00:00'], 18],
            [['type=user', 'fromSentAt=2023-02-01+00:00:00', 'untilSentAt=2023-02-09+00:00:00'], 8],
        ];
    }

    /**
     * @return array<int, array<int, string>>
     */
    public static function statusProvider(): iterable
    {
        $statusArray = [];

        foreach (EmailStatus::cases() as $status) {
            $statusArray[] = [$status->value];
        }

        return $statusArray;
    }
}
