<?php

declare(strict_types=1);

namespace App\Tests\Integration\Controller;

use App\DataFixtures\EmailSettingsFixtures;
use App\Entity\EmailSettings;
use App\Entity\OAuth2Connection;
use App\Entity\SmtpImapConnection;
use App\Enum\ConnectionStatus;
use App\Enum\EmailTransportScheme;
use App\Mailbox\ImapMailbox;
use App\Mailbox\ImapMailboxFactory;
use App\OAuth\GoogleProvider;
use App\Service\EmailTransportService;
use App\Tests\Util\Factory\UserFactory;
use App\Tests\Util\transports\TestTransport;
use App\Util\Result;
use Doctrine\ORM\EntityManager;
use Google\Client;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Liip\TestFixturesBundle\Services\DatabaseTools\AbstractDatabaseTool;
use stdClass;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\DependencyInjection\ContainerInterface;

class EmailSettingsControllerTest extends WebTestCase
{
    private const BASE_URL = '/v1/users/%s/email-settings';

    private KernelBrowser $client;
    private ContainerInterface $container;
    private AbstractDatabaseTool $databaseTool;
    private EntityManager $em;

    public function setUp(): void
    {
        $this->client = static::createClient();
        $this->client->loginUser(UserFactory::createPwUser());

        $this->container = self::getContainer();

        $googleProviderMock = $this->createMock(GoogleProvider::class);
        $googleProviderMock->method('getCheckedClientForUserId')->willReturn(new Client());
        $this->container->set('App\OAuth\GoogleProvider', $googleProviderMock);


        $this->databaseTool = $this->container->get(DatabaseToolCollection::class)->get();
        $this->databaseTool->loadFixtures([EmailSettingsFixtures::class]);

        $this->em = $this->container->get('doctrine.orm.entity_manager');
    }

    public function tearDown(): void
    {
        parent::tearDown();

        TestTransport::setTestConnectionResult(null);
        EmailTransportService::emptyTransportCache();
    }

    /**
     * @test
     */
    public function it_saves_email_settings_with_smtp_config_to_the_database(): void
    {
        $this->databaseTool->loadFixtures();
        $this->prepareTransportAndMailboxToReturn(Result::ok(''), Result::ok(''));

        $payload = $this->getValidSmtpPayload();

        $this->makeJsonRequest('PUT', sprintf(self::BASE_URL, 'me'), $payload);

        self::assertResponseStatusCodeSame(200);

        $emailSettings = $this->em->getRepository(SmtpImapConnection::class)->find(1);
        self::assertNotNull($emailSettings);
        self::assertSame(1, $emailSettings->getUserId());
        self::assertSame($payload['email'], $emailSettings->getEmail());
        self::assertSame($payload['scheme'], $emailSettings->getTransportScheme()->value);
        self::assertSame($payload['smtpImapConnection']['smtpHost'], $emailSettings->getSmtpHost());
        self::assertSame($payload['smtpImapConnection']['smtpPort'], $emailSettings->getSmtpPort());
        self::assertSame($payload['smtpImapConnection']['username'], $emailSettings->getUsername());
        self::assertSame($payload['smtpImapConnection']['password'], $emailSettings->getPassword());
        self::assertSame($payload['smtpImapConnection']['imapHost'], $emailSettings->getImapHost());
        self::assertSame($payload['smtpImapConnection']['imapPort'], $emailSettings->getImapPort());

        $rawPasswordValue = $this->getRawValueFromDatabase(SmtpImapConnection::class, 'password', 1);
        self::assertNotEquals('SECRET_PASSWORD', $rawPasswordValue, 'Value not encrypted?');
    }

    /**
     * @test
     */
    public function it_saves_email_settings_with_api_config_to_the_database(): void
    {
        $this->databaseTool->loadFixtures();
        $this->prepareTransportAndMailboxToReturn(Result::ok(''), null);

        $payload = $this->getValidApiPayload();

        $this->makeJsonRequest('PUT', sprintf(self::BASE_URL, 'me'), $payload);

        self::assertResponseStatusCodeSame(200);

        $emailSettings = $this->em->getRepository(OAuth2Connection::class)->find(1);
        self::assertNotNull($emailSettings);
        self::assertSame(1, $emailSettings->getUserId());
        self::assertSame($payload['email'], $emailSettings->getEmail());
        self::assertSame($payload['scheme'], $emailSettings->getTransportScheme()->value);
        self::assertSame($payload['oAuthConnection']['provider'], $emailSettings->getProvider());
        self::assertSame($payload['oAuthConnection']['access_token'], $emailSettings->getAccessToken());
        self::assertSame($payload['oAuthConnection']['refresh_token'], $emailSettings->getRefreshToken());
        self::assertSame(
            $payload['oAuthConnection']['expires_at'],
            $emailSettings->getExpiresAt()?->format('Y-m-d H:i:s')
        );
        self::assertSame($payload['oAuthConnection']['data'], $emailSettings->getData());
        self::assertSame($payload['oAuthConnection']['provider_user_id'], $emailSettings->getProviderUserId());
        self::assertSame($payload['oAuthConnection']['provider_nickname'], $emailSettings->getProviderNickname());
        self::assertSame($payload['oAuthConnection']['provider_name'], $emailSettings->getProviderName());
        self::assertSame($payload['oAuthConnection']['provider_email'], $emailSettings->getProviderEmail());
        self::assertSame($payload['oAuthConnection']['provider_avatar'], $emailSettings->getProviderAvatar());

        $rawAccessToken = $this->getRawValueFromDatabase(OAuth2Connection::class, 'access_token', 1);
        self::assertNotEquals('VALID_ACCESS_TOKEN', $rawAccessToken, 'Value not encrypted?');
        $rawRefreshToken = $this->getRawValueFromDatabase(OAuth2Connection::class, 'refresh_token', 1);
        self::assertNotEquals('VALID_REFRESH_TOKEN', $rawRefreshToken, 'Value not encrypted?');
    }

    /**
     * @test
     */
    public function it_updates_email_settings_smtp(): void
    {
        $this->prepareTransportAndMailboxToReturn(Result::ok(''), Result::ok(''));

        $payload = $this->getValidSmtpPayload();

        $this->makeJsonRequest('PUT', sprintf(self::BASE_URL, 'me'), $payload);

        self::assertResponseStatusCodeSame(200);

        $emailSettings = $this->em->getRepository(EmailSettings::class)->find(1);
        self::assertNotNull($emailSettings);
        self::assertInstanceOf(SmtpImapConnection::class, $emailSettings);
        self::assertSame(1, $emailSettings->getUserId());
        self::assertSame($payload['email'], $emailSettings->getEmail());
        self::assertSame($payload['scheme'], $emailSettings->getTransportScheme()->value);
        self::assertSame($payload['smtpImapConnection']['smtpHost'], $emailSettings->getSmtpHost());
        self::assertSame($payload['smtpImapConnection']['smtpPort'], $emailSettings->getSmtpPort());
        self::assertSame($payload['smtpImapConnection']['username'], $emailSettings->getUsername());
        self::assertSame($payload['smtpImapConnection']['password'], $emailSettings->getPassword());
        self::assertSame($payload['smtpImapConnection']['imapHost'], $emailSettings->getImapHost());
        self::assertSame($payload['smtpImapConnection']['imapPort'], $emailSettings->getImapPort());
    }

    /**
     * @test
     */
    public function it_updates_email_settings_oauth(): void
    {
        $this->prepareTransportAndMailboxToReturn(Result::ok(''), null);

        $payload = $this->getValidApiPayload();

        $this->makeJsonRequest('PUT', sprintf(self::BASE_URL, 'me'), $payload);

        self::assertResponseStatusCodeSame(200);

        $emailSettings = $this->em->getRepository(EmailSettings::class)->find(1);
        self::assertNotNull($emailSettings);
        self::assertInstanceOf(OAuth2Connection::class, $emailSettings);
        self::assertSame(1, $emailSettings->getUserId());
        self::assertSame($payload['email'], $emailSettings->getEmail());
        self::assertSame($payload['scheme'], $emailSettings->getTransportScheme()->value);
        self::assertSame($payload['oAuthConnection']['provider'], $emailSettings->getProvider());
        self::assertSame($payload['oAuthConnection']['access_token'], $emailSettings->getAccessToken());
        self::assertSame($payload['oAuthConnection']['refresh_token'], $emailSettings->getRefreshToken());
        self::assertSame(
            $payload['oAuthConnection']['expires_at'],
            $emailSettings->getExpiresAt()?->format('Y-m-d H:i:s')
        );
        self::assertSame($payload['oAuthConnection']['data'], $emailSettings->getData());
        self::assertSame($payload['oAuthConnection']['provider_user_id'], $emailSettings->getProviderUserId());
        self::assertSame($payload['oAuthConnection']['provider_nickname'], $emailSettings->getProviderNickname());
        self::assertSame($payload['oAuthConnection']['provider_name'], $emailSettings->getProviderName());
        self::assertSame($payload['oAuthConnection']['provider_email'], $emailSettings->getProviderEmail());
        self::assertSame($payload['oAuthConnection']['provider_avatar'], $emailSettings->getProviderAvatar());
    }

    /**
     * @test
     */
    public function it_does_throw_an_error_on_invalid_string_parameter_for_user(): void
    {
        $response = $this->makeJsonRequest('PUT', sprintf(self::BASE_URL, 'abc'), $this->getValidSmtpPayload());

        self::assertNotNull($response);
        self::assertResponseStatusCodeSame(400);
        self::assertSame('error', $response->status);
        self::assertSame('User needs to be "me" or an integer', $response->error->validation);
    }

    /**
     * @test
     */
    public function it_only_allows_changes_for_authenticated_user(): void
    {
        $response = $this->makeJsonRequest('PUT', sprintf(self::BASE_URL, 123), $this->getValidSmtpPayload());

        self::assertNotNull($response);
        self::assertResponseStatusCodeSame(403);
        self::assertSame('error', $response->status);
        self::assertSame('Updating email settings for others users is not supported', $response->error->validation);
    }

    /**
     * @test
     * @dataProvider getSmtpImapStatuses
     */
    public function it_returns_email_settings_with_connection_status(
        ConnectionStatus $smtpConnectionStatus,
        ConnectionStatus $imapConnectionStatus,
        int $expectedUserinfoCalls,
    ): void {
        $smtpResult = match ($smtpConnectionStatus) {
            ConnectionStatus::FAILED,
            ConnectionStatus::INACTIVE => Result::err(''),
            ConnectionStatus::ACTIVE => Result::ok(''),
        };
        $imapResult = match ($imapConnectionStatus) {
            ConnectionStatus::FAILED,
            ConnectionStatus::INACTIVE => Result::err(''),
            ConnectionStatus::ACTIVE => Result::ok(''),
        };

        $this->prepareTransportAndMailboxToReturn($smtpResult, $imapResult);

        $response = $this->makeJsonRequest('PUT', sprintf(self::BASE_URL, 'me'), $this->getValidSmtpPayload());

        self::assertNotNull($response);
        self::assertResponseStatusCodeSame(200);
        self::assertSame($smtpConnectionStatus->value, $response->response->sendingStatus);
        self::assertSame($imapConnectionStatus->value, $response->response->receivingStatus);
    }

    /**
     * @test
     * @dataProvider getSmtpImapStatuses
     */
    public function it_persists_email_settings_with_connection_status(
        ConnectionStatus $smtpConnectionStatus,
        ConnectionStatus $imapConnectionStatus,
        int $expectedUserinfoCalls,
    ): void {
        $this->databaseTool->loadFixtures();

        $smtpResult = match ($smtpConnectionStatus) {
            ConnectionStatus::FAILED,
            ConnectionStatus::INACTIVE => Result::err(''),
            ConnectionStatus::ACTIVE => Result::ok(''),
        };
        $imapResult = match ($imapConnectionStatus) {
            ConnectionStatus::FAILED,
            ConnectionStatus::INACTIVE => Result::err(''),
            ConnectionStatus::ACTIVE => Result::ok(''),
        };

        $this->prepareTransportAndMailboxToReturn($smtpResult, $imapResult);

        $payload = $this->getValidSmtpPayload();

        $this->makeJsonRequest('PUT', sprintf(self::BASE_URL, 'me'), $payload);

        self::assertResponseStatusCodeSame(200);

        $repository = $this->em->getRepository(SmtpImapConnection::class);
        $emailSettings = $repository->find(1);

        self::assertNotNull($emailSettings);
        self::assertSame(1, $emailSettings->getUserId());
        self::assertSame($payload['email'], $emailSettings->getEmail());
        self::assertSame($payload['scheme'], $emailSettings->getTransportScheme()->value);
        self::assertSame($smtpConnectionStatus->value, $emailSettings->getSendingStatus()?->value);
        self::assertSame($imapConnectionStatus->value, $emailSettings->getReceivingStatus()?->value);
    }

    /**
     * @test
     */
    public function it_returns_deactivated_if_user_deactivated_email_settings(): void
    {
        $this->prepareTransportAndMailboxToReturn(smtpResult: Result::ok(''), imapResult: Result::err(''));

        $payload = $this->getValidSmtpPayload();
        $payload['sendingStatus'] = ConnectionStatus::INACTIVE;
        $payload['receivingStatus'] = ConnectionStatus::INACTIVE;

        $response = $this->makeJsonRequest('PUT', sprintf(self::BASE_URL, 'me'), $payload);

        self::assertNotNull($response);
        self::assertResponseStatusCodeSame(200);
        self::assertSame('inactive', $response->response->sendingStatus);
        self::assertSame('inactive', $response->response->receivingStatus);
    }

    /**
     * @test
     */
    public function it_only_allows_delete_email_settings_from_authenticated_user(): void
    {
        $response = $this->makeJsonRequest('DELETE', sprintf(self::BASE_URL, 123), []);

        self::assertNotNull($response);
        self::assertResponseStatusCodeSame(403);
        self::assertSame('error', $response->status);
        self::assertSame('Deleting email settings for others users is not supported', $response->error->validation);
    }

    /**
     * @test
     */
    public function it_deletes_email_settings(): void
    {
        $repository = $this->em->getRepository(EmailSettings::class);
        $emailSettings = $repository->find(1);
        self::assertNotNull($emailSettings);

        $this->makeJsonRequest('DELETE', sprintf(self::BASE_URL, 'me'), []);
        $emailSettings = $repository->find(1);
        self::assertNull($emailSettings);
    }

    /**
     * @param mixed[] $payload
     */
    private function makeJsonRequest(string $method, string $url, array $payload): ?stdClass
    {
        $this->client->jsonRequest($method, $url, parameters: $payload);
        $content = $this->client->getResponse()->getContent();
        assert(is_string($content));

        /** @var stdClass|null $result */
        $result = json_decode($content);

        return $result;
    }

    /**
     * @return array{
     *     'email': string,
     *     'scheme': string,
     *     'smtpImapConnection': array{
     *          'smtpHost': string,
     *          'smtpPort': int,
     *          'username': string,
     *          'password': string,
     *          'imapHost': string,
     *          'imapPort': int
     *     }
     * }
     */
    private function getValidSmtpPayload(): array
    {
        return [
            "email" => "<EMAIL>",
            "scheme" => EmailTransportScheme::SMTP->value,
            "sendingStatus" => "active",
            "receivingStatus" => "active",
            'smtpImapConnection' => [
                "smtpHost" => "host",
                "smtpPort" => 123456,
                "username" => "MariaMusterfrau",
                "password" => "SECRET_PASSWORD",
                "imapHost" => "host",
                "imapPort" => 987654,
            ],
        ];
    }

    /**
     * @return array{
     *     'email': string,
     *     'scheme': string,
     *     'oAuthConnection': array{
     *          'provider': string,
     *          'access_token': string,
     *          'refresh_token': string,
     *          'expires_at': string,
     *          'data': string,
     *          'provider_user_id': string,
     *          'provider_nickname': string,
     *          'provider_name': string,
     *          'provider_email': string,
     *          'provider_avatar': string
     *     }
     *     }
     */
    private function getValidApiPayload(): array
    {
        return [
            "email" => "<EMAIL>",
            "scheme" => EmailTransportScheme::API->value,
            "sendingStatus" => "active",
            "receivingStatus" => "active",
            'oAuthConnection' => [
                "provider" => "google",
                "access_token" => "VALID_ACCESS_TOKEN",
                "refresh_token" => "VALID_REFRESH_TOKEN",
                "expires_at" => "2022-09-09 11:22:33",
                "data" => "",
                "provider_user_id" => "2",
                "provider_nickname" => "",
                "provider_name" => "",
                "provider_email" => "",
                "provider_avatar" => "",
            ],
        ];
    }

    private function getRawValueFromDatabase(string $cls, string $field, int $userId): mixed
    {
        return $this
            ->em
            ->createQueryBuilder()
            ->select("t.{$field}")
            ->from($cls, 't')
            ->where('t.userId = :user_id')
            ->setParameter('user_id', $userId)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @return array{array{ConnectionStatus, ConnectionStatus}}
     */
    public static function getSmtpImapStatuses(): array
    {
        return [
            [
                ConnectionStatus::FAILED,
                ConnectionStatus::FAILED,
                1,
            ],
            [
                ConnectionStatus::ACTIVE,
                ConnectionStatus::FAILED,
                0,
            ],
            [
                ConnectionStatus::FAILED,
                ConnectionStatus::ACTIVE,
                1,
            ],
            [
                ConnectionStatus::ACTIVE,
                ConnectionStatus::ACTIVE,
                0,
            ],
        ];
    }

    /**
     * @param Result<string,string> $smtpResult
     * @param Result<string,string>|null $imapResult
     */
    private function prepareTransportAndMailboxToReturn(Result $smtpResult, ?Result $imapResult): void
    {
        /** @phpstan-ignore-next-line */
        TestTransport::setTestConnectionResult($smtpResult);

        if ($imapResult === null) {
            return;
        }

        $mailboxMock = $this->createMock(ImapMailbox::class);
        $mailboxMock->method('testConnection')->willReturn($imapResult);
        $imapMailboxFactoryMock = $this->createMock(ImapMailboxFactory::class);
        $imapMailboxFactoryMock->method('create')->willReturn($mailboxMock);
        $this->container->set('App\Mailbox\ImapMailboxFactory', $imapMailboxFactoryMock);
    }
}
