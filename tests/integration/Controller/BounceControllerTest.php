<?php

declare(strict_types=1);

namespace App\Tests\Integration\Controller;

use App\DataFixtures\EmailFixtures;
use App\Entity\Email;
use App\Entity\EmailHistory;
use App\Entity\EmailHistoryError;
use App\Entity\EmailHistoryTransition;
use App\Enum\EmailStatus;
use App\Error\BounceErrorType;
use App\Error\ComplaintErrorType;
use App\EventListener\EmailListener;
use App\Service\SignatureService;
use App\Util\Result;
use Doctrine\Common\DataFixtures\Purger\ORMPurger;
use Doctrine\ORM\EntityManager;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class BounceControllerTest extends WebTestCase
{
    private const BASE_URL = '/v1/bounces/ses';

    private KernelBrowser $client;
    private EntityManager $em;
    private ContainerInterface $container;

    public function setUp(): void
    {
        $this->client = static::createClient();
        $this->container = self::getContainer();
        $this->em = $this->container->get('doctrine.orm.entity_manager');

        $emailListener = $this->createMock(EmailListener::class);
        $this->container->set(EmailListener::class, $emailListener);

        $this->container
            ->get(DatabaseToolCollection::class)
            ->get()
            ->loadFixtures([EmailFixtures::class]);
    }

    public function tearDown(): void
    {
        $purger = new ORMPurger($this->em);
        $purger->purge();

        parent::tearDown();
    }

    /**
     * @test
     */
    public function it_retuns_400_if_signature_validation_fails(): void
    {
        $signatureServiceMock = $this->createMock(SignatureService::class);
        $signatureServiceMock->method('validate')->willReturn(Result::err(''));
        $this->container->set(SignatureService::class, $signatureServiceMock);

        $payload = <<<EOJSON
            {
              "Type" : "Notification",
              "MessageId" : "",
              "Token" : "",
              "TopicArn" : "",
              "Message" : "",
              "SubscribeURL" : "",
              "Timestamp" : "",
              "SignatureVersion" : "",
              "Signature" : "",
              "SigningCertURL" : ""
            }
            EOJSON;

        $this->client->request(
            'POST',
            self::BASE_URL,
            server: ['CONTENT_TYPE' => 'application/json'],
            content: $payload
        );

        self::assertResponseStatusCodeSame(400);
    }

    /**
     * @test
     * @dataProvider getTestConditionsForStatusChange
     */
    public function it_matches_the_bounce_by_messageId_and_adjusts_status_accordingly(
        string $messageId,
        string $payload,
        EmailStatus $expectedStatus,
        BounceErrorType|ComplaintErrorType $expectedErrorType,
        string $expectedCause,
    ): void {
        $signatureServiceMock = $this->createMock(SignatureService::class);
        $signatureServiceMock->method('validate')->willReturn(Result::ok(''));
        $this->container->set(SignatureService::class, $signatureServiceMock);

        $emailRepo = $this->em->getRepository(Email::class);
        /** @var Email $email */
        $email = $emailRepo->find(1);
        $email->setStatus(EmailStatus::SENT);
        $email->setMessageId($messageId . '@eu-central-1.amazonses.com');
        $this->em->flush();

        $this->client->request(
            'POST',
            self::BASE_URL,
            server: ['CONTENT_TYPE' => 'application/json'],
            content: $payload
        );

        self::assertResponseStatusCodeSame(200);

        /** @var Email $email */
        $email = $emailRepo->find(1);

        self::assertSame($expectedStatus, $email->getStatus());

        $emaiHistoryRepo = $this->em->getRepository(EmailHistory::class);
        $emailHistoryEntries = $emaiHistoryRepo->findBy(['email' => 1]);

        self::assertCount(2, $emailHistoryEntries);

        /** @var EmailHistoryError $bounceError */
        $bounceError = $emailHistoryEntries[0];
        /** @var EmailHistoryTransition $transitionLog */
        $transitionLog = $emailHistoryEntries[1];

        self::assertSame($expectedErrorType->value, $bounceError->getType());
        self::assertSame($expectedCause, $bounceError->getCause());
        self::assertSame($expectedStatus->value, $transitionLog->getToState());
        self::assertSame($expectedStatus->value, $transitionLog->getToState());
    }

    /**
     * @return array<string, array{string,string,EmailStatus,BounceErrorType|ComplaintErrorType,string}>
     */
    public static function getTestConditionsForStatusChange(): array
    {
        $messageId = "1235aneg7348-9875arn5-8ae5-6i6a-a45e-ina84ra0ge8l-000000";

        // @codingStandardsIgnoreStart ignore long lines
        return [
            'bounce' => [
                $messageId,
                <<<EOJSON
                {
                    "Type" : "Notification",
                    "MessageId" : "82e2c308-68fa-5ebd-b0c9-9d32af76f210",
                    "TopicArn" : "arn:aws:sns:eu-central-1:************:mailer",
                    "Message" : "{\"notificationType\":\"Bounce\",\"bounce\":{\"bounceType\":\"Permanent\",\"bouncedRecipients\":[{\"details\":\"test\"}]},\"mail\":{\"messageId\":\"$messageId\"}}",
                    "Timestamp" : "2024-02-05T11:23:56.167Z",
                    "SignatureVersion" : "1",
                    "Signature" : "b5w/TjHt02T31ulTk9V/AJIUWiPOk3MuuoEGDoEkZNf28RflEQaXJoYSzHdMZdES+bNxfXTZT/4p4csmhjXwSVepS3mHKUHfIJb9MlTzd4Z1TDUhUKl0Ju22EXqPAxcOvCO1LPk9dVK4tjLMC7EXi/OiWSaQ+KKdg89YDghCQs4qq1uYLIF4owOk7efhgPeXXBO6cpruPdVIbKDppCg+6HX6C3awJviaU+4yQiSckwmDToEQfvuYGzuJPwl5Gr9ijxpN8+UQ8IxiACodJhkkHUyaFBGE/dGB7SV6errJvzdqWkmmuwlhIengeJWwi06OpiKFLQBi/KR32gJzN7Ht8w==",
                    "SigningCertURL" : "https://sns.eu-central-1.amazonaws.com/SimpleNotificationService-60eadc530605d63b8e62a523676ef735.pem",
                    "UnsubscribeURL" : "https://sns.eu-central-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-central-1:************:mailer:e7c4214b-737f-4983-a282-8ee9e1351f0e"
                }
                EOJSON,
                EmailStatus::DELIVERY_FAILED_PERMANENTLY,
                BounceErrorType::PERMANENT,
                "[{\"details\":\"test\"}]",
            ],

            'complaint with subtype OnAccountSuppressionList' => [
                $messageId,
                <<<EOJSON
                {
                    "Message": "{\"notificationType\":\"Complaint\",\"complaint\":{\"complaintSubType\":\"OnAccountSuppressionList\",\"complainedRecipients\":[{\"emailAddress\":\"<EMAIL>\"}]},\"mail\":{\"messageId\":\"$messageId\"}}",
                    "MessageId": "cc8e82cc-8d78-58e1-8f5e-dd789e9862f0",
                    "Signature": "cOvHUs0fthsQN7zedKNuulOOAoIwY8XGW97fF6U9cWVQkBoj+h3biIBk+h1fDr+WrTqXt4YzGRiFjsNHeQBTA3s/dRg+QUJMUQNsUu6ZNZkUQHOtkiJzURLoa+dBICoRKKPADIvWQZe/rsCkCqJIN9TmTy4WEYlczGvzNm247AWBC3rLV9TbeNfbuh6yFp+aI3uUiPHgPWI8ppCQlMDRuJq6B6SrjZthdTncKK6qv3WSkVJpJrbRitagdxYPddVEfJWj7U9SdqHHHLCbQR7Z7uNpQrKqYyXbIBKJ8l5rz9HPnkwbSnEOtyDkZAVXK+dXSJpSUXlQ8LaDDLvMXIDrtA==",
                    "SignatureVersion": "1",
                    "SigningCertURL": "https://sns.eu-central-1.amazonaws.com/SimpleNotificationService-9c6465fa7f48f5cacd23014631ec1136.pem",
                    "Timestamp": "2024-11-17T15:54:08.976Z",
                    "TopicArn": "arn:aws:sns:eu-central-1:************:mailer",
                    "Type": "Notification",
                    "UnsubscribeURL": "https://sns.eu-central-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-central-1:************:mailer:1a54435a-ca6b-461c-943e-4672f757b390"
                }
                EOJSON,
                EmailStatus::DELIVERY_FAILED_PERMANENTLY,
                ComplaintErrorType::ON_ACCOUNT_SUPPRESSION_LIST,
                "[{\"emailAddress\":\"<EMAIL>\"}]",
            ],

            'complaint with no subtype' => [
                $messageId,
                <<<EOJSON
                {
                    "Message": "{\"notificationType\":\"Complaint\",\"complaint\":{\"complaintSubType\":null,\"complainedRecipients\":[{\"emailAddress\":\"<EMAIL>\"}]},\"mail\":{\"messageId\":\"$messageId\"}}",
                    "MessageId": "cc8e82cc-8d78-58e1-8f5e-dd789e9862f0",
                    "Signature": "cOvHUs0fthsQN7zedKNuulOOAoIwY8XGW97fF6U9cWVQkBoj+h3biIBk+h1fDr+WrTqXt4YzGRiFjsNHeQBTA3s/dRg+QUJMUQNsUu6ZNZkUQHOtkiJzURLoa+dBICoRKKPADIvWQZe/rsCkCqJIN9TmTy4WEYlczGvzNm247AWBC3rLV9TbeNfbuh6yFp+aI3uUiPHgPWI8ppCQlMDRuJq6B6SrjZthdTncKK6qv3WSkVJpJrbRitagdxYPddVEfJWj7U9SdqHHHLCbQR7Z7uNpQrKqYyXbIBKJ8l5rz9HPnkwbSnEOtyDkZAVXK+dXSJpSUXlQ8LaDDLvMXIDrtA==",
                    "SignatureVersion": "1",
                    "SigningCertURL": "https://sns.eu-central-1.amazonaws.com/SimpleNotificationService-9c6465fa7f48f5cacd23014631ec1136.pem",
                    "Timestamp": "2024-11-17T15:54:08.976Z",
                    "TopicArn": "arn:aws:sns:eu-central-1:************:mailer",
                    "Type": "Notification",
                    "UnsubscribeURL": "https://sns.eu-central-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-central-1:************:mailer:1a54435a-ca6b-461c-943e-4672f757b390"
                }
                EOJSON,
                EmailStatus::DELIVERY_FAILED_PERMANENTLY,
                ComplaintErrorType::UNDETERMINED,
                "[{\"emailAddress\":\"<EMAIL>\"}]",
            ],
        ];
        // @codingStandardsIgnoreEnd
    }

    /**
     * @test
     */
    public function it_logs_the_notification_if_it_cannot_be_matched_with_an_email(): void
    {
        $signatureServiceMock = $this->createMock(SignatureService::class);
        $signatureServiceMock->method('validate')->willReturn(Result::ok(''));
        $this->container->set(SignatureService::class, $signatureServiceMock);

        $clientMock = new MockHttpClient();
        $this->container->set(HttpClientInterface::class, $clientMock);

        $messageId = "1235aneg7348-9875arn5-8ae5-6i6a-a45e-ina84ra0ge8l-000000";

        // @codingStandardsIgnoreStart
        $payload = <<<EOJSON
            {
              "Type" : "Notification",
              "MessageId" : "82e2c308-68fa-5ebd-b0c9-9d32af76f210",
              "TopicArn" : "arn:aws:sns:eu-central-1:************:mailer",
              "Message" : "{\"notificationType\":\"Bounce\",\"bounce\":{\"bounceType\":\"Permanent\",\"bouncedRecipients\":[{\"details\":\"test\"}]},\"mail\":{\"messageId\":\"$messageId\"}}",
              "Timestamp" : "2024-02-05T11:23:56.167Z",
              "SignatureVersion" : "1",
              "Signature" : "b5w/TjHt02T31ulTk9V/AJIUWiPOk3MuuoEGDoEkZNf28RflEQaXJoYSzHdMZdES+bNxfXTZT/4p4csmhjXwSVepS3mHKUHfIJb9MlTzd4Z1TDUhUKl0Ju22EXqPAxcOvCO1LPk9dVK4tjLMC7EXi/OiWSaQ+KKdg89YDghCQs4qq1uYLIF4owOk7efhgPeXXBO6cpruPdVIbKDppCg+6HX6C3awJviaU+4yQiSckwmDToEQfvuYGzuJPwl5Gr9ijxpN8+UQ8IxiACodJhkkHUyaFBGE/dGB7SV6errJvzdqWkmmuwlhIengeJWwi06OpiKFLQBi/KR32gJzN7Ht8w==",
              "SigningCertURL" : "https://sns.eu-central-1.amazonaws.com/SimpleNotificationService-60eadc530605d63b8e62a523676ef735.pem",
              "UnsubscribeURL" : "https://sns.eu-central-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-central-1:************:mailer:e7c4214b-737f-4983-a282-8ee9e1351f0e"
            }
            EOJSON;
        // @codingStandardsIgnoreEnd

        // there is no email, this message id was assigned to

        $this->client->request(
            'POST',
            self::BASE_URL,
            server: ['CONTENT_TYPE' => 'application/json'],
            content: $payload
        );

        self::assertResponseStatusCodeSame(200);

        $emailHistoryRepo = $this->em->getRepository(EmailHistory::class);
        $emailHistoryError = $emailHistoryRepo->findBy(['email' => null]);

        self::assertCount(1, $emailHistoryError);

        /** @var EmailHistoryError $bounceError */
        $bounceError = $emailHistoryError[0];

        self::assertSame(BounceErrorType::PERMANENT->value, $bounceError->getType());
        self::assertSame("[{\"details\":\"test\"}]", $bounceError->getCause());
    }
}
