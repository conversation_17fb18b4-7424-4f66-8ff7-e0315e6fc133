<?php

declare(strict_types=1);

namespace App\Tests\Integration\EventSubscriber;

use App\EventSubscriber\ErrorSubscriber;
use App\Util\EmailHistoryLogger;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Console\Event\ConsoleErrorEvent;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\KernelInterface;

class ErrorSubscriberTest extends KernelTestCase
{
    private EventDispatcherInterface $dispatcher;

    public function setUp(): void
    {
        $this->dispatcher = self::getContainer()->get(EventDispatcherInterface::class);
    }

    #[Test]
    public function it_handles_kernel_exception(): void
    {
        $loggerMock = $this->createMock(EmailHistoryLogger::class);
        $loggerMock->expects(self::exactly(1))->method('unknownError');
        $this->dispatcher->addSubscriber(new ErrorSubscriber($loggerMock));

        $kernel = $this->createMock(KernelInterface::class);
        $request = $this->createMock(Request::class);
        $event = new ExceptionEvent($kernel, $request, 1, new \Exception());
        $this->dispatcher->dispatch($event);
    }

    #[Test]
    public function it_handles_command_error(): void
    {
        $loggerMock = $this->createMock(EmailHistoryLogger::class);
        $loggerMock->expects(self::exactly(1))->method('unknownError');
        $this->dispatcher->addSubscriber(new ErrorSubscriber($loggerMock));

        $inputMock = $this->createMock(InputInterface::class);
        $outputMock = $this->createMock(OutputInterface::class);
        $error = new \Exception();
        $errorEvent = new ConsoleErrorEvent($inputMock, $outputMock, $error);
        $this->dispatcher->dispatch($errorEvent);
    }
}
