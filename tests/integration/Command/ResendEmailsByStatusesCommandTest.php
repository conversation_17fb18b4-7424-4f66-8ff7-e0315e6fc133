<?php

declare(strict_types=1);

namespace App\Tests\Integration\Command;

use App\Entity\Email;
use App\Entity\EmailHistoryTransition;
use App\Enum\EmailStatus;
use App\EventListener\EmailListener;
use App\SendEmailStateMachine\StatesWithTransitions\Posted;
use Doctrine\ORM\EntityManagerInterface;
use Liip\TestFixturesBundle\Services\DatabaseToolCollection;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Console\Tester\CommandTester;

class ResendEmailsByStatusesCommandTest extends KernelTestCase
{
    private EntityManagerInterface $em;

    public function setUp(): void
    {
        $container = self::getContainer();
        $this->em = $container->get('doctrine.orm.entity_manager');

        $emailListener = $this->createMock(EmailListener::class);
        $container->set(EmailListener::class, $emailListener);

        $container
            ->get(DatabaseToolCollection::class)
            ->get()
            ->loadFixtures([]);
    }

    /**
     * @test
     * @dataProvider getTestConditionsForEmailRetry
     */
    public function it_queues_emails_again_if_not_older_than_24hrs_and_delay_time_of_current_retry_level_expired_yet(
        int $retriesSoFar,
        int $hoursAgoCreated,
        bool $shallResend,
        EmailStatus $emailStatus
    ): void {
        $arrayWithIds = $this->buildTestData($retriesSoFar, $hoursAgoCreated, $shallResend, $emailStatus);

        /** @var \Symfony\Component\HttpKernel\KernelInterface $kernel */
        $kernel = self::$kernel;
        $application = new Application($kernel);

        $command = $application->find('app:resend-email:statuses');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'emailStatuses' => ['delivery_failed_temporarily'],
        ]);

        $commandTester->assertCommandIsSuccessful();

        // the output of the command in the console
        $output = $commandTester->getDisplay();
        $needle = 'Emails Queued: [' . implode(',', $arrayWithIds) . ']';
        self::assertStringContainsString($needle, $output);
    }

    /**
     * @return array<int>|array<null>
     */
    private function buildTestData(
        int $retriesSoFar,
        int $hoursAgoCreated,
        bool $shallResend,
        EmailStatus $emailStatus
    ): array {
        $now = new \DateTimeImmutable();

        $email1 = new Email(null);
        $email1->setSubject('subject');
        $email1->setStatus($emailStatus);
        $this->em->persist($email1);
        $email1->setCreatedAt($now->modify("-$hoursAgoCreated hours"));

        $originalSentAt = $now->modify("-$hoursAgoCreated hours");
        $delayInMinutes = pow(2, $retriesSoFar) * 10;
        $email1->setSentAt($originalSentAt->modify("+$delayInMinutes min"));

        for ($i = 0; $i <= $retriesSoFar; $i++) {
            $transition = new EmailHistoryTransition('unknown', '', Posted::getIdentifier());
            $this->em->persist($transition);
            $email1->addHistory($transition);
        }

        $email2 = new Email(null);
        $email2->setSubject('subject');
        $email2->setStatus($emailStatus);
        $this->em->persist($email2);
        $email2->setCreatedAt($now->modify("-$hoursAgoCreated hours"));

        $originalSentAt = $now->modify("-$hoursAgoCreated hours");
        $delayInMinutes = pow(2, $retriesSoFar) * 10;
        $email2->setSentAt($originalSentAt->modify("+$delayInMinutes min"));

        for ($i = 0; $i <= $retriesSoFar; $i++) {
            $transition = new EmailHistoryTransition('unknown', '', Posted::getIdentifier());
            $this->em->persist($transition);
            $email2->addHistory($transition);
        }

        $this->em->flush();

        return $shallResend ? [$email1->getId(), $email2->getId()] : [];
    }

    /**
     * The delay for the retry levels is calculated as (5^retriesSoFar)*10
     * The delay is measured from email.created_at
     *
     * Example: If there has been 2 retries yet and the email was created an hour ago, the delay of 250 minutes is not
     * expired yet and the email shall not be queued again yet.
     *
     * No email older than 24hrs shall be queued again. Also only emails with status delivery_failed_temporarily shall
     * be resent currently.
     *
     * @return array<string, array{int,int,bool,EmailStatus}>
     */
    public static function getTestConditionsForEmailRetry(): array
    {
        return [
            "0 retries, 1h ago, delivery_failed_temporarily"
            => [0, 1, true, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "1 retry, 1h ago, delivery_failed_temporarily"
            => [1, 1, true, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "2 retries, 1h ago, delivery_failed_temporarily"
            => [2, 1, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "3 retries, 1h ago, delivery_failed_temporarily"
            => [3, 1, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "4 retries, 1h ago, delivery_failed_temporarily"
            => [4, 1, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "0 retries, 2hrs ago, delivery_failed_temporarily"
            => [0, 2, true, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "1 retry, 2hrs ago, delivery_failed_temporarily"
            => [1, 2, true, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "2 retries, 2hrs ago, delivery_failed_temporarily"
            => [2, 2, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "3 retries, 2hrs ago, delivery_failed_temporarily"
            => [3, 2, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "4 retries, 2hrs ago, delivery_failed_temporarily"
            => [4, 2, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "0 retries, 5hrs ago, delivery_failed_temporarily"
            => [0, 5, true, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "1 retry, 5hrs ago, delivery_failed_temporarily"
            => [1, 5, true, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "2 retries, 5hrs ago, delivery_failed_temporarily"
            => [2, 5, true, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "3 retries, 5hrs ago, delivery_failed_temporarily"
            => [3, 5, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "4 retries, 5hrs ago, delivery_failed_temporarily"
            => [4, 5, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "0 retries, 23hrs ago, delivery_failed_temporarily"
            => [0, 23, true, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "1 retry, 23hrs ago, delivery_failed_temporarily"
            => [1, 23, true, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "2 retries, 23hrs ago, delivery_failed_temporarily"
            => [2, 23, true, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "3 retries, 23hrs ago, delivery_failed_temporarily"
            => [3, 23, true, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "4 retries, 23hrs ago, delivery_failed_temporarily"
            => [4, 23, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "0 retries, 24hrs ago, delivery_failed_temporarily"
            => [0, 24, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "1 retry, 24hrs ago, delivery_failed_temporarily"
            => [1, 24, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "2 retries, 24hrs ago, delivery_failed_temporarily"
            => [2, 24, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "3 retries, 24hrs ago, delivery_failed_temporarily"
            => [3, 24, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "4 retries, 24hrs ago, delivery_failed_temporarily"
            => [4, 24, false, EmailStatus::DELIVERY_FAILED_TEMPORARILY],
            "0 retries, 1h ago, delivery_failed_permanently"
            => [0, 1, false, EmailStatus::DELIVERY_FAILED_PERMANENTLY],
            "0 retries, 1h ago, sent" => [0, 1, false, EmailStatus::SENT],
            "0 retries, 1h ago, initialized" => [0, 1, false, EmailStatus::INITIALIZED],
            "0 retries, 1h ago, queued" => [0, 1, false, EmailStatus::QUEUED],
            "0 retries, 1h ago, sending" => [0, 1, false, EmailStatus::SENDING],
            "0 retries, 1h ago, sending_failed" => [0, 1, false, EmailStatus::SENDING_FAILED],
            "0 retries, 1h ago, wont_send" => [0, 1, false, EmailStatus::WONT_SEND],
        ];
    }
}
