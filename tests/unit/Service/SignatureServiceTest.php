<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service;

use App\Enum\HashAlgorithm;
use App\Service\SignatureService;
use App\Service\SignedMessageInterface;
use App\Util\Result;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class SignatureServiceTest extends KernelTestCase
{
    /**
     * @param Result<null,null> $result
     *
     * @test
     * @dataProvider getKeysAndResults
     */
    public function it_validates_the_signature_and_returns_result_accordingly(string $privateKey, Result $result): void
    {
        $certificate = <<<X509
            -----BEGIN CERTIFICATE-----
            MIICzjCCAjegAwIBAgIUbMtEdlFxfZkUD6/MyTHg1sXd7ckwDQYJKoZIhvcNAQEL
            BQAweTELMAkGA1UEBhMCREUxEDAOBgNVBAgMB0dlcm1hbnkxEDAOBgNVBAcMB0hh
            bWJ1cmcxFTATBgNVBAoMDERlbXYgU3lzdGVtczESMBAGA1UEAwwJVW5pdC1UZXN0
            MRswGQYJKoZIhvcNAQkBFgx0ZXN0QHRlc3QuZGUwHhcNMjQwMjA3MTI0NjQxWhcN
            MjUwMjA2MTI0NjQxWjB5MQswCQYDVQQGEwJERTEQMA4GA1UECAwHR2VybWFueTEQ
            MA4GA1UEBwwHSGFtYnVyZzEVMBMGA1UECgwMRGVtdiBTeXN0ZW1zMRIwEAYDVQQD
            DAlVbml0LVRlc3QxGzAZBgkqhkiG9w0BCQEWDHRlc3RAdGVzdC5kZTCBnzANBgkq
            hkiG9w0BAQEFAAOBjQAwgYkCgYEA4Mg4AumsJxx6xpR+fMMc1tl+qqIxAXKsevNV
            yzX6L4rqp26QJgYYakCPB4ECWOSLGasuWIGEHv99qI/62Ml5lN2JWy6EcEwtcI22
            4nX+Ei1lTR+Jg2qcTU8Pal5kewDBga5F7n5B2sgBmOePVszyrmpMMtx8rSTxSWMG
            LIr9rqUCAwEAAaNTMFEwHQYDVR0OBBYEFAAOrrKCsnsDxGDPjMt0q083eWFzMB8G
            A1UdIwQYMBaAFAAOrrKCsnsDxGDPjMt0q083eWFzMA8GA1UdEwEB/wQFMAMBAf8w
            DQYJKoZIhvcNAQELBQADgYEAJ14UYvInRTeXJXXtHMBuI6BT/6EOMCDpJdrhxIHo
            jQikkDMV7zvy0TMiNZkRdbgcFo5vrudMeW7yEwkgoCMb1xHqY15ZT8gZOG75THi2
            +Trxp9Dh17cmxTNhYDGsfsoJqrdJ+8/kBvEwnUJ+dnryztKTPVh/dsX8w+bJt29f
            hbQ=
            -----END CERTIFICATE-----
            X509;

        $container = self::getContainer();
        $httpClientMock = new MockHttpClient(new MockResponse($certificate));
        $container->set(HttpClientInterface::class, $httpClientMock);

        $signedFormat = 'TestStringToBeSigned';
        $signature = '';
        $privateKey = openssl_pkey_get_private($privateKey);

        if ($privateKey !== false) {
            openssl_sign($signedFormat, $signature, $privateKey, HashAlgorithm::SHA1->name);
        }

        $signedMessage = $this->createMock(SignedMessageInterface::class);
        $signedMessage->method('getIdentifier')->willReturn('SignedTestMessage');
        $signedMessage->method('getSignFormat')->willReturn($signedFormat);
        $signedMessage->method('getSignature')->willReturn(base64_encode($signature));
        $signedMessage->method('getCertificateURL')->willReturn(
            'https://sns.eu-central-1.amazonaws.com/SimpleNotificationService-udonsußagenßg.pem'
        );
        $signedMessage->method('getHashAlgorithm')->willReturn(HashAlgorithm::SHA1);

        /** @var SignatureService $signatureService */
        $signatureService = $container->get(SignatureService::class);
        $result = $signatureService->validate($signedMessage);

        self::assertSame($result->state, $result->state);
    }

    /**
     * @return array<array{privateKey:string, result:Result<null,null>}>
     */
    public static function getKeysAndResults(): array
    {
        return [
            [
                'privateKey' => <<<PK
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                    PK,
                'result' => Result::ok(null),
            ],
            [
                'privateKey' => <<<PK
                    -----BEGIN PRIVATE KEY-----
                    -----END PRIVATE KEY-----
                    PK,
                'result' => Result::err(null),
            ],
        ];
    }
}
