<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service;

use App\Enum\EmailStatus;
use App\Security\UserStatus;
use App\Service\EmailPermissionService;
use App\Tests\Util\Factory\UserFactory;
use App\Util\EmailHistoryLogger;
use Generator;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\TestCase;
use Symfony\Bundle\SecurityBundle\Security;

class EmailPermissionServiceTest extends TestCase
{
    /**
     * @param UserStatus[] $alsoSendIfUserStatusIsIn
     */
    #[Test]
    #[DataProvider('emailPermissionProvider')]
    public function testEmailPermission(
        bool $canSendMail,
        UserStatus $userStatus,
        array $alsoSendIfUserStatusIsIn,
        EmailStatus $expectedEmailState
    ): void {
        $security = $this->createMock(Security::class);
        $security->method('isGranted')->willReturn($canSendMail);
        $emalPermissionService = new EmailPermissionService($security, $this->createMock(EmailHistoryLogger::class));

        $user = UserFactory::createPwUser(status: $userStatus->value);

        $hasPermission = $emalPermissionService->hasPermission($user, $alsoSendIfUserStatusIsIn);

        self::assertSame($expectedEmailState->value === 'created', $hasPermission);
    }

    /**
     * @return Generator<string, array<mixed>>
     */
    public static function emailPermissionProvider(): Generator
    {
        yield 'User can send Mail and is active' => [
            'canSendMail' => true,
            'userStatus' => UserStatus::ACTIVE,
            'alsoSendIfUserStatusIsIn' => [],
            'expectedEmailState' => EmailStatus::CREATED,
        ];

        yield 'User can not send Mail and is active, no additional status is added' => [
            'canSendMail' => false,
            'userStatus' => UserStatus::ACTIVE,
            'alsoSendIfUserStatusIsIn' => [],
            'expectedEmailState' => EmailStatus::WONT_SEND,
        ];

        yield 'User can send Mail and is passive, no additional status is added' => [
            'canSendMail' => true,
            'userStatus' => UserStatus::PASSIVE,
            'alsoSendIfUserStatusIsIn' => [],
            'expectedEmailState' => EmailStatus::CREATED,
        ];

        yield 'User can not send Mail and is passive, no additional status is added' => [
            'canSendMail' => false,
            'userStatus' => UserStatus::PASSIVE,
            'alsoSendIfUserStatusIsIn' => [],
            'expectedEmailState' => EmailStatus::WONT_SEND,
        ];

        yield 'User can send Mail and is passive, "passive" is given as allowed status' => [
            'canSendMail' => true,
            'userStatus' => UserStatus::PASSIVE,
            'alsoSendIfUserStatusIsIn' => [UserStatus::PASSIVE],
            'expectedEmailState' => EmailStatus::CREATED,
        ];

        yield 'User can not send Mail and is passive, "passive" is given as allowed status' => [
            'canSendMail' => false,
            'userStatus' => UserStatus::PASSIVE,
            'alsoSendIfUserStatusIsIn' => [UserStatus::PASSIVE],
            'expectedEmailState' => EmailStatus::WONT_SEND,
        ];

        yield 'User can send Mail and is passive, "interested" is given as allowed status' => [
            'canSendMail' => true,
            'userStatus' => UserStatus::PASSIVE,
            'alsoSendIfUserStatusIsIn' => [UserStatus::INTERESTED],
            'expectedEmailState' => EmailStatus::CREATED,
        ];

        yield 'User can not send Mail and is passive, "interested" is given as allowed status' => [
            'canSendMail' => false,
            'userStatus' => UserStatus::PASSIVE,
            'alsoSendIfUserStatusIsIn' => [UserStatus::INTERESTED],
            'expectedEmailState' => EmailStatus::WONT_SEND,
        ];
    }
}
