<?php

declare(strict_types=1);

namespace App\Tests\Unit\SendEmailStateMachine;

use App\Security\UserInterface;
use App\SendEmailStateMachine\SendEmailContext;
use App\SendEmailStateMachine\StatesWithTransitions\AbstractSendEmailState;
use App\SendEmailStateMachine\StatesWithTransitions\AttachmentTooLarge;
use App\SendEmailStateMachine\StatesWithTransitions\Posted;
use App\SendEmailStateMachine\StatesWithTransitions\RetryAt;
use App\SendEmailStateMachine\StatesWithTransitions\Sent;
use App\SendEmailStateMachine\StatesWithTransitions\UserTransportFailedOrNull;
use App\Service\UserInfoService;
use App\Tests\Util\Factory\MimeEmailFactory;
use App\Tests\Util\Factory\SendEmailContextFactory;
use App\Transport\ParentFallbackTransportFactory;
use App\Transport\TransportInterface;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\Exception\TransportException;

class PostedTest extends TestCase
{
    /**
     * @test
     */
    public function it_sends_with_user_transport(): void
    {
        $context = $this->createMock(SendEmailContext::class);
        $context->method('getUserTransport')->willReturn(self::createStub(TransportInterface::class));
        $context->expects(self::once())->method('getUserTransport');
        $context->expects(self::never())->method('getFallbackTransport');
        $state = new Posted(
            $context,
            self::createStub(LoggerInterface::class),
            self::createStub(UserInfoService::class),
            self::createStub(ParentFallbackTransportFactory::class),
        );
        $email = MimeEmailFactory::create();

        $state->tryToSend($email);
    }

    /**
     * @test
     */
    public function it_returns_sent_if_everything_went_fine(): void
    {
        $context = SendEmailContextFactory::create();
        $state = new Posted(
            $context,
            self::createStub(LoggerInterface::class),
            self::createStub(UserInfoService::class),
            self::createStub(ParentFallbackTransportFactory::class)
        );
        $email = MimeEmailFactory::create();

        $result = $state->tryToSend($email);

        self::assertInstanceOf(Sent::class, $result);
    }

    /**
     * @param string $errMsg ,
     * @param class-string<AbstractSendEmailState> $expectedState
     *
     * @test
     * @dataProvider getErrorMatrix
     */
    public function it_handles_all_known_errors(string $errMsg, string $expectedState): void
    {
        $transport = self::createStub(TransportInterface::class);
        $user = self::createStub(UserInterface::class);
        $transport->method('send')->willThrowException(new TransportException($errMsg));
        $context = SendEmailContextFactory::create(
            user: $user,
            userTransport: $transport,
        );
        $state = new Posted(
            $context,
            self::createStub(LoggerInterface::class),
            self::createStub(UserInfoService::class),
            self::createStub(ParentFallbackTransportFactory::class)
        );

        $result = $state->tryToSend($context->originalEmail);

        self::assertInstanceOf($expectedState, $result);
    }

    /**
     * Overview of the created error matrix
     *
     * @codingStandardsIgnoreStart
     * | errorMessage                      | expectedState             |
     * | ----------------------------------|---------------------------|
     * | 500 Message size limit exceeded   | AttachmentTooLarge        |
     * | 552 Mail size limit exceeded      | AttachmentTooLarge        |
     * | Sending of SPAM is not permitted  | UserTransportFailedOrNull |
     * | Unknown error                     | UserTransportFailedOrNull |
     * | 451 4.4.2 Timeout waiting for ... | RetryAt                   |
     * @codingStandardsIgnoreEnd
     *
     * @return iterable<array{string, class-string<AbstractSendEmailState>}>
     */
    public static function getErrorMatrix(): iterable
    {
        foreach (self::getAllExceptionsWithFollowingStates() as ['errMsg' => $errMsg, 'state' => $state]) {
            yield [$errMsg, $state];
        }
    }

    /**
     * @return array<array{'errMsg': string, 'state': class-string<AbstractSendEmailState>}>
     */
    public static function getAllExceptionsWithFollowingStates(): array
    {
        return [
            [
                'errMsg' => 'An error message containing this string: 500 Message size limit exceeded',
                'state' => AttachmentTooLarge::class,
            ],
            [
                'errMsg' => 'An error message containing this string: 552 Mail size limit exceeded',
                'state' => AttachmentTooLarge::class,
            ],
            [
                'errMsg' => 'Refused by local policy. Sending of SPAM is not permitted!',
                'state' => UserTransportFailedOrNull::class,
            ],
            [
                'errMsg' => 'Unknown error',
                'state' => UserTransportFailedOrNull::class,
            ],
            [
                'errMsg' => '451 4.4.2 Timeout waiting for data from client',
                'state' => RetryAt::class,
            ],
        ];
    }
}
