<?php

declare(strict_types=1);

namespace App\Tests\Unit\SendEmailStateMachine;

use App\SendEmailStateMachine\SendEmailContext;
use App\SendEmailStateMachine\StatesWithTransitions\AttachmentTooLarge;
use App\SendEmailStateMachine\StatesWithTransitions\SendingFailed;
use App\SendEmailStateMachine\StatesWithTransitions\Sent;
use App\SendEmailStateMachine\StatesWithTransitions\ZippedAttachmentTooLarge;
use App\Tests\Util\Factory\MimeEmailFactory;
use App\Tests\Util\Factory\SendEmailContextFactory;
use App\Transport\TransportInterface;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Mailer\Exception\TransportException;
use Symfony\Component\Mime\Email;

class AttachmentTooLargeTest extends TestCase
{
    /**
     * @test
     */
    public function it_uses_fallback_transport_and_sets_fallback_recipient(): void
    {
        $originalEmail = MimeEmailFactory::create();
        $contextConfig = SendEmailContextFactory::getConstructorArgsAsArray($originalEmail);
        $context = $this->getMockBuilder(SendEmailContext::class)->setConstructorArgs($contextConfig)->getMock();
        $context->expects(self::once())->method('getFallbackTransport');
        $context->expects(self::never())->method('getUserTransport');
        $state = new AttachmentTooLarge($context);

        $state->tryToSend($originalEmail);
        $prepareEmailClosure = function (Email $email) {
            /** @phpstan-ignore-next-line */
            return $this->prepare($email);
        };
        $preparedEmail = $prepareEmailClosure->call($state, $originalEmail);

        self::assertSame($context->fallbackRecipient, $preparedEmail->getTo()[0]);
    }

    /**
     * @test
     */
    public function it_zips_before_sending(): void
    {
        $email = MimeEmailFactory::create(attachments: 2);
        $context = SendEmailContextFactory::create(email: $email);
        $state = new AttachmentTooLarge($context);

        $prepareEmailClosure = function (Email $email) {
            /** @phpstan-ignore-next-line */
            return $this->prepare($email);
        };
        $preparedEmail = $prepareEmailClosure->call($state, $context->originalEmail);

        $attachments = $preparedEmail->getAttachments();
        self::assertCount(1, $attachments);
        self::assertSame('zip', $attachments[0]->getMediaSubtype());
    }

    /**
     * @test
     * @dataProvider getAttachmentTooLargeIndicators
     */
    public function it_returns_zippedAttachmentTooLarge_if_transportException_was_thrown(string $indicator): void
    {
        $originalEmail = MimeEmailFactory::create(attachments: 1);
        $fallbackTransport = $this->createMock(TransportInterface::class);
        $fallbackTransport->method('send')->willThrowException(new TransportException($indicator));
        $context = SendEmailContextFactory::create(email: $originalEmail, fallbackTransport: $fallbackTransport);
        $state = new AttachmentTooLarge($context);

        $result = $state->tryToSend($originalEmail);

        self::assertInstanceOf(ZippedAttachmentTooLarge::class, $result);
    }

    /**
     * @test
     */
    public function it_returns_sendingFailed_if_error_indicating_spam_was_thrown(): void
    {
        $originalEmail = MimeEmailFactory::create(attachments: 1);
        $fallbackTransport = $this->createMock(TransportInterface::class);
        $stratoSpamError = 'Refused by local policy. Sending of SPAM is not permitted!';
        $fallbackTransport->method('send')->willThrowException(new TransportException($stratoSpamError));
        $context = SendEmailContextFactory::create(email: $originalEmail, fallbackTransport: $fallbackTransport);
        $state = new AttachmentTooLarge($context);

        $result = $state->tryToSend($originalEmail);

        self::assertInstanceOf(SendingFailed::class, $result);
    }

    /**
     * @test
     */
    public function it_returns_sent_if_everything_went_fine(): void
    {
        $originalEmail = $this->createMock(Email::class);
        $context = $this->createMock(SendEmailContext::class);
        $state = $this->getMockBuilder(AttachmentTooLarge::class)
            ->setConstructorArgs([$context])
            ->onlyMethods(['prepare'])
            ->getMock();
        $state->method('prepare')->willReturn($originalEmail);

        $result = $state->tryToSend($originalEmail);

        self::assertInstanceOf(Sent::class, $result);
    }

    /**
     * @return array<array<string>>
     */
    public static function getAttachmentTooLargeIndicators(): array
    {
        return [
            ['Does error message contain this string: 500 Message size limit exceeded'],
            ['Does error message contain this string: 552 Mail size limit exceeded'],
        ];
    }
}
