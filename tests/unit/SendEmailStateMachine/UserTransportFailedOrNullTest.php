<?php

declare(strict_types=1);

namespace App\Tests\Unit\SendEmailStateMachine;

use App\Entity\EmailSettings;
use App\SendEmailStateMachine\SendEmailContext;
use App\SendEmailStateMachine\StatesWithTransitions\AbstractSendEmailState;
use App\SendEmailStateMachine\StatesWithTransitions\FallbackTransportFailed;
use App\SendEmailStateMachine\StatesWithTransitions\SendingFailed;
use App\SendEmailStateMachine\StatesWithTransitions\Sent;
use App\SendEmailStateMachine\StatesWithTransitions\UserTransportFailedOrNull;
use App\Tests\Util\Factory\MimeEmailFactory;
use App\Tests\Util\Factory\SendEmailContextFactory;
use App\Tests\Util\Factory\UserFactory;
use App\Transport\ParentFallbackTransportFactory;
use App\Transport\TransportInterface;
use Generator;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Mailer\Exception\TransportException;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;

class UserTransportFailedOrNullTest extends TestCase
{
    #[Test]
    public function it_uses_fallback_transport(): void
    {
        $originalEmail = MimeEmailFactory::create();
        $context = $this->getMockBuilder(SendEmailContext::class)
            ->setConstructorArgs(SendEmailContextFactory::getConstructorArgsAsArray(email: $originalEmail))
            ->getMock();
        $context->expects(self::once())->method('getFallbackTransport');
        $context->expects(self::never())->method('getUserTransport');
        $state = new UserTransportFailedOrNull($context, self::createStub(ParentFallbackTransportFactory::class));

        $state->tryToSend($originalEmail);
    }

    #[Test]
    public function it_returns_fallbackTransportFailed_if_transportException_was_thrown(): void
    {
        $fallbackTransport = self::createStub(TransportInterface::class);
        $fallbackTransport->method('send')->willThrowException(new TransportException("Unknown error"));
        $context = SendEmailContextFactory::create(fallbackTransport: $fallbackTransport);
        $state = new UserTransportFailedOrNull($context, self::createStub(ParentFallbackTransportFactory::class));

        $result = $state->tryToSend($context->originalEmail);

        self::assertInstanceOf(FallbackTransportFailed::class, $result);
    }

    #[Test]
    public function it_returns_sent_if_everything_went_fine(): void
    {
        $email = self::createStub(Email::class);
        $fallbackTransport = $this->createMock(TransportInterface::class);

        $fallbackTransport->expects(self::once())->method('send')->willReturn($this->createMock(SentMessage::class));

        $context = new SendEmailContext(
            $email,
            UserFactory::createPwUser(),
            self::createStub(TransportInterface::class),
            $fallbackTransport,
            new Address('<EMAIL>'),
            true,
            self::createStub(EmailSettings::class)
        );

        $parentFallbackTransportFactory = self::createStub(ParentFallbackTransportFactory::class);
        $parentFallbackTransportFactory->method('createForUser')->willReturn(null);

        $state = new UserTransportFailedOrNull($context, $parentFallbackTransportFactory);
        $result = $state->tryToSend($email);
        self::assertInstanceOf(Sent::class, $result);
    }

    /**
     * @param class-string<AbstractSendEmailState> $expectedResultState
     */
    #[Test]
    #[DataProvider('fallbackDataprovider')]
    public function it_uses_fallbacks_correctly(
        bool $useFailsafeTransport,
        bool $mayUseFallback,
        bool $hasWorkingParentTransport,
        string $expectedResultState,
    ): void {
        $email = self::createStub(Email::class);

        $workingTransport = $this->createMock(TransportInterface::class);
        $workingTransport->method('send')->willReturn($this->createMock(SentMessage::class));

        if ($hasWorkingParentTransport) {
            $workingParentTransport = $this->createMock(TransportInterface::class);
            $workingParentTransport->method('send')->willReturn($this->createMock(SentMessage::class));

            $parentTransportFactory = self::createStub(ParentFallbackTransportFactory::class);
            $parentTransportFactory->method('createForUser')->willReturn($workingParentTransport);
        } else {
            $parentTransportFactory = self::createStub(ParentFallbackTransportFactory::class);
            $parentTransportFactory->method('createForUser')->willReturn(null);
        }

        $user = $mayUseFallback ? UserFactory::createPwUser() : UserFactory::createSystemUser();

        $context = new SendEmailContext(
            $email,
            $user,
            self::createStub(TransportInterface::class),
            $workingTransport,
            new Address('<EMAIL>'),
            $useFailsafeTransport,
            self::createStub(EmailSettings::class)
        );

        $result = (new UserTransportFailedOrNull($context, $parentTransportFactory))->tryToSend($email);

        self::assertInstanceOf($expectedResultState, $result);
    }

    public static function fallbackDataprovider(): Generator
    {
        yield 'When useFailsafeTransport and mayUseFallback are true and there is no
                working parent transport the fallback should send the email'
        => [
            'useFailsafeTransport' => true,
            'mayUseFallback' => true,
            'hasWorkingParentTransport' => false,
            'expectedResultState' => Sent::class,
        ];

        yield 'When useFailsafeTransport is false the fallback should not be used' => [
            'useFailsafeTransport' => false,
            'mayUseFallback' => true,
            'hasWorkingParentTransport' => false,
            'expectedResultState' => SendingFailed::class,
        ];

        yield 'When mayUseFallback is false the fallback should not be used' => [
            'useFailsafeTransport' => true,
            'mayUseFallback' => false,
            'hasWorkingParentTransport' => false,
            'expectedResultState' => SendingFailed::class,
        ];

        yield 'Parent Fallback is always used if available' => [
            'useFailsafeTransport' => false,
            'mayUseFallback' => false,
            'hasWorkingParentTransport' => true,
            'expectedResultState' => Sent::class,
        ];

        yield 'When useFailsafeTransport and mayUseFallback are false and there is no
                working parent transport the fallback should not send the email'
        => [
            'useFailsafeTransport' => false,
            'mayUseFallback' => false,
            'hasWorkingParentTransport' => false,
            'expectedResultState' => SendingFailed::class,
        ];
    }
}
