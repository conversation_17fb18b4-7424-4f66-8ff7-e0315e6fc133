<?php

declare(strict_types=1);

namespace App\Tests\Unit\SendEmailStateMachine;

use App\SendEmailStateMachine\SendEmailContext;
use App\SendEmailStateMachine\StatesWithTransitions\FallbackTransportFailed;
use App\SendEmailStateMachine\StatesWithTransitions\SendingFailed;
use App\SendEmailStateMachine\StatesWithTransitions\Sent;
use App\Tests\Util\Factory\MimeEmailFactory;
use App\Tests\Util\Factory\SendEmailContextFactory;
use App\Transport\TransportInterface;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Mailer\Exception\TransportException;
use Symfony\Component\Mime\Email;

class FallbackTransportFailedTest extends TestCase
{
    /**
     * @test
     */
    public function it_uses_fallback_recipient_and_transport(): void
    {
        $originalEmail = MimeEmailFactory::create();
        $contextConfig = SendEmailContextFactory::getConstructorArgsAsArray(email: $originalEmail);
        $context = $this->getMockBuilder(SendEmailContext::class)
            ->setConstructorArgs($contextConfig)
            ->getMock();
        $context->expects(self::once())->method('getFallbackTransport');
        $context->expects(self::never())->method('getUserTransport');
        $state = new FallbackTransportFailed($context);

        $prepareEmailClosure = function (Email $email) {
            /** @phpstan-ignore-next-line */
            return $this->prepare($email);
        };
        $preparedEmail = $prepareEmailClosure->call($state, $originalEmail);

        self::assertSame($context->fallbackRecipient, $preparedEmail->getTo()[0]);
        $state->tryToSend($preparedEmail);
    }

    /**
     * @test
     */
    public function it_returns_sendingFailed_if_transportException_was_thrown(): void
    {
        $fallbackTransport = self::createStub(TransportInterface::class);
        $fallbackTransport->method('send')->willThrowException(new TransportException("Unknown error"));
        $context = SendEmailContextFactory::create(fallbackTransport: $fallbackTransport);
        $state = new FallbackTransportFailed($context);
        $result = $state->tryToSend($context->originalEmail);
        self::assertInstanceOf(SendingFailed::class, $result);
    }

    /**
     * @test
     */
    public function it_returns_sent_if_everything_went_fine(): void
    {
        $email = self::createStub(Email::class);
        $context = self::createStub(SendEmailContext::class);
        $state = $this->getMockBuilder(FallbackTransportFailed::class)
            ->setConstructorArgs([$context])
            ->onlyMethods(['prepare'])
            ->getMock();
        $state->method('prepare')->willReturn($email);

        $result = $state->tryToSend($email);
        self::assertInstanceOf(Sent::class, $result);
    }
}
