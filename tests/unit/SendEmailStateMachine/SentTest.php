<?php

declare(strict_types=1);

namespace App\Tests\Unit\SendEmailStateMachine;

use App\SendEmailStateMachine\Exceptions\ActionNotAllowedForFinalStateException;
use App\SendEmailStateMachine\StatesWithTransitions\Sent;
use App\Tests\Util\Factory\MimeEmailFactory;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Mailer\SentMessage;

class SentTest extends TestCase
{
    /**
     * @test
     */
    public function it_throws_notAllowedException_if_it_is_tried_to_send(): void
    {
        $email = MimeEmailFactory::create();
        $this->expectException(ActionNotAllowedForFinalStateException::class);
        $sentMessage = self::createStub(SentMessage::class);
        $state = new Sent($sentMessage);
        $state->tryToSend($email);
    }
}
