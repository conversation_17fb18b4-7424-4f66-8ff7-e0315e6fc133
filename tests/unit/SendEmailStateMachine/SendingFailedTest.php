<?php

declare(strict_types=1);

namespace App\Tests\Unit\SendEmailStateMachine;

use App\SendEmailStateMachine\Exceptions\ActionNotAllowedForFinalStateException;
use App\SendEmailStateMachine\StatesWithTransitions\SendingFailed;
use App\Tests\Util\Factory\MimeEmailFactory;
use PHPUnit\Framework\TestCase;

class SendingFailedTest extends TestCase
{
    /**
     * @test
     */
    public function it_throws_notAllowedException_if_it_is_tried_to_send(): void
    {
        $email = MimeEmailFactory::create();
        $this->expectException(ActionNotAllowedForFinalStateException::class);
        $state = new SendingFailed();
        $state->tryToSend($email);
    }
}
