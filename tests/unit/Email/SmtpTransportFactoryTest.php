<?php

declare(strict_types=1);

namespace App\Tests\Unit\Email;

use App\Email\Provider\ProviderConfigInterface;
use App\Email\SmtpTransport;
use App\Email\SmtpTransportFactory;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Mailer\Transport\Dsn;
use Symfony\Component\Mailer\Transport\Smtp\EsmtpTransport;
use Symfony\Component\Mailer\Transport\TransportFactoryInterface;

class SmtpTransportFactoryTest extends TestCase
{
    /**
     * @test
     */
    public function it_sets_maxSentEMailsPerKeepAlive_from_providerConfig(): void
    {
        $esmtpTransportMock = $this->createMock(EsmtpTransport::class);
        $esmtpTransportMock
            ->expects(self::once())
            ->method('setRestartThreshold')
            ->with(42);

        $esmtpTransportfactoryMock = $this->createMock(TransportFactoryInterface::class);
        $esmtpTransportfactoryMock
            ->expects(self::once())
            ->method('create')
            ->willReturn($esmtpTransportMock);

        $providerConfigMock = $this->createMock(ProviderConfigInterface::class);
        $providerConfigMock
            ->expects(self::once())
            ->method('getMaxSentEmailsPerKeepAlive')
            ->willReturn(42);
        $providerConfigMock
            ->expects(self::once())
            ->method('supportsDsn')
            ->willReturn(true);

        $smtpTransportFactory = new SmtpTransportFactory(
            $esmtpTransportfactoryMock,
            [$providerConfigMock],
        );

        $smtpTransport = $smtpTransportFactory->create(new Dsn('smtp', 'host'));

        self::assertInstanceOf(SmtpTransport::class, $smtpTransport);
    }
}
