<?php

declare(strict_types=1);

namespace App\Tests\Unit\Email;

use App\Email\MicrosoftOAuthTransport;
use App\OAuth\MicrosoftProvider;
use PHPUnit\Framework\TestCase;

class MicrosoftOAuthTransportTest extends TestCase
{
    /**
     * @test
     */
    public function it_fails_if_the_sending_scope_is_missing(): void
    {
        $result = $this->getMicrosoftOAuthTranpsort('')->testConnection();

        self::assertTrue($result->isErr());
        self::assertSame(
            'The Mail.Send Scope is missing for the refresh-token',
            $result->getError()
        );
    }

    private function getMicrosoftOAuthTranpsort(
        string $scope = 'https://graph.microsoft.com/Mail.Send'
    ): MicrosoftOAuthTransport {
        $microsoftProviderMock = $this->createMock(MicrosoftProvider::class);

        return new MicrosoftOAuthTransport($microsoftProviderMock, $scope, 1);
    }
}
