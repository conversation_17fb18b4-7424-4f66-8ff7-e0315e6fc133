<?php

declare(strict_types=1);

namespace App\Tests\Unit\Email;

use App\Email\ApiTransportFactory;
use App\Email\GoogleOAuthTransport;
use App\Email\MicrosoftOAuthTransport;
use App\Email\UnsupportedApiProviderException;
use App\Enum\EmailTransportScheme;
use App\OAuth\GoogleProvider;
use App\OAuth\MicrosoftProvider;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Mailer\Transport\Dsn;

class ApiTransportFactoryTest extends KernelTestCase
{
    /**
     * @test
     */
    public function it_only_supports_api_scheme(): void
    {
        $apiTransportFactory = static::getContainer()->get("mailer.transport_factory.api");

        self::assertTrue($apiTransportFactory->supports(
            new Dsn(scheme: EmailTransportScheme::API->value, host: 'randomHost')
        ));
        self::assertFalse($apiTransportFactory->supports(
            new Dsn(scheme: EmailTransportScheme::SMTP->value, host: 'randomHost')
        ));
        self::assertFalse($apiTransportFactory->supports(
            new Dsn(scheme: EmailTransportScheme::SMTPS->value, host: 'randomHost')
        ));
    }

    /**
     * @test
     */
    public function it_fails_on_unknown_provider(): void
    {
        $apiTransportFactory = static::getContainer()->get("mailer.transport_factory.api");

        self::expectException(UnsupportedApiProviderException::class);
        $apiTransportFactory->create(
            new Dsn(scheme: EmailTransportScheme::API->value, host: 'unknownProvider', options: ['user_id' => 1])
        );
    }

    /**
     * @test
     */
    public function it_creates_a_google_oauth_transport(): void
    {
        static::getContainer()->set(
            GoogleProvider::class,
            self::createStub(GoogleProvider::class)
        );

        $apiTransportFactory = static::getContainer()->get(ApiTransportFactory::class);

        self::assertInstanceOf(GoogleOAuthTransport::class, $apiTransportFactory->create($this->makeDsn()));
    }

    /**
     * @test
     */
    public function it_creates_a_microsoft_oauth_transport(): void
    {
        static::getContainer()->set(
            MicrosoftProvider::class,
            self::createStub(MicrosoftProvider::class)
        );

        $apiTransportFactory = static::getContainer()->get(ApiTransportFactory::class);

        self::assertInstanceOf(
            MicrosoftOAuthTransport::class,
            $apiTransportFactory->create($this->makeDsn(provider: 'microsoft'))
        );
    }

    private function makeDsn(
        EmailTransportScheme $scheme = EmailTransportScheme::API,
        string $provider = 'google',
    ): Dsn {
        return new Dsn(scheme: $scheme->value, host: $provider, options: [
            'provider' => $provider,
            'access_token' => 'ACCESS_TOKEN',
            'refresh_token' => 'REFRESH_TOKEN',
            'user_id' => 1,
        ]);
    }
}
