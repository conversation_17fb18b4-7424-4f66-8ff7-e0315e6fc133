<?php

declare(strict_types=1);

namespace App\Tests\Unit\Email;

use App\Email\GoogleOAuthTransport;
use App\OAuth\GoogleProvider;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use PHPUnit\Framework\TestCase;

class GoogleOAuthTransportTest extends TestCase
{
    /**
     * @test
     */
    public function it_fails_if_connection_can_not_be_established(): void
    {
        $authorizedGuzzleClient = $this->createMock(Client::class);
        $authorizedGuzzleClient->expects(self::once())->method('request')->willReturn(new Response(500, [], 'Fehler'));

        $googleClient = $this->createMock(\Google\Client::class);
        $googleClient->expects(self::once())->method('authorize')->willReturn($authorizedGuzzleClient);

        $result = $this->getGoogleOAuthTransport($googleClient)->testConnection();

        self::assertTrue($result->isErr());
        self::assertSame(
            'Fehler',
            $result->getError()
        );
    }

    /**
     * @test
     */
    public function it_fails_if_the_sending_scope_is_missing(): void
    {
        $googleClient = $this->createMock(\Google\Client::class);

        $result = $this->getGoogleOAuthTransport($googleClient, '')->testConnection();

        self::assertTrue($result->isErr());
        self::assertSame(
            'The gmail.send Scope is missing for the refresh-token',
            $result->getError()
        );
    }

    /**
     * @test
     */
    public function it_succeeds_if_connection_can_be_established(): void
    {
        $authorizedGuzzleClient = $this->createMock(Client::class);
        $authorizedGuzzleClient->expects(self::once())->method('request')->willReturn(new Response(200));

        $googleClient = $this->createMock(\Google\Client::class);
        $googleClient->expects(self::once())->method('authorize')->willReturn($authorizedGuzzleClient);

        $result = $this->getGoogleOAuthTransport($googleClient)->testConnection();

        self::assertTrue($result->isOk());
    }

    private function getGoogleOAuthTransport(
        ?\Google\Client $googleClient,
        string $scope = 'https://www.googleapis.com/auth/gmail.send'
    ): GoogleOAuthTransport {
        $googleProviderMock = $this->createMock(GoogleProvider::class);
        $googleProviderMock->method('getCheckedClientForUserId')->willReturn($googleClient);

        return new GoogleOAuthTransport(
            $googleProviderMock,
            $scope,
            1
        );
    }
}
