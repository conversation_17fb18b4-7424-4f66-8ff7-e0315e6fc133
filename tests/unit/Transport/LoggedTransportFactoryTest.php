<?php

declare(strict_types=1);

namespace App\Tests\Unit\Transport;

use App\Tests\Util\Factory\DsnFactory;
use App\Transport\LoggedTransportFactory;
use App\Transport\TransportFactoryInterface;
use App\Util\EmailHistoryLogger;
use PHPUnit\Framework\TestCase;

class LoggedTransportFactoryTest extends TestCase
{
    /**
     * @test
     */
    public function it_uses_the_inner_factory_for_fromDsnObject(): void
    {
        $innerTransport = $this->createMock(TransportFactoryInterface::class);
        $innerTransport->expects(self::once())->method('fromDsnObject');
        $logger = self::createStub(EmailHistoryLogger::class);
        $dsn = DsnFactory::create();

        $factory = new LoggedTransportFactory($innerTransport, $logger);
        $factory->fromDsnObject($dsn);
    }
}
