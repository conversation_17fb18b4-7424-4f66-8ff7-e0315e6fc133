<?php

declare(strict_types=1);

namespace App\Tests\Unit\Transport;

use App\Tests\Util\Factory\MimeEmailFactory;
use App\Transport\SenderAdjustingTransport;
use App\Transport\SymfonyTransportAdapter;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Mailer\Transport;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;

class SenderAdjustingTransportTest extends TestCase
{
    /**
     * @test
     */
    public function it_prepares_headers_before_sending(): void
    {
        $transport = new SymfonyTransportAdapter(Transport::fromDsn('null://null'));
        $fallbackSender = Address::create('Fallback Sender <<EMAIL>>');
        $email = MimeEmailFactory::create();
        $originalFrom = $email->getFrom()[0];

        $senderAdjustingTransport = new SenderAdjustingTransport(
            $transport,
            $fallbackSender,
        );
        $sentEmail = $senderAdjustingTransport->send($email)?->getOriginalMessage();

        self::assertInstanceOf(Email::class, $sentEmail);
        self::assertEquals($originalFrom, $sentEmail->getReplyTo()[0]);
        self::assertCount(1, $sentEmail->getFrom());
        self::assertEquals($fallbackSender, $sentEmail->getFrom()[0]);
    }
}
