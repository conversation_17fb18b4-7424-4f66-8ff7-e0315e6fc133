<?php

declare(strict_types=1);

namespace App\Tests\Unit\Converter;

use App\Converter\EmailDtoConverter;
use App\DTO\Request\Attachment;
use App\DTO\Request\Email;
use App\Enum\AttachmentContentDisposition;
use App\Service\EmailFileService;
use App\Tests\Util\Factory\UserFactory;
use Generator;
use League\HTMLToMarkdown\HtmlConverterInterface;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Uid\Uuid;

class EmailDtoConverterTest extends TestCase
{
    /**
     * @test
     * @dataProvider caseProviderForTextFallback
     */
    public function it_converts_html_as_fallback_for_missing_text(?string $text, ?string $html, int $conversions): void
    {
        $mailFileService = self::createStub(EmailFileService::class);

        $dto = self::createStub(Email::class);
        $dto->subject = 'UnitTest';
        $dto->text = $text;
        $dto->html = $html;
        $dto->uuid = Uuid::v4();

        $converter = $this->createMock(HtmlConverterInterface::class);
        $converter->expects(self::exactly($conversions))
            ->method('convert');

        $emailDtoConverter = new EmailDtoConverter(
            $mailFileService,
            $converter,
            self::createStub(LoggerInterface::class),
        );
        $user = UserFactory::createPwUser();
        $emailDtoConverter->toEmailEntity($dto, $user);
    }

    /**
     * @param array<Attachment> $attachments
     * @param array<array<string,string>> $expectedImages
     *
     * @test
     * @dataProvider inlineImagesProvider
     */
    public function it_handles_inline_images_correctly(string $html, array $attachments, array $expectedImages): void
    {
        $mailFileService = self::createStub(EmailFileService::class);

        $dto = self::createStub(Email::class);
        $dto->subject = 'UnitTest';
        $dto->html = $html;
        $dto->attachments = $attachments;
        $dto->uuid = Uuid::v4();

        $converter = $this->createMock(HtmlConverterInterface::class);

        $emailDtoConverter = new EmailDtoConverter(
            $mailFileService,
            $converter,
            self::createStub(LoggerInterface::class)
        );
        $user = UserFactory::createPwUser();

        $entity = $emailDtoConverter->toEmailEntity($dto, $user);

        foreach ($expectedImages as $image) {
            self::assertNotEmpty(
                array_filter(
                    $entity->getAttachments()->toArray(),
                    static fn(\App\Entity\Attachment $attachment) => $attachment->getContentId() === $image['cid']
                )
            );
        }
    }

    /**
     * @return array<string, array{?string, ?string, int}>
     */
    public static function caseProviderForTextFallback(): array
    {
        return [
            'no text, no html, no conversion' => [null, null, 0],
            'no text, but html, one conversion' => [null, '<h3></h3>', 1],
            'text and html, no conversion' => ['string', '<p>string</p>', 0],
        ];
    }

    public static function inlineImagesProvider(): Generator
    {
        yield 'one image' => [
            '<img src="cid:123@pw">',
            [
                new Attachment(
                    'name',
                    'body',
                    contentDisposition: AttachmentContentDisposition::INLINE,
                    contentId: '123@pw'
                ),
            ],
            [
                ['cid' => '123@pw'],
            ],
        ];

        yield 'one image without domain' => [
            '<img src="cid:123">',
            [
                new Attachment(
                    'name',
                    'body',
                    contentDisposition: AttachmentContentDisposition::INLINE,
                    contentId: '123'
                ),
            ],
            [
                ['cid' => '<EMAIL>'],
            ],
        ];

        yield 'two images without domain' => [
            '<img src="cid:123"> & <img src="cid:456">',
            [
                new Attachment(
                    'name',
                    'body',
                    contentDisposition: AttachmentContentDisposition::INLINE,
                    contentId: '123'
                ),
                new Attachment(
                    'name',
                    'body',
                    contentDisposition: AttachmentContentDisposition::INLINE,
                    contentId: '456'
                ),
            ],
            [
                ['cid' => '<EMAIL>'],
                ['cid' => '<EMAIL>'],
            ],
        ];
    }
}
