<?php

declare(strict_types=1);

namespace App\Tests\Unit\Converter;

use App\Converter\EmailEntityConverter;
use App\Entity\Address;
use App\Entity\Email;
use App\Entity\Path;
use App\Service\EmailFileService;
use PHPUnit\Framework\TestCase;

class EmailEntityConverterTest extends TestCase
{
    /**
     * @test
     */
    public function it_converts_email_entities_to_mime_emails(): void
    {
        $emailFileService = self::createStub(EmailFileService::class);

        $emailEntity = new Email(42, 'test');
        $emailEntity->setToAddresses([new Address('<EMAIL>', 'Unit Tester')]);
        $emailEntity->setTextPath(new Path('url://example.org'));

        $emailEntityConverter = new EmailEntityConverter($emailFileService);

        $mimeMail = $emailEntityConverter->toMimeEmail($emailEntity);

        self::assertTrue($mimeMail->isOk());
        self::assertEquals('test', mb_decode_mimeheader($mimeMail->getValue()->getSubject() ?? ''));
    }
}
