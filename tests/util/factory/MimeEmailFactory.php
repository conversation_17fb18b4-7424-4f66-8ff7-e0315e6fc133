<?php

declare(strict_types=1);

namespace App\Tests\Util\Factory;

use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Part\DataPart;

class MimeEmailFactory
{
    public static function create(?Email $email = null, bool $withHtmlBody = true, int $attachments = 0): Email
    {
        $email = $email ?? new Email();
        $email->from(Address::create('Test Sender <<EMAIL>>'));
        $email->to(Address::create('Test Recipient <<EMAIL>>'));

        if ($withHtmlBody) {
            $email->html('<p>Hello TestWorld!</p>');
        }

        for ($attached = 0; $attached < $attachments; ++$attached) {
            $email->addPart(new DataPart('This is a test attachment'));
        }

        return $email;
    }
}
