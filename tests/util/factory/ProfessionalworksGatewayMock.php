<?php

declare(strict_types=1);

namespace App\Tests\Util\Factory;

use Demv\SdkFramework\Gateway\GatewayInterface;
use GuzzleHttp\Psr7\Response;
use Psr\Http\Message\ResponseInterface;

class ProfessionalworksGatewayMock implements GatewayInterface
{
    /**
     * @param array<mixed> $requestOptions
     * @param array<mixed> $middlewares
     */
    public function request(
        string $method,
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throwHttpError = false
    ): ResponseInterface {
        return new Response();
    }

    /**
     * @param array<mixed> $params
     * @param array<mixed> $middlewares
     */
    public function get(
        string $uri,
        array $params = [],
        array $middlewares = [],
        bool $throw = false
    ): ResponseInterface {
        return $this->request('GET', $uri, [], $middlewares, $throw);
    }

    /**
     * @param array<mixed> $requestOptions
     * @param array<mixed> $middlewares
     */
    public function post(
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throw = false
    ): ResponseInterface {
        return $this->request('POST', $uri, $requestOptions, $middlewares, $throw);
    }

    /**
     * @param array<mixed> $requestOptions
     * @param array<mixed> $middlewares
     */
    public function put(
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throw = false
    ): ResponseInterface {
        return $this->request('PUT', $uri, $requestOptions, $middlewares, $throw);
    }

    public function getBaseUrl(): string
    {
        return '';
    }
}
