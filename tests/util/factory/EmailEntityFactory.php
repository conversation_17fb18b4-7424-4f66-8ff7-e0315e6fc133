<?php

declare(strict_types=1);

namespace App\Tests\Util\Factory;

use App\Entity\Attachment;
use App\Entity\Email;
use App\Entity\Path;
use App\Enum\AttachmentContentDisposition;

class EmailEntityFactory
{
    public static function createEmail(): Email
    {
        $email = new Email(null);
        $reflectId = new \ReflectionProperty(Email::class, 'id');
        $reflectId->setValue($email, 1);
        $attachment = new Attachment('a', 'text', AttachmentContentDisposition::ATTACHMENT);
        $attachment->setBodyPath(new Path(''));
        $email->addAttachment($attachment);

        return $email;
    }
}
