<?php

declare(strict_types=1);

namespace App\Tests\Util\Factory;

use App\Entity\EmailSettings;
use App\Security\UserInterface;
use App\SendEmailStateMachine\SendEmailContext;
use App\Transport\SymfonyTransportAdapter;
use App\Transport\TransportInterface;
use Symfony\Component\Mailer\Transport;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;

class SendEmailContextFactory
{
    public static function create(
        Email $email = null,
        ?UserInterface $user = null,
        bool $useFailSafeTransport = true,
        Address $fallbackRecipient = null,
        TransportInterface $fallbackTransport = null,
        TransportInterface $userTransport = null,
        EmailSettings $emailSettings = null,
    ): SendEmailContext {
        return new SendEmailContext(...self::getConstructorArgsAsArray(
            $email,
            $user,
            $useFailSafeTransport,
            $fallbackRecipient,
            $fallbackTransport,
            $userTransport,
            $emailSettings,
        ));
    }

    /**
     * @return array{
     *     email: Email,
     *     user: UserInterface,
     *     useFailsafeTransport: bool,
     *     fallbackRecipient: Address,
     *     fallbackTransport: TransportInterface,
     *     userTransport: TransportInterface,
     *     emailSettings: EmailSettings,
     * }
     */
    public static function getConstructorArgsAsArray(
        Email $email = null,
        UserInterface $user = null,
        bool $useFailSafeTransport = true,
        Address $fallbackRecipient = null,
        TransportInterface $fallbackTransport = null,
        TransportInterface $userTransport = null,
        EmailSettings $emailSettings = null,
    ): array {
        $email = $email ?? MimeEmailFactory::create();
        $user = $user ?? UserFactory::createPwUser();
        $fallbackRecipient = $fallbackRecipient ?? Address::create('Fallback Recipient <<EMAIL>>');
        $fallbackTransport = $fallbackTransport ?? new SymfonyTransportAdapter(Transport::fromDsn('null://null'));
        $userTransport = $userTransport ?? new SymfonyTransportAdapter(Transport::fromDsn('null://null'));
        $emailSettings = $emailSettings ?? EmailSettingsFactory::create();

        return [
            'email' => $email,
            'user' => $user,
            'useFailsafeTransport' => $useFailSafeTransport,
            'fallbackRecipient' => $fallbackRecipient,
            'fallbackTransport' => $fallbackTransport,
            'userTransport' => $userTransport,
            'emailSettings' => $emailSettings,
        ];
    }
}
