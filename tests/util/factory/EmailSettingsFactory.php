<?php

declare(strict_types=1);

namespace App\Tests\Util\Factory;

use App\Entity\EmailSettings;
use App\Entity\SmtpImapConnection;

class EmailSettingsFactory
{
    public static function create(): EmailSettings
    {
        return new SmtpImapConnection(
            userId: 1,
            email: '<EMAIL>',
            smtpHost: 'demvHost',
            smtpPort: 123456,
            username: 'TestUser',
            password: 'Testpassword',
        );
    }
}
