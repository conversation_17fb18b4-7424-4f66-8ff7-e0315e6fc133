<?php

declare(strict_types=1);

namespace App\Tests\Util\Service;

use App\Entity\Path;
use App\Service\EmailFileService;

class TestEmailFileService extends EmailFileService
{
    private static int $sleepTime = 0;

    public static function setSleepTimeforWriting(int $sleepTime): void
    {
        self::$sleepTime = $sleepTime;
    }

    public function setContent(Path $path, string $content): void
    {
        usleep(self::$sleepTime);

        parent::setContent($path, $content);
    }
}
