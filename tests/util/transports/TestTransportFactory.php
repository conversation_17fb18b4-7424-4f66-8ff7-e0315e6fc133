<?php

declare(strict_types=1);

namespace App\Tests\Util\transports;

use App\Transport\TransportFactoryInterface;
use App\Transport\TransportInterface;
use Psr\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Mailer\Transport\Dsn;

class TestTransportFactory implements TransportFactoryInterface
{
    public function __construct(
        private readonly TransportFactoryInterface $inner,
        private readonly EventDispatcherInterface $dispatcher
    ) {
    }

    public function fromDsnString(string $dsn): TransportInterface
    {
        $transport = $this->inner->fromDsnString($dsn);

        return new TestTransport($transport, $this->dispatcher);
    }

    public function fromDsnStrings(array $dsns): array
    {
        return array_map($this->fromDsnString(...), $dsns);
    }

    public function fromDsnObject(Dsn $dsn): TransportInterface
    {
        $transport = $this->inner->fromDsnObject($dsn);

        return new TestTransport($transport, $this->dispatcher);
    }
}
