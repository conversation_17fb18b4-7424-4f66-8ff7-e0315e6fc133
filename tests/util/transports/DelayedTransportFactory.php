<?php

declare(strict_types=1);

namespace App\Tests\Util\transports;

use App\Transport\TransportFactoryInterface;
use App\Transport\TransportInterface;
use Symfony\Component\Mailer\Transport\Dsn;

/**
 * The factory needs to be configured explicitly ($delayInSecs). Make sure to do so before it is used. Otherwise
 * $delayInSecs is null and the DelayedTransport decoration will be skipped.
 */
class DelayedTransportFactory implements TransportFactoryInterface
{
    /** @var float[] $delaysInSecs */
    public array $delaysInSecs = [];

    public function __construct(private readonly TransportFactoryInterface $inner)
    {
    }

    public function fromDsnString(string $dsn): TransportInterface
    {
        $transport = $this->inner->fromDsnString($dsn);

        return new DelayedTransport($transport, $this->delaysInSecs);
    }

    public function fromDsnStrings(array $dsns): array
    {
        return array_map($this->fromDsnString(...), $dsns);
    }

    public function fromDsnObject(Dsn $dsn): TransportInterface
    {
        $transport = $this->inner->fromDsnObject($dsn);

        return new DelayedTransport($transport, $this->delaysInSecs);
    }
}
