<?php

declare(strict_types=1);

namespace App\Tests\Util\transports;

use App\Email\Provider\ProviderConfigInterface;
use App\Transport\TransportInterface;
use App\Util\Result;
use Exception;
use Psr\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mailer\Transport\AbstractTransport;

/**
 * This transport is used in tests to avoid sending real emails.
 */
class TestTransport extends AbstractTransport implements TransportInterface
{
    /** @var Result<null,string>|null */
    private static ?Result $testConnectionResult = null;
    private static ?Exception $exceptionDuringSending = null;

    public function __construct(private readonly TransportInterface $inner, EventDispatcherInterface $dispatcher)
    {
        parent::__construct($dispatcher);
    }

    /**
     * @param Result<null,string>|null $testConnectionResult
     *
     * @return void
     */
    public static function setTestConnectionResult(?Result $testConnectionResult): void
    {
        self::$testConnectionResult = $testConnectionResult;
    }

    public static function setExceptionDuringSending(?Exception $exception): void
    {
        self::$exceptionDuringSending = $exception;
    }

    protected function doSend(SentMessage $message): void
    {
        if (self::$exceptionDuringSending !== null) {
            throw self::$exceptionDuringSending;
        }

        // besides that do nothing
    }

    public function testConnection(): Result
    {
        return self::$testConnectionResult ?? Result::ok(null);
    }

    public function getProviderConfig(): ?ProviderConfigInterface
    {
        return $this->inner->getProviderConfig();
    }

    public function __toString(): string
    {
        return "DontSendTransport({$this->inner})";
    }
}
