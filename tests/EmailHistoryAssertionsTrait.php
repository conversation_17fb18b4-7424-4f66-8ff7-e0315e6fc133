<?php

declare(strict_types=1);

namespace App\Tests;

use App\Entity\EmailHistoryError;
use App\Entity\EmailHistoryTransition;
use Doctrine\ORM\EntityManager;

trait EmailHistoryAssertionsTrait
{
    abstract protected function getEntityManager(): EntityManager;

    /**
     * @param array<string> $expectedCauses
     */
    public function assertEmailHistoryErrorForEmailId(int $id, array $expectedCauses): void
    {
        $logErrorRepository = $this->getEntityManager()->getRepository(EmailHistoryError::class);
        $loggedErrors = $logErrorRepository->findBy(['email' => $id]);
        self::assertCount(count($expectedCauses), $loggedErrors);

        $loggedErrorResponses = array_map(static fn(EmailHistoryError $error) => $error->getCause(), $loggedErrors);
        $loggedResponseString = '[' . implode(', ', $loggedErrorResponses) . ']';

        foreach ($expectedCauses as $expectedCause) {
            self::assertContains(
                $expectedCause,
                $loggedErrorResponses,
                "The array {$loggedResponseString} does not contain the string '{$expectedCause}'"
            );
        }
    }

    /**
     * @param array<string> $transitionLog
     */
    public function assertEmailHistoryTransitionForEmailId(int $id, array ...$transitionLog): void
    {
        $logTransitionRepository = $this->getEntityManager()->getRepository(EmailHistoryTransition::class);
        $transitions = $logTransitionRepository->findBy(['email' => $id]);

        for ($i = 0; $i < count($transitionLog) - 1; $i++) {
            self::assertEquals($transitionLog[$i][0], $transitions[$i]->getFromState());
            self::assertEquals($transitionLog[$i][1], $transitions[$i]->getToState());
        }

        self::assertCount(count($transitionLog), $transitions);
    }
}
