# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_SECRET='$ecretf0rt3st'
SYMFONY_DEPRECATIONS_HELPER=999999
PANTHER_APP_ENV=panther
PANTHER_ERROR_SCREENSHOT_DIR=./var/error-screenshots

MYSQL_ROOT_PASSWORD=
DB_DATABASE=mailer
DB_USERNAME=mailer
DB_PASSWORD=demv
DB_HOST=mariadb_mailer
DB_PORT=3306

DATABASE_URL="mysql://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}?serverVersion=5.7&charset=utf8mb4"

SYSTEM_MAILER_DSN="null://null"
FALLBACK_MAILER_SENDER="Fallback Sender <<EMAIL>>"
FALLBACK_MAILER_RECIPIENT="Fallback Recipient <<EMAIL>>"

BATCH_TIMEOUT=11
QUEUE_SEND_TIMEOUT=11

MICROSOFT_OAUTH_CLIENT_ID=
MICROSOFT_OAUTH_CLIENT_SECRET=

MESSENGER_TRANSPORT_DSN=redis://redis:6379

FORCED_MAILER_RECIPIENT="<EMAIL>"
