on: [ push ]
jobs:
    lint-and-test:
        runs-on: ubuntu-latest
        strategy:
            matrix:
                php-versions: [ '8.2' ]
        services:
            mariadb:
                image: mariadb:10.6
                env:
                    MYSQL_ALLOW_EMPTY_PASSWORD: yes
                    MYSQL_DATABASE: mailer
                ports:
                    - 3306
                options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
            redis:
                image: redis
                ports:
                  - 6379:6379
                options: >-
                  --health-cmd="redis-cli ping"
                  --health-interval=10s
                  --health-timeout=5s
                  --health-retries=3

        steps:
            -   uses: actions/checkout@v4

            -   name: Setup PHP with composer v2
                uses: shivammathur/setup-php@v2
                with:
                    php-version: ${{ matrix.php-versions }}
                    tools: composer:v2
                    coverage: none
                env:
                    COMPOSER_TOKEN: ${{ secrets.PRIVATE_TOKEN }}

            -   name: Get composer cache directory
                id: composercache
                run: echo "::set-output name=dir::$(composer config cache-files-dir)"

            -   name: Cache dependencies
                uses: actions/cache@v4
                with:
                    path: ${{ steps.composercache.outputs.dir }}
                    key: ${{ runner.os }}-composer-${{ hashFiles('composer.lock') }}
                    restore-keys: ${{ runner.os }}-composer-

            -   name: Set Composer Secret
                run: echo "COMPOSER_SECRET=${{ secrets.PRIVATE_TOKEN }}" >> $GITHUB_ENV

            -   name: Install private repo token
                run: composer config --global github-oauth.github.com ${{ env.COMPOSER_SECRET }}

            -   name: Install Composer dependencies
                run: composer install --prefer-dist --no-progress --no-suggest --optimize-autoloader

            -   name: Lint Symfony container
                run: bin/console lint:container

            -   name: Lint project
                run: vendor/bin/phpcstd --ci --continue

            -   name: Run PHPUnit Tests
                run: vendor/bin/paratest -p6 --runner WrapperRunner
                env:
                    DB_PASSWORD: ""
                    DB_USERNAME: root
                    DB_DATABASE: mailer
                    DB_HOST: 127.0.0.1
                    DB_PORT: ${{ job.services.mariadb.ports[3306] }}
                    MESSENGER_TRANSPORT_DSN: redis://127.0.0.1:6379

            -   name: Run Swagger-PHP
                run: ./vendor/bin/openapi src -o documentation/openapi.yaml

            -   name: Check if OpenApi Documentation is up to date
                run: git diff --exit-code documentation/openapi.yaml || (echo "Die OpenApi Dokumentation ist nicht auf dem aktuellsten Stand, `make openApi` ausführen wenn sich die API ändert!" && exit 1)
