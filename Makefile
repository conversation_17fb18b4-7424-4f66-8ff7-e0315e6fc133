.env: .env.dist
	@echo Prüfe .env Daten...
	@test -f .env || cp .env.dist .env
	@env -i sh -c '. ./.env; test -n "$$GITHUB_TOKEN" || (echo -n "Github Token eingeben: "; read line; echo "GITHUB_TOKEN=$$line" >> .env)'
	@env -i sh -c '. ./.env; test -n "$$USER_ID" || (echo -n "USER_ID eingeben: "; read line; echo "USER_ID=$$line" >> .env)'
	@env -i sh -c '. ./.env; test -n "$$GROUP_ID" || (echo -n "GROUP_ID eingeben: "; read line; echo "GROUP_ID=$$line" >> .env)'
	@echo ...fertig

include .env

##
## SETUP COMMANDS -
## ---

.PHONY: setup config composer-install migrate

setup: up config composer-install migrate setup-pw ## Initializes project

setup-pw:
	@./setup_pw.sh

config: ## Checks the configuration
	@test -f config/jwt/pw_public.pem || (echo " The file 'config/jwt/pw_public.pem' does not exist! Exiting"; false;)

install: composer-install

composer-install: ## Run `composer install`
	docker compose exec php-fpm composer install

migrate: composer-install ## Apply the database migrations
	docker compose exec php-fpm bin/console doctrine:migrations:migrate

##
## UTILITY COMMANDS - Static Code Analysis & Testing
## ---
.PHONY: check test lint lint-fix

check: test lint ## Runs all tests and static code analysis tools

test: ## Runs all tests
	docker compose exec php-fpm vendor/bin/paratest -p6 --runner WrapperRunner

test-coverage: ## Runs all tests with code coverage
	docker compose exec -e XDEBUG_MODE=coverage php-fpm vendor/bin/paratest -p6 --runner WrapperRunner --coverage-text

test-coverage-extended: ## Runs all test with code coverage including path and branch coverage
	docker compose exec -e XDEBUG_MODE=coverage php-fpm vendor/bin/phpunit ./tests/ --path-coverage --coverage-text

lint: lint-symfony lint-php ## Runs linters on Symfony config and PHP code

lint-symfony: ## Lints the Symfony config
	docker compose exec php-fpm bin/console lint:container

lint-php: ## Runs static code analysis tools (PHPCodeSniffer, Phpstan, composer-normalize)
	docker compose exec php-fpm vendor/bin/phpcstd

lint-fix: ## Runs static code analysis tools (PHPCodeSniffer, Phpstan, composer-normalize) and fixes issues if possible
	docker compose exec php-fpm vendor/bin/phpcstd --fix

##
## UTILITY COMMANDS - DOCKER
## ---

.PHONY: kill up stop

kill: ## Kill all containers
	docker compose kill
	docker compose down --volumes --remove-orphans

up: ## Start all containers
	docker compose up -d

stop: ## Stop all containers
	docker compose stop

##
## UTILITY COMMANDS - MISC
## ---

php-console:
	docker compose exec php-fpm bash -c "XDEBUG_MODE=off php bin/console $(filter-out $@,$(MAKECMDGOALS))"

##
## UTILITY COMMANDS - DEPLOYMENT
## ---

.PHONY: deploy-dev

branch = main

deploy-dev: ## Deploy to development-server
	.docker/deploy/deployer.sh deploy stage=dev -o branch=$(branch)

deploy-staging: ## Deploy to staging-server
	.docker/deploy/deployer.sh deploy stage=staging -o branch=$(branch)

deploy-live: ## Deploy to all live servers
	.docker/deploy/deployer.sh deploy stage=live

##
## Documentation
## ---

open-api: ## Generate openApi Documentation (path: documentation/openapi.yaml)
	docker compose exec php-fpm ./vendor/bin/openapi src -o ./documentation/openapi.yaml

##
## Application
## ---

resend-mails:
	@./resend_mails.sh $(filter-out $@,$(MAKECMDGOALS))


##
## HELP
## ---

.PHONY: help
.DEFAULT_GOAL := help

help: ## show this help message
	@grep -h -E '(^[a-zA-Z0-9_-]+:.*?## .*$$)|(^##( |$$))' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[32m%-30s\033[0m %s\n", $$1, $$2}' | sed -e 's/\[32m##/[33m/'

shell:
	@docker exec -it mailer bash
