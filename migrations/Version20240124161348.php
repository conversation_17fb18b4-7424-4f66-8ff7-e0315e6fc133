<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240124161348 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add MessageId column to Email table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email ADD message_id VARCHAR(1023) DEFAULT NULL AFTER sent_at');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email DROP message_id');
    }
}
