<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231012082357 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add sending and receiving status to email settings';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email_settings ADD sending_status VARCHAR(255) DEFAULT NULL, ADD receiving_status VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email_settings DROP sending_status, DROP receiving_status');
    }
}
