<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240703104341 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add retry_at field to email table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email ADD retry_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email DROP retry_at');
    }
}
