<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231024084632 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add ServiceId to EmailBatch to see what service issued a specific email batch';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email_batch ADD service_id VARCHAR(255) NOT NULL DEFAULT \'mailer\' after user_id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email_batch DROP service_id');
    }
}
