<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240724134713 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add index on email_history.created_at';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX index_created_at ON email_history (created_at)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX index_created_at ON email_history');
    }
}
