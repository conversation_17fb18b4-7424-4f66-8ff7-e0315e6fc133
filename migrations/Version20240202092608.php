<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240202092608 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add receiving_failed_connections_attempts column to be able to count failed connection attempts';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email_settings ADD receiving_failed_connections_attempts INT DEFAULT 0 NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email_settings DROP receiving_failed_connections_attempts');
    }
}
