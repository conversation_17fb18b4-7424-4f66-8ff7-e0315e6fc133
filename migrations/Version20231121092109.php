<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231121092109 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add index (status, user_id) to Email Table.'
            . ' This is needed to be able to use "for update skip locked" in our queue workers';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX IDX_E7927C747B00651CA76ED395 ON email (status, user_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX IDX_E7927C747B00651CA76ED395 ON email');
    }
}
