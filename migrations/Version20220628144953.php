<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220628144953 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add imap_host and imap_port column to smtp_imap_connection table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE
          smtp_imap_connection
        ADD
          imap_host VARCHAR(255) DEFAULT NULL,
        ADD
          imap_port INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE smtp_imap_connection DROP imap_host, DROP imap_port');
    }
}
