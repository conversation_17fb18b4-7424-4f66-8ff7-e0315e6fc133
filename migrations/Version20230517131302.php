<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230517131302 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename EmailHistoryErrorEntity properties, make cause nullable and refresh' . PHP_EOL .
            'email history full view';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'EOSQL'
            ALTER TABLE email_history_error
            CHANGE transport_error_type kind VARCHAR(255) NOT NULL,
            CHANGE title type VARCHAR(255) NOT NULL,
            CHANGE description cause LONGTEXT DEFAULT NULL
            EOSQL);
        $this->addSql(<<<'EOSQL'
            UPDATE email_history_error
                NATURAL JOIN (
                    SELECT
                        id,
                        CASE
                            WHEN kind = 'unknown'
                            AND type = 'User has no valid transport'
                                THEN JSON_ARRAY('TransportError', 'no_transport_available', cause)
                            WHEN kind = 'unknown'
                            AND type = 'Error in Transporting the Email'
                            AND SUBSTR(cause, 1, 10) = 'Refused by'
                                THEN JSON_ARRAY('TransportError', 'classified_as_spam', cause)
                            WHEN kind = 'unknown'
                            AND type = 'Error in Transporting the Email'
                            AND SUBSTR(cause, 1, 10) = '500 Messag'
                                THEN JSON_ARRAY('TransportError', 'attachment_too_large', cause)
                            WHEN kind = 'unknown'
                            AND type = 'Error in Transporting the Email'
                            AND SUBSTR(cause, 1, 10) = '552 Mail s'
                                THEN JSON_ARRAY('TransportError', 'attachment_too_large', cause)
                            WHEN kind = 'smtp_connect'
                                THEN JSON_ARRAY('TransportError', 'smtp_connect_failed', type)
                            -- validation errors where collected per email and saved as imploded string in title
                            -- therefore we cannot safely reconstruct the address field which failed
                            WHEN kind = 'input_invalid'
                                THEN JSON_ARRAY('ValidationError', 'unknown', type)
                            WHEN kind = 'timeout'
                            AND type = 'Timeout exceeded'
                                THEN JSON_ARRAY('TransportError', 'timeout_exceeded', cause)
                            -- the former description field was only used by ErrorLogContext::fromTransportException
                            -- in all other cases, the errorMessage was written to title
                            -- (except for timeout it was written to both)
                            ELSE JSON_ARRAY('UnknownError', type , cause)
                        END AS new_vals
                    FROM email_history_error) AS x
            SET
                kind = JSON_VALUE(new_vals, '$[0]'),
                type = JSON_VALUE(new_vals, '$[1]'),
                cause = JSON_VALUE(new_vals, '$[2]');
            EOSQL);
        $this->addSql(<<<'EOSQL'
            CREATE OR REPLACE VIEW email_history_full AS
            SELECT *
            FROM email_history
            LEFT JOIN email_history_transition using (id)
            LEFT JOIN email_history_error using (id);
            EOSQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'EOSQL'
            UPDATE email_history_error
                NATURAL JOIN (
                    SELECT
                        id,
                        CASE
                            WHEN kind = 'TransportError'
                            AND type = 'no_transport_available'
                                THEN JSON_ARRAY('unknown', 'User has no valid transport', cause)
                            WHEN kind = 'TransportError'
                            AND type = 'classified_as_spam'
                            AND SUBSTR(cause, 1, 10) = 'Refused by'
                                THEN JSON_ARRAY('unknown', 'Error in Transporting the Email', cause)
                            WHEN kind = 'TransportError'
                            AND type = 'attachment_too_large'
                            AND SUBSTR(cause, 1, 10) = '500 Messag'
                                THEN JSON_ARRAY('unknown', 'Error in Transporting the Email', cause)
                            WHEN kind = 'TransportError'
                            AND type = 'attachment_too_large'
                            AND SUBSTR(cause, 1, 10) = '552 Mail s'
                                THEN JSON_ARRAY('unknown', 'Error in Transporting the Email', cause)
                            WHEN kind = 'TransportError'
                            AND type = 'smtp_connect_failed'
                                THEN JSON_ARRAY('smtp_connect', type, cause)
                            -- errors where collected per email and saved as imploded string in title
                            -- therefore we cannot safely reconstruct the address field which failed
                            WHEN kind = 'ValidationError'
                            AND type = 'unknown'
                                THEN JSON_ARRAY('input_invalid', type, cause)
                            WHEN kind = 'TransportError'
                            AND type = 'timeout_exceeded'
                                THEN JSON_ARRAY('timeout', 'Timeout exceeded', cause)
                            WHEN kind = 'UnknownError'
                                THEN JSON_ARRAY('unknown', type, cause)
                        END AS new_vals
                    FROM email_history_error) AS x
            SET
                kind = JSON_VALUE(new_vals, '$[0]'),
                type = JSON_VALUE(new_vals, '$[1]'),
                cause = JSON_VALUE(new_vals, '$[2]');
            EOSQL);
        $this->addSql(<<<'EOSQL'
            ALTER TABLE email_history_error
            CHANGE kind title VARCHAR(1023) NOT NULL,
            CHANGE type transport_error_type VARCHAR(255) NOT NULL,
            CHANGE cause description LONGTEXT NOT NULL
            EOSQL);
        $this->addSql(<<<'EOSQL'
            CREATE OR REPLACE VIEW email_history_full AS
            SELECT *
            FROM email_history
            LEFT JOIN email_history_transition using (id)
            LEFT JOIN email_history_error using (id);
            EOSQL);
    }
}
