<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230321133213 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add id_in_Batch column to email';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email ADD id_in_batch SMALLINT UNSIGNED DEFAULT NULL AFTER batch_id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email DROP id_in_batch');
    }
}
