<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250807122820 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add force_utf8 flag to email settings';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email_settings ADD force_utf8 TINYINT(1) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email_settings DROP force_utf8');
    }
}
