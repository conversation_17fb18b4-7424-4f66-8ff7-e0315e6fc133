<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250317145229 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove obsolete batch table and add index on sent_at';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE email_batch_deprecated');
        $this->addSql('CREATE INDEX IDX_E7927C7496E4F388 ON email (sent_at)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE email_batch_deprecated (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, service_id VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT \'mailer\' NOT NULL COLLATE `utf8mb4_unicode_ci`, use_failsafe_transport TINYINT(1) NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('DROP INDEX IDX_E7927C7496E4F388 ON email');
    }
}
