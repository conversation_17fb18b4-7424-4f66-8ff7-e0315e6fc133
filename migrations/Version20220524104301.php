<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220524104301 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE email_settings (
          user_id INT NOT NULL,
          transport_scheme VARCHAR(255) NOT NULL,
          email VARCHAR(255) NOT NULL,
          sender_name VARCHAR(255) DEFAULT NULL,
          created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          PRIMARY KEY(user_id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE oauth2_connection (
          user_id INT NOT NULL,
          provider VARCHAR(255) NOT NULL,
          access_token VARCHAR(255) NOT NULL,
          refresh_token VARCHAR(255) NOT NULL,
          expires_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          data LONGTEXT DEFAULT NULL,
          provider_user_id INT NOT NULL,
          provider_nickname VARCHAR(255) DEFAULT NULL,
          provider_name VARCHAR(255) DEFAULT NULL,
          provider_email VARCHAR(255) DEFAULT NULL,
          provider_avatar VARCHAR(255) DEFAULT NULL,
          PRIMARY KEY(user_id),
          CONSTRAINT FK_A0604253A76ED395 FOREIGN KEY (user_id) REFERENCES email_settings (user_id) ON DELETE CASCADE
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE smtp_imap_connection (
          user_id INT NOT NULL,
          smtp_host VARCHAR(255) NOT NULL,
          smtp_port INT NOT NULL,
          username VARCHAR(255) NOT NULL,
          password VARCHAR(255) NOT NULL,
          PRIMARY KEY(user_id),
          CONSTRAINT FK_AA1B83CA76ED395 FOREIGN KEY (user_id) REFERENCES email_settings (user_id) ON DELETE CASCADE
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE smtp_imap_connection');
        $this->addSql('DROP TABLE oauth2_connection');
        $this->addSql('DROP TABLE email_settings');
    }
}
