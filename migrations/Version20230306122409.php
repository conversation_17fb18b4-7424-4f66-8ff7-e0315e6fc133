<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230306122409 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add History Tables';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE email_history (
          id INT AUTO_INCREMENT NOT NULL,
          email_id INT DEFAULT NULL,
          user_id INT DEFAULT NULL,
          service_id VARCHAR(255) DEFAULT NULL,
          uid VARCHAR(255) DEFAULT NULL,
          server VARCHAR(255) NOT NULL,
          created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          email_history_type VARCHAR(255) NOT NULL,
          INDEX IDX_8F3F68C5A832C1C9 (email_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE email_history_error (
          id INT NOT NULL,
          transport_error_type VARCHAR(255) NOT NULL,
          title VARCHAR(1023) NOT NULL,
          description LONGTEXT NOT NULL,
          debug_info LONGTEXT DEFAULT NULL,
          dsn VARCHAR(511) DEFAULT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE email_history_transition (
          id INT NOT NULL,
          from_state VARCHAR(255) NOT NULL,
          to_state VARCHAR(255) NOT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE
          email_history
        ADD
          CONSTRAINT FK_8F3F68C5A832C1C9 FOREIGN KEY (email_id) REFERENCES email (id)');
        $this->addSql('ALTER TABLE
          email_history_error
        ADD
          CONSTRAINT FK_7DA3572CBF396750 FOREIGN KEY (id) REFERENCES email_history (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE
          email_history_transition
        ADD
          CONSTRAINT FK_FD030BE1BF396750 FOREIGN KEY (id) REFERENCES email_history (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE email_history DROP FOREIGN KEY FK_8F3F68C5A832C1C9');
        $this->addSql('ALTER TABLE email_history_error DROP FOREIGN KEY FK_7DA3572CBF396750');
        $this->addSql('ALTER TABLE email_history_transition DROP FOREIGN KEY FK_FD030BE1BF396750');
        $this->addSql('DROP TABLE email_history');
        $this->addSql('DROP TABLE email_history_error');
        $this->addSql('DROP TABLE email_history_transition');
    }
}
