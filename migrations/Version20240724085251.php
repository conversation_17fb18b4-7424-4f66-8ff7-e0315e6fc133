<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240724085251 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add index for email.sent_at';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX IDX_2e44784961bf8fdf ON email (sent_at)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX IDX_2e44784961bf8fdf ON email');
    }
}
