<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240909144349 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename email_batch and move its functional contents to email entity. Also add uuid to email.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email DROP FOREIGN KEY FK_E7927C74F39EBE7A');
        $this->addSql('DROP INDEX IDX_E7927C74F39EBE7A ON email');
        $this->addSql(
            'ALTER TABLE email ADD service_id VARCHAR(255) DEFAULT NULL, ADD use_failsafe_transport SMALLINT DEFAULT NULL'
        );
        $this->addSql(
            'UPDATE email e LEFT JOIN email_batch eb ON e.batch_id = eb.id SET e.service_id = eb.service_id, e.use_failsafe_transport = eb.use_failsafe_transport'
        );
        $this->addSql(
            'ALTER TABLE email DROP batch_id, DROP id_in_batch, CHANGE service_id service_id VARCHAR(255) NOT NULL, CHANGE use_failsafe_transport use_failsafe_transport SMALLINT NOT NULL'
        );
        $this->addSql('RENAME TABLE email_batch TO email_batch_deprecated');
        $this->addSql('DROP INDEX IDX_2e44784961bf8fdf ON email');
        $this->addSql(
            'ALTER TABLE email ADD uuid BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', CHANGE from_address from_address JSON DEFAULT NULL, CHANGE actually_sent_from_address actually_sent_from_address JSON DEFAULT NULL, CHANGE reply_to_address reply_to_address JSON DEFAULT NULL, CHANGE to_addresses to_addresses JSON DEFAULT NULL, CHANGE cc_addresses cc_addresses JSON DEFAULT NULL, CHANGE bcc_addresses bcc_addresses JSON DEFAULT NULL, CHANGE use_failsafe_transport use_failsafe_transport TINYINT(1) NOT NULL'
        );
        $this->addSql('UPDATE email SET uuid = (CONCAT(UNHEX(CONV(ROUND(UNIX_TIMESTAMP(CURTIME(4))*1000), 10, 16)), RANDOM_BYTES(10)))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_E7927C74D17F50A6 ON email (uuid)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX UNIQ_E7927C74D17F50A6 ON email');
        $this->addSql(
            'ALTER TABLE email DROP uuid, CHANGE from_address from_address LONGTEXT DEFAULT NULL, CHANGE actually_sent_from_address actually_sent_from_address LONGTEXT DEFAULT NULL, CHANGE reply_to_address reply_to_address LONGTEXT DEFAULT NULL, CHANGE to_addresses to_addresses LONGTEXT DEFAULT NULL, CHANGE cc_addresses cc_addresses LONGTEXT DEFAULT NULL, CHANGE bcc_addresses bcc_addresses LONGTEXT DEFAULT NULL, CHANGE use_failsafe_transport use_failsafe_transport SMALLINT NOT NULL'
        );
        $this->addSql('RENAME TABLE email_batch_deprecated TO email_batch');
        $this->addSql(
            'ALTER TABLE email ADD batch_id INT DEFAULT NULL, ADD id_in_batch INT DEFAULT NULL, DROP service_id, DROP use_failsafe_transport'
        );
        $this->addSql(
            'ALTER TABLE email ADD CONSTRAINT FK_E7927C74F39EBE7A FOREIGN KEY (batch_id) REFERENCES email_batch (id)'
        );
        $this->addSql('CREATE INDEX IDX_E7927C74F39EBE7A ON email (batch_id)');
    }
}
