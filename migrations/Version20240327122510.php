<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240327122510 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Increase length of smtp password field';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE smtp_imap_connection CHANGE password password VARCHAR(1024) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE smtp_imap_connection CHANGE password password VARCHAR(255) NOT NULL');
    }
}
