<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230328092611 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add history_full view to view the full History (error & transition)';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(sql: <<<SQL
    CREATE VIEW email_history_full AS SELECT *
    FROM email_history
         LEFT JOIN email_history_transition using (id)
         LEFT JOIN email_history_error using (id);
SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP VIEW email_history_full');
    }
}
