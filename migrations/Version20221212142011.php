<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221212142011 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Allows saving system mails whose system users have a string as identifier.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email CHANGE user_id user_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE email_batch CHANGE user_id user_id INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email CHANGE user_id user_id INT NOT NULL');
        $this->addSql('ALTER TABLE email_batch CHANGE user_id user_id INT NOT NULL');
    }
}
