<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220916090829 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE attachment (
          id INT AUTO_INCREMENT NOT NULL,
          email_id INT DEFAULT NULL,
          name VARCHAR(255) NOT NULL,
          content_type VARCHAR(255) NOT NULL,
          body_path VARCHAR(255) DEFAULT NULL,
          INDEX IDX_795FD9BBA832C1C9 (email_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE email (
          id INT AUTO_INCREMENT NOT NULL,
          batch_id INT DEFAULT NULL,
          user_id INT NOT NULL,
          status VARCHAR(32) NOT NULL,
          from_address LONGTEXT DEFAULT NULL,
          actually_sent_from_address LONGTEXT DEFAULT NULL,
          reply_to_address LONGTEXT DEFAULT NULL,
          to_addresses LONGTEXT DEFAULT NULL,
          cc_addresses LONGTEXT DEFAULT NULL,
          bcc_addresses LONGTEXT DEFAULT NULL,
          subject VARCHAR(1023) NOT NULL,
          text_path VARCHAR(255) DEFAULT NULL,
          html_path VARCHAR(255) DEFAULT NULL,
          sent_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          INDEX IDX_E7927C74F39EBE7A (batch_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE email_batch (
          id INT AUTO_INCREMENT NOT NULL,
          user_id INT NOT NULL,
          use_failsafe_transport TINYINT(1) NOT NULL,
          created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE
          attachment
        ADD
          CONSTRAINT FK_795FD9BBA832C1C9 FOREIGN KEY (email_id) REFERENCES email (id)');
        $this->addSql('ALTER TABLE
          email
        ADD
          CONSTRAINT FK_E7927C74F39EBE7A FOREIGN KEY (batch_id) REFERENCES email_batch (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE attachment');
        $this->addSql('DROP TABLE email');
        $this->addSql('DROP TABLE email_batch');
    }
}
