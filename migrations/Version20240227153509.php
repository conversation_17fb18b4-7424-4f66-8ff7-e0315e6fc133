<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240227153509 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add disposition and cid to attachment table for embedded images in emails';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("ALTER TABLE attachment ADD content_disposition VARCHAR(255) DEFAULT 'attachment' NOT NULL");
        $this->addSql('ALTER TABLE attachment ADD content_id VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE attachment DROP content_disposition');
        $this->addSql('ALTER TABLE attachment DROP content_id');
    }
}
