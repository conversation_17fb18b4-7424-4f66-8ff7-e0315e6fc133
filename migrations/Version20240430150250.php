<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240430150250 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add statusCallbackUrl column to email table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email ADD status_callback_url TEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE email DROP status_callback_url');
    }
}
