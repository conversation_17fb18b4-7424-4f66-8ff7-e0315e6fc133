<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240108160615 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Recreate IDX and FK for email_id on email_history to satisfy doctrine '. PHP_EOL .
            'insisting on a different hash and to prevent those changes to slide into the next migration via diff.';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE email_history DROP FOREIGN KEY FK_8F3F68C5A832C1C9');
        $this->addSql('DROP INDEX idx_8f3f68c5a832c1c9 ON email_history');
        $this->addSql('CREATE INDEX IDX_9A7A1884A832C1C9 ON email_history (email_id)');
        $this->addSql('ALTER TABLE email_history ADD CONSTRAINT FK_8F3F68C5A832C1C9 FOREIGN KEY (email_id) REFERENCES email (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE email_history DROP FOREIGN KEY FK_9A7A1884A832C1C9');
        $this->addSql('DROP INDEX idx_9a7a1884a832c1c9 ON email_history');
        $this->addSql('CREATE INDEX IDX_8F3F68C5A832C1C9 ON email_history (email_id)');
        $this->addSql('ALTER TABLE email_history ADD CONSTRAINT FK_9A7A1884A832C1C9 FOREIGN KEY (email_id) REFERENCES email (id)');
    }
}
