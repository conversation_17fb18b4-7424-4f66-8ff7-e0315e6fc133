<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220601070739 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE
          oauth2_connection
        CHANGE
          access_token access_token TEXT NOT NULL,
        CHANGE
          refresh_token refresh_token TEXT NOT NULL,
        CHANGE
          provider_user_id provider_user_id VARCHAR(255) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE
          oauth2_connection
        CHANGE
          access_token access_token VARCHAR(255) NOT NULL,
        CHANGE
          refresh_token refresh_token VARCHAR(255) NOT NULL,
        CHANGE
          provider_user_id provider_user_id INT NOT NULL');
    }
}
