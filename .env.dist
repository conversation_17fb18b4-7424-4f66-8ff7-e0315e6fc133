# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=ba88c195a0c9366ffc27b1ba93375624
###< symfony/framework-bundle ###

### MYSQL:
MYSQL_ROOT_PASSWORD=root
DB_DATABASE=mailer
DB_USERNAME=mailer
DB_PASSWORD=demv
DB_HOST=mariadb_mailer
DB_PORT=3306

### DOCKER:
USER_ID=
GROUP_ID=
GITHUB_TOKEN=
###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
#
# DATABASE_URL="sqlite:///%kernel.project_dir%/var/data.db"
DATABASE_URL="mysql://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}?serverVersion=10.5.15-MariaDB&charset=utf8mb4"
# DATABASE_URL="postgresql://symfony:ChangeMe@127.0.0.1:5432/app?serverVersion=13&charset=utf8"
###< doctrine/doctrine-bundle ###

###> symfony/mailer ###
SYSTEM_MAILER_DSN="smtp://mailpit:1025"
FALLBACK_MAILER_SENDER="Fallback Sender <<EMAIL>>"
FALLBACK_MAILER_RECIPIENT="Fallback Recipient <<EMAIL>>"
# in production environment the system transport is only used for system users like other services
ALWAYS_SEND_WITH_SYSTEM_TRANSPORT=false
# time after which sending of a batch is canceled (in seconds)
BATCH_TIMEOUT=40
QUEUE_SEND_TIMEOUT=55
###< symfony/mailer ###

###> sentry/sentry-symfony ###
SENTRY_DSN=
###< sentry/sentry-symfony ###

###> google/apiclient ###
GOOGLE_API_KEY=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
###< google/apiclient ###

###> Microsoft Api
MICROSOFT_OAUTH_CLIENT_ID=
MICROSOFT_OAUTH_CLIENT_SECRET=
###< Microsoft Api

###> aws/aws-sdk-php-symfony ###
AWS_KEY=minio
AWS_SECRET=minio123
AWS_DEFAULT_REGION=eu-central-1
AWS_URL=http://s3.mailer.demv.internal
AWS_ENDPOINT=mailer_s3:9000
AWS_BUCKET=demv-dev-mailer
###< aws/aws-sdk-php-symfony ###

###> lexik/jwt-authentication-bundle ###
PW_PUBLIC_KEY=%kernel.project_dir%/config/jwt/pw_public.pem
###< lexik/jwt-authentication-bundle ###

# PW API Token
PW_ADMIN_TOKEN=

###> symfony/messenger ###
MESSENGER_TRANSPORT_DSN=redis://redis:6379/messages

FORCED_MAILER_RECIPIENT=''
