<?php

declare(strict_types=1);

namespace App\Gateway;

use Demv\ProfessionalworksSdk\Gateway\ProfessionalworksGateway;
use Demv\SdkFramework\Gateway\GatewayInterface;

readonly class ProfessionalworksGatewayFactory
{
    /**
     * @param array<string, mixed> $options
     */
    public function __construct(
        protected string $baseUrl,
        protected string $origin,
        protected string $apiToken,
        private string $proxyAuthUser = '',
        private string $proxyAuthPassword = '',
        private string $proxyBaseUrl = '',
        protected array $options = [],
    ) {
    }

    public function createForSystem(): GatewayInterface
    {
        return new ProfessionalworksGateway(
            baseUrl: $this->baseUrl,
            token: $this->apiToken,
            origin: $this->origin,
            proxyAuthUser: $this->proxyAuthUser,
            proxyAuthPassword: $this->proxyAuthPassword,
            proxyBaseUrl: $this->proxyBaseUrl,
        );
    }
}
