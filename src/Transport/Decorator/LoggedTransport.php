<?php

declare(strict_types=1);

namespace App\Transport\Decorator;

use App\Email\Provider\ProviderConfigInterface;
use App\Transport\TransportInterface;
use App\Util\EmailHistoryLogger;
use Exception;
use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mime\RawMessage;
use Throwable;

class LoggedTransport extends AbstractTransportDecorator
{
    private const SENT_MESSAGE_NULL = 'SentMessage should never be `null`';

    public function __construct(TransportInterface $inner, private EmailHistoryLogger $logger)
    {
        parent::__construct($inner);
    }

    /**
     * @throws Throwable
     */
    public function send(RawMessage $message, Envelope $envelope = null): SentMessage
    {
        try {
            $sentMessage = $this->inner->send($message, $envelope);
        } catch (Throwable $e) {
            $this->logger->transportErrorFromThrowable($e, $this->inner);

            throw $e;
        }

        if ($sentMessage === null) {
            $e = new Exception(self::SENT_MESSAGE_NULL);
            $this->logger->transportErrorFromThrowable($e, $this->inner);

            throw $e;
        }

        return $sentMessage;
    }

    public function getProviderConfig(): ?ProviderConfigInterface
    {
        return $this->inner->getProviderConfig();
    }

    public function __toString(): string
    {
        return "LoggedTransport({$this->inner})";
    }
}
