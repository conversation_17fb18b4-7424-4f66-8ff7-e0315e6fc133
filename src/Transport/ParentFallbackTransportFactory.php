<?php

declare(strict_types=1);

namespace App\Transport;

use App\Error\ProcessErrorType;
use App\Repository\EmailSettingsRepository;
use App\Security\UserInterface;
use App\Service\EmailTransportService;
use App\Service\UserInfoServiceInterface;
use App\Util\EmailHistoryLogger;
use App\Util\ExceptionHelper;
use Exception;
use Symfony\Component\Mime\Address;

class ParentFallbackTransportFactory
{
    public function __construct(
        private readonly UserInfoServiceInterface $userInfoService,
        private readonly EmailTransportService $emailTransportService,
        private readonly EmailSettingsRepository $emailSettingsRepository,
        private readonly EmailHistoryLogger $emailHistoryLogger,
    ) {
    }

    public function createForUser(UserInterface $user): ?TransportInterface
    {
        try {
            $userInfo = $this->userInfoService->getUserInfo($user);
        } catch (Exception $e) {
            $this->emailHistoryLogger->processError(
                ProcessErrorType::USER_INFO_NOT_OBTAINABLE,
                ExceptionHelper::getMessageChain($e)
            );

            return null;
        }

        foreach ($userInfo->parentUserIds as $parentUserId) {
            $parentEmailSettings = $this->emailSettingsRepository->find($parentUserId);
            $transport = $this->emailTransportService
                ->createCheckAndUpdateTransportFromEmailSettings($parentEmailSettings);

            if ($transport === null || $parentEmailSettings === null) {
                continue;
            }

            $newSender = new Address($parentEmailSettings->getEmail(), $parentEmailSettings->getSenderName() ?? '');

            return new ParentFallbackTransport(
                new SenderAdjustingTransport($transport, $newSender),
                $this->emailHistoryLogger
            );
        }

        return null;
    }
}
