<?php

declare(strict_types=1);

namespace App\Transport;

use Symfony\Component\Mailer\Transport\Dsn;

interface TransportFactoryInterface
{
    public function fromDsnString(string $dsn): TransportInterface;

    /**
     * @param string[] $dsns
     *
     * @return TransportInterface[]
     */
    public function fromDsnStrings(array $dsns): array;

    public function fromDsnObject(Dsn $dsn): TransportInterface;
}
