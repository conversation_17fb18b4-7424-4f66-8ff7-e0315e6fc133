<?php

declare(strict_types=1);

namespace App\Transport;

use Symfony\Component\Mailer\Transport;
use Symfony\Component\Mailer\Transport\Dsn;

class TransportFactoryAdapter implements TransportFactoryInterface
{
    public function __construct(private readonly Transport $transportFactory)
    {
    }

    public function fromDsnString(string $dsn): TransportInterface
    {
        $transport = $this->transportFactory->fromString($dsn);

        if (!$transport instanceof TransportInterface) {
            $transport = new SymfonyTransportAdapter($transport);
        }

        return $transport;
    }

    public function fromDsnStrings(array $dsns): array
    {
        return array_map($this->fromDsnString(...), $dsns);
    }

    public function fromDsnObject(Dsn $dsn): TransportInterface
    {
        if (!($transport = $this->transportFactory->fromDsnObject($dsn)) instanceof TransportInterface) {
            $transport = new SymfonyTransportAdapter($transport);
        }

        return $transport;
    }
}
