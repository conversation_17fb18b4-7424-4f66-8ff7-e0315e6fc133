<?php

declare(strict_types=1);

namespace App\Transport;

use App\Email\Provider\ProviderConfigInterface;
use App\Error\ProcessErrorType;
use App\Transport\Decorator\AbstractTransportDecorator;
use App\Util\EmailHistoryLogger;
use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mime\RawMessage;

class ParentFallbackTransport extends AbstractTransportDecorator
{
    public function __construct(TransportInterface $inner, private readonly EmailHistoryLogger $emailHistoryLogger)
    {
        parent::__construct($inner);
    }

    public function send(RawMessage $message, Envelope $envelope = null): ?SentMessage
    {
        $this->emailHistoryLogger->processError(
            ProcessErrorType::FALLBACK_TO_PARENT_USER,
            'Für den Nutzer kann aktuell keine Email versendet werden.'
            . ' Es wird versucht, die Email über einen dem Nutzer zugehörigen anderen <PERSON>er zu versenden.'
        );

        return $this->inner->send($message, $envelope);
    }

    public function getProviderConfig(): ?ProviderConfigInterface
    {
        return $this->inner->getProviderConfig();
    }

    public function __toString(): string
    {
        return "ParentTransport({$this->inner})";
    }
}
