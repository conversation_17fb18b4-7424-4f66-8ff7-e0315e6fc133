<?php

declare(strict_types=1);

namespace App\Transport;

use App\Transport\Decorator\LoggedTransport;
use App\Util\EmailHistoryLogger;
use Symfony\Component\Mailer\Transport\Dsn;

class LoggedTransportFactory implements TransportFactoryInterface
{
    public function __construct(
        private readonly TransportFactoryInterface $inner,
        private readonly EmailHistoryLogger $logger
    ) {
    }

    public function fromDsnString(string $dsn): TransportInterface
    {
        return new LoggedTransport($this->inner->fromDsnString($dsn), $this->logger);
    }

    public function fromDsnStrings(array $dsns): array
    {
        return array_map($this->fromDsnString(...), $dsns);
    }

    public function fromDsnObject(Dsn $dsn): TransportInterface
    {
        return new LoggedTransport($this->inner->fromDsnObject($dsn), $this->logger);
    }
}
