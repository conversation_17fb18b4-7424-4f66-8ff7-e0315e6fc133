<?php

declare(strict_types=1);

namespace App\Transport;

use App\Email\Provider\ProviderConfigInterface;
use App\Transport\Decorator\AbstractTransportDecorator;
use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mailer\Exception\TransportException;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\RawMessage;

class SenderAdjustingTransport extends AbstractTransportDecorator
{
    public function __construct(TransportInterface $inner, private readonly Address $newSender)
    {
        parent::__construct($inner);
    }

    public function send(RawMessage $message, Envelope $envelope = null): ?SentMessage
    {
        if (!$message instanceof Email) {
            throw new TransportException(
                'Message cannot be prepared for sending with new sender. ' .
                'Must be of type ' . Email::class . '.'
            );
        }

        if ($message->getReplyTo() === []) {
            $message->replyTo(...$message->getFrom());
        }

        $message->from($this->newSender);

        return $this->inner->send($message, $envelope);
    }

    public function getProviderConfig(): ?ProviderConfigInterface
    {
        return $this->inner->getProviderConfig();
    }

    public function __toString(): string
    {
        return "SenderAdjusting({$this->inner})";
    }
}
