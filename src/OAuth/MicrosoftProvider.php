<?php

declare(strict_types=1);

namespace App\OAuth;

use App\Entity\OAuth2Connection;
use App\Util\CloneOnCall;
use Carbon\Carbon;
use Doctrine\ORM\EntityManagerInterface;
use Microsoft\Graph\Graph;
use TheNetworg\OAuth2\Client\Provider\Azure;

class MicrosoftProvider
{
    /** @var CloneOnCall<Azure> */
    private CloneOnCall $azure;

    public function __construct(private readonly EntityManagerInterface $em, string $clientId, string $clientSecret)
    {
        $this->azure = new CloneOnCall(new Azure([
            'clientId' => $clientId,
            'clientSecret' => $clientSecret,
            'redirectUri' => '',
            'defaultEndPointVersion' => '2.0',
            'scopes' => ['openId'],
        ]));
    }

    public function refreshAccessToken(string $refreshToken): string
    {
        return $this->azure->getAccessToken('refresh_token', ['refresh_token' => $refreshToken])->getToken();
    }

    public function refreshAccessTokenForOAuth2Connection(OAuth2Connection &$oAuth2Connection): ?string
    {
        if (
            $oAuth2Connection->getAccessToken() !== null
            && $oAuth2Connection->getExpiresAt() !== null
            && $oAuth2Connection->getExpiresAt() > Carbon::now()->addMinute()->toDateTimeImmutable()
        ) {
            return $oAuth2Connection->getAccessToken();
        }

        $refreshToken = $oAuth2Connection->getRefreshToken();

        if ($refreshToken === null || $refreshToken === '') {
            throw new \Exception('Refresh token cannot be empty.');
        }

        $accessToken = $this->azure->getAccessToken(
            'refresh_token',
            ['refresh_token' => $refreshToken]
        );

        $oAuth2Connection->setAccessToken($accessToken->getToken());
        $oAuth2Connection->setExpiresAt(
            Carbon::createFromTimestamp($accessToken->getExpires() ?? 0)->toDateTimeImmutable()
        );
        $oAuth2Connection->setRefreshToken($accessToken->getRefreshToken() ?? $refreshToken);

        $this->em->flush();

        return $oAuth2Connection->getAccessToken();
    }

    public function getGraphForConnection(OAuth2Connection $oAuth2Connection): Graph
    {
        $graph = new Graph();
        $accessToken = $this->refreshAccessTokenForOAuth2Connection($oAuth2Connection);

        if ($accessToken === null) {
            throw new \Exception('No Valid AccessToken avaliable');
        }

        $graph->setAccessToken($accessToken);

        return $graph;
    }

    public function refreshAccessTokenForUserId(int $userId): ?string
    {
        $oAuth2Connection = $this->em->find(OAuth2Connection::class, $userId);

        if ($oAuth2Connection === null || $oAuth2Connection->getProvider() !== 'microsoft') {
            throw new \Exception('No Connection found for User');
        }

        return $this->refreshAccessTokenForOAuth2Connection($oAuth2Connection);
    }
}
