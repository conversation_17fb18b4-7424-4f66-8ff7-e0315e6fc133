<?php

declare(strict_types=1);

namespace App\OAuth;

use App\Entity\OAuth2Connection;
use Carbon\Carbon;
use Doctrine\ORM\EntityManagerInterface;
use Google\Client;

class GoogleProvider
{
    private readonly Client $googleClient;

    public function __construct(private readonly EntityManagerInterface $em, string $clientId, string $clientSecret)
    {
        $this->googleClient = new Client(
            [
                'client_id' => $clientId,
                'client_secret' => $clientSecret,
            ]
        );
    }

    public function getClientForConnection(OAuth2Connection $oAuth2Connection): Client
    {
        $googleClient = clone $this->googleClient;

        $accessToken = $this->refreshAccessTokenForOAuth2Connection($oAuth2Connection, $googleClient);

        if ($accessToken === null) {
            throw new \Exception('Access token could not be refreshed for OAuth2 connection');
        }

        $googleClient->setAccessToken($accessToken);

        return $googleClient;
    }

    public function getCheckedClientForUserId(int $userId): Client
    {
        $oAuth2Connection = $this->em->find(OAuth2Connection::class, $userId);

        if ($oAuth2Connection === null || $oAuth2Connection->getProvider() !== 'google') {
            throw new \Exception('No Connection found for User');
        }

        $googleClient = clone $this->googleClient;

        $accessToken = $this->refreshAccessTokenForOAuth2Connection($oAuth2Connection, $googleClient);

        if ($accessToken === null) {
            throw new \Exception('Access token could not be refreshed');
        }

        $googleClient->setAccessToken($accessToken);

        return $googleClient;
    }

    private function refreshAccessTokenForOAuth2Connection(
        OAuth2Connection &$oAuth2Connection,
        Client $googleClient
    ): ?string {
        if (
            $oAuth2Connection->getAccessToken() !== null
            && $oAuth2Connection->getExpiresAt() !== null
            && $oAuth2Connection->getExpiresAt() > Carbon::now()->addMinute()->toDateTimeImmutable()
        ) {
            return $oAuth2Connection->getAccessToken();
        }

        $accessTokenArray = $googleClient->fetchAccessTokenWithRefreshToken($oAuth2Connection->getRefreshToken());

        if ($accessTokenArray['access_token'] === null) {
            throw new \Exception('The access token is null after fetching with refresh token');
        }

        $oAuth2Connection->setAccessToken($accessTokenArray['access_token']);
        $oAuth2Connection->setExpiresAt(
            Carbon::now()->addSeconds($accessTokenArray['expires_in'] ?? 0)->toDateTimeImmutable()
        );
        $this->em->flush();

        return $oAuth2Connection->getAccessToken();
    }
}
