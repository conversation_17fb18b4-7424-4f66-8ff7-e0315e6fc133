<?php

declare(strict_types=1);

namespace App\Service;

use Psr\Container\ContainerInterface;

/**
 * We use this class to monitor the number of messages in our queue. This allows us to automatically send out
 * alerts if the queues fill up (e.g. emails can't be sent or workers could not be spawned for a while)
 */
readonly class QueueStatsService
{
    public function __construct(private ContainerInterface $receiverLocator)
    {
    }

    /**
     * @return array<string, int>
     *
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function getQueueStats(): array
    {
        $transportNames = [
            'persist_email',
            'send_email',
            'failed',
        ];
        $stats = [];

        foreach ($transportNames as $transportName) {
            /** @var \Symfony\Component\Messenger\Bridge\Redis\Transport\RedisTransport $transport */
            $transport = $this->receiverLocator->get($transportName);
            $stats[$transportName] = $transport->getMessageCount();
        }

        return $stats;
    }
}
