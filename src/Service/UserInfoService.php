<?php

declare(strict_types=1);

namespace App\Service;

use App\DTO\UserInfo;
use App\Gateway\ProfessionalworksGatewayFactory;
use App\Security\PwUser;
use App\Security\UserInterface;
use Exception;

class UserInfoService implements UserInfoServiceInterface
{
    /** @var array<int, UserInfo> */
    private static array $userInfoCache;

    public function __construct(private readonly ProfessionalworksGatewayFactory $gatewayFactory)
    {
    }

    public function getUserInfo(UserInterface $user): UserInfo
    {
        if (!$user instanceof PwUser) {
            throw new Exception('User must be an instance of PwUser');
        }

        // The UserInfoCache should be replaced with a proper redis cache at some point
        //   as this cache only works for the current php process.
        if (isset(self::$userInfoCache[$user->getUserId()])) {
            return self::$userInfoCache[$user->getUserId()];
        }

        $gateway = $this->gatewayFactory->createForSystem();

        $response = $gateway->get('/api/mailer/forUser/' . $user->getUserId() . '/user');

        if ($response->getStatusCode() !== 200) {
            throw new Exception('Error while fetching user info');
        }

        $responseAsArray = json_decode($response->getBody()->getContents(), true);

        if (
            !is_array($responseAsArray)
            || ($responseAsArray['status'] ?? '') !== 'success'
            || !is_numeric($responseAsArray['data']['userId'] ?? null)
            || !is_string($responseAsArray['data']['emailAddress'] ?? null)
            || !is_array($responseAsArray['data']['parentUserIds'] ?? null)
        ) {
            throw new Exception('Error while fetching user info');
        }

        $userInfo = new UserInfo(
            userid: (int) $responseAsArray['data']['userId'],
            parentUserIds: $responseAsArray['data']['parentUserIds'],
            email: $responseAsArray['data']['emailAddress']
        );

        self::$userInfoCache[$user->getUserId()] = $userInfo;

        return $userInfo;
    }
}
