<?php

declare(strict_types=1);

namespace App\Service;

use App\Repository\EmailSettingsRepository;
use App\Util\EmailHistoryLogger;
use Carbon\Carbon;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Exception;

readonly class EmailSettingsService
{
    public function __construct(
        private readonly EmailSettingsRepository $emailSettingsRepository,
        private readonly ProfessionalWorksEventService $eventService,
        private readonly EmailHistoryLogger $logger
    ) {
    }

    public function fetchAndDelete(Carbon $fromDate): void
    {
        $eventParams = [
            ProfessionalWorksEventService::EVENT_TYPE_USER_DELETED,
            ProfessionalWorksEventService::EVENT_TYPE_USER_SOFT_DELETED,
        ];

        try {
            $events = $this->eventService->getUserEvents($fromDate, $eventParams);
        } catch (Exception $e) {
            $this->logger->unknownError($e->getMessage());

            return;
        }

        foreach ($events as $event) {
            if (!in_array($event->eventType, $eventParams, true)) {
                continue;
            }

            try {
                $entity = $this->emailSettingsRepository->find($event->userId);

                if ($entity === null) {
                    $this->logger->unknownError("Expected entity for user id {$event->userId} not found.");

                    continue;
                }

                if ($this->emailSettingsRepository->isDeleteLockFree()) {
                    throw new \Exception(
                        'EmailsSettingsRepository::remove is not concurrency safe.
                        You need to get the delete lock first!'
                    );
                }

                $this->emailSettingsRepository->remove($entity, true);
            } catch (OptimisticLockException | ORMException $e) {
                $this->logger->unknownError($e->getMessage());
            }
        }
    }
}
