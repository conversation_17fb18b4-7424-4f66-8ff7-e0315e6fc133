<?php

declare(strict_types=1);

namespace App\Service;

use App\DTO\Service\RateLimit\MaxSmtpConnectionErrorsPerDay;
use App\DTO\Service\RateLimit\MaxUserEmailsPerMinutes;
use App\DTO\Service\RateLimit\UserSmtpRateLimitError;
use App\Email\Provider\ProviderConfigInterface;
use App\Entity\Email;
use App\Entity\EmailHistoryError;
use App\Util\EmailHistoryLogger;
use Doctrine\ORM\EntityManagerInterface;
use Redis;

readonly class RateLimitService
{
    public function __construct(
        private EmailHistoryLogger $logger,
        private EntityManagerInterface $entityManager,
        // this redis instance uses db 1, see services.yml
        private Redis $redis,
    ) {
    }

    public function getNextRetryTime(?ProviderConfigInterface $providerConfig, ?int $userId): ?\DateTimeImmutable
    {
        if ($userId === null) {
            // we are sending as a system user, so no need to check any rate limits
            return null;
        }

        $rateLimits = $providerConfig?->getRateLimits() ?? [];

        foreach ($rateLimits as $limit) {
            $retryTime = match (true) {
                $limit instanceof MaxUserEmailsPerMinutes => $this->resolveMaxUserEmailsPerMinutes($limit, $userId),
                $limit instanceof MaxSmtpConnectionErrorsPerDay => $this->resolveMaxSmtpConnectionErrorsPerDay($limit),
                $limit instanceof UserSmtpRateLimitError => $this->resolveUserSmtpRateLimitError($userId),
                default => null,
            };

            if ($retryTime !== null) {
                return $retryTime;
            }
        }

        return null;
    }

    private function resolveMaxUserEmailsPerMinutes(
        MaxUserEmailsPerMinutes $rateLimit,
        int $userId
    ): ?\DateTimeImmutable {
        $emailEntityRepository = $this->entityManager->getRepository(Email::class);
        $sentEmailCount = $emailEntityRepository->countEmailsSentInTimespanByUser(
            $userId,
            $rateLimit->minutes
        );

        if ($sentEmailCount->count >= $rateLimit->limit) {
            $this->logger->warning(
                sprintf(
                    'Rate limit reached: %d of max %d emails in %d minute(s)',
                    $sentEmailCount->count,
                    $rateLimit->limit,
                    $rateLimit->minutes
                )
            );
            /** @var \DateTimeImmutable $lastSentTime */
            $lastSentTime = $sentEmailCount->lastSentTime;

            $retryTime = $lastSentTime->modify('+' . $rateLimit->minutes . ' minutes');

            if ($retryTime < (new \DateTimeImmutable())) {
                $retryTime = (new \DateTimeImmutable('+1 minute'));
            }

            return $retryTime;
        }

        return null;
    }

    private function resolveMaxSmtpConnectionErrorsPerDay(
        MaxSmtpConnectionErrorsPerDay $rateLimit
    ): ?\DateTimeImmutable {
        $emailHistoryErrorRepository = $this->entityManager->getRepository(EmailHistoryError::class);
        $smtpErrorLoginCount = $emailHistoryErrorRepository->countFailedSmtpLogins($rateLimit->dsn);

        if ($smtpErrorLoginCount >= $rateLimit->limit) {
            $this->logger->warning(
                sprintf('Rate limit reached: %d connection errors for today', $smtpErrorLoginCount)
            );

            return (new \DateTimeImmutable())->modify('next day midnight');
        }

        return null;
    }

    private function resolveUserSmtpRateLimitError(int $userId): ?\DateTimeImmutable
    {
        /** @var string|false $lastRateLimitErrorAt */
        $lastRateLimitErrorAt = $this->redis->get((string) $userId);

        if ($lastRateLimitErrorAt === false) {
            return null;
        }

        try {
            $lastRateLimitErrorAt = new \DateTimeImmutable($lastRateLimitErrorAt);
        } catch (\Exception $e) {
            // For some reason the value in Redis was not a valid datetime
            $lastRateLimitErrorAt = new \DateTimeImmutable();
        }

        return $lastRateLimitErrorAt->modify('+1 hour');
    }

    public function setUserSmtpRateLimitError(int $userId, int $expireInSeconds = 3600): void
    {
        $this->redis->set(
            (string) $userId,
            (new \DateTimeImmutable())->format('c'),
            // NX: Only set the key if it does not already exist.
            // EX: Set the specified expire time, in seconds.
            // KEEPTTL: Retain the time to live associated with the key.
            ['NX', 'EX' => $expireInSeconds, 'KEEPTTL' => true]
        );
    }
}
