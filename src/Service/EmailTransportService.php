<?php

declare(strict_types=1);

namespace App\Service;

use App\Entity\EmailSettings;
use App\Entity\OAuth2Connection;
use App\Entity\SmtpImapConnection;
use App\Enum\ConnectionStatus;
use App\Error\ConnectionError;
use App\Error\ConnectionErrorType;
use App\Logging\ErrorLogContext;
use App\Repository\EmailSettingsRepository;
use App\Security\PwUser;
use App\Security\SystemUser;
use App\Security\UserInterface;
use App\Transport\TransportFactoryInterface;
use App\Transport\TransportInterface;
use App\Util\ExceptionHelper;
use App\Util\Result;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\Transport\Dsn;

class EmailTransportService
{
    private const MAX_TRANSPORTS_IN_CACHE = 3;

    /** @var TransportInterface[] */
    private static array $transportCache = [];

    public function __construct(
        private readonly TransportFactoryInterface $transportFactory,
        private readonly TransportInterface $systemTransport,
        private readonly TransportInterface $fallbackTransport,
        private readonly bool $alwaysSendWithSystemTransport,
        private readonly EmailSettingsRepository $emailSettingsRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly LoggerInterface $logger,
    ) {
    }

    public function getCheckAndUpdateTransportForUser(UserInterface $user): ?TransportInterface
    {
        $userIdentifier = $user->getServiceId() . '-' . $user->getUserId();

        if (isset(self::$transportCache[$userIdentifier])) {
            return self::$transportCache[$userIdentifier];
        }

        $transport = match (true) {
            $user instanceof PwUser => $this->createCheckAndUpdateUserTransport($user),
            $user instanceof SystemUser => $this->systemTransport,
            default => throw new Exception('No transport creation configured for {$user::class}.')
        };

        if ($transport !== null) {
            self::$transportCache[$userIdentifier] = $transport;
        }

        if (count(self::$transportCache) > self::MAX_TRANSPORTS_IN_CACHE) {
            array_shift(self::$transportCache);
        }

        return $transport;
    }

    /**
     * Returns a fallback transport for failing user transports
     *
     * This transport may adjust the sender address and reply-to headers to comply with SPF/DKIM/DMARC rules.
     *
     * @return TransportInterface
     */
    public function getFallbackTransport(): TransportInterface
    {
        return $this->fallbackTransport;
    }

    public static function emptyTransportCache(): void
    {
        self::$transportCache = [];
    }

    private function createCheckAndUpdateUserTransport(PwUser $user): ?TransportInterface
    {
        // if we can't load settings for a user, we try to send with fallback transport later
        $emailSettings = $this->emailSettingsRepository->find($user->getUserId());

        return $this->createCheckAndUpdateTransportFromEmailSettings($emailSettings);
    }

    private function checkAndUpdateSending(
        EmailSettings $emailSettings,
        TransportInterface $transport
    ): ?TransportInterface {
        try {
            $resultSending = $transport->testConnection();
        } catch (\Exception $e) {
            $resultSending = Result::err(ExceptionHelper::getMessageChain($e));
        }

        $emailSettings->setSendingStatus(
            $resultSending->isOk()
                ? ConnectionStatus::ACTIVE
                : ConnectionStatus::FAILED
        );

        if ($resultSending->isOk()) {
            return $transport;
        }

        $logContext = new ErrorLogContext(
            error: new ConnectionError(ConnectionErrorType::SENDING, $resultSending->getError()),
            transport: (string) $transport,
        );
        $this->logger->error('', context: [ErrorLogContext::IDENTIFIER => $logContext]);

        return null;
    }

    public function createCheckAndUpdateTransportFromEmailSettings(?EmailSettings $emailSettings): ?TransportInterface
    {
        if ($this->alwaysSendWithSystemTransport) {
            return $this->systemTransport;
        }

        if (
            $emailSettings === null
            || !$emailSettings->canSend()
            || $emailSettings->getSendingStatus() !== ConnectionStatus::ACTIVE
        ) {
            return null;
        }

        $transport = $this->transportFactory->fromDsnObject($this->convertEmailSettingsToDsn($emailSettings));

        $this->checkAndUpdateSending($emailSettings, $transport);
        $this->entityManager->flush();

        /** @phpstan-ignore-next-line */
        if ($emailSettings->getSendingStatus() !== ConnectionStatus::ACTIVE) {
            return null;
        }

        return $transport;
    }

    private function convertEmailSettingsToDsn(EmailSettings $settings): Dsn
    {
        if ($settings instanceof SmtpImapConnection) {
            return new Dsn(
                $settings->getTransportScheme()->value,
                $settings->getSmtpHost(),
                $settings->getUsername(),
                $settings->getPassword(),
                $settings->getSmtpPort(),
            );
        }

        if ($settings instanceof OAuth2Connection) {
            $tokenData = json_decode($settings->getData() ?? '', associative: true);

            $scope = is_array($tokenData) && isset($tokenData['scope']) ? $tokenData['scope'] : '';

            return new Dsn(
                $settings->getTransportScheme()->value,
                $settings->getProvider(),
                options: [
                    'provider' => $settings->getProvider(),
                    'access_token' => $settings->getAccessToken(),
                    'refresh_token' => $settings->getRefreshToken(),
                    'scope' => $scope,
                    'expires_at' => $settings->getExpiresAt(),
                    'provider_user_id' => $settings->getProviderUserId(),
                    'provider_nickname' => $settings->getProviderNickname(),
                    'provider_name' => $settings->getProviderName(),
                    'provider_email' => $settings->getProviderEmail(),
                    'user_id' => $settings->getUserId(),
                ]
            );
        }

        throw new Exception('Cannot convert connection to DSN');
    }
}
