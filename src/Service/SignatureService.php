<?php

declare(strict_types=1);

namespace App\Service;

use App\Util\Result;
use Exception;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class SignatureService
{
    private const HOST_PATTERN = '/^sns\.eu-central-1\.amazonaws\.com$/';

    public function __construct(private readonly HttpClientInterface $httpClient)
    {
    }

    /**
     * @return Result<SignedMessageInterface, string>
     */
    public function validate(SignedMessageInterface $signedMessage): Result
    {
        $certUrl = $signedMessage->getCertificateURL();
        $this->validateUrl($certUrl);

        $certificate = $this->httpClient->request('GET', $signedMessage->getCertificateURL())->getContent();
        $sesPublicKey = openssl_pkey_get_public($certificate);

        if ($sesPublicKey === false) {
            return Result::err(sprintf(
                'Public key could not be extracted from certificate for notification %s',
                $signedMessage->getIdentifier()
            ));
        }

        $messageSignFormat = $signedMessage->getSignFormat();
        $signature = base64_decode($signedMessage->getSignature(), true);

        if ($signature === false) {
            return Result::err(sprintf(
                'Signature of notification %s is not properly base64 encoded.',
                $signedMessage->getIdentifier()
            ));
        }

        $algo = $signedMessage->getHashAlgorithm();

        return openssl_verify($messageSignFormat, $signature, $sesPublicKey, $algo->name) === 1
            ? Result::ok($signedMessage)
            : Result::err(sprintf(
                'Notification %s has no valid signature.',
                $signedMessage->getIdentifier()
            ));
    }

    private function validateUrl(string $certUrl): void
    {
        $parsed = parse_url($certUrl);

        if (
            !isset($parsed['scheme'])
            || !isset($parsed['host'])
            || $parsed['scheme'] !== 'https'
            || substr($certUrl, -4) !== '.pem'
            || preg_match(self::HOST_PATTERN, $parsed['host']) !== 1
        ) {
            throw new Exception(
                'The SNS certificate is located on an invalid domain.'
            );
        }
    }
}
