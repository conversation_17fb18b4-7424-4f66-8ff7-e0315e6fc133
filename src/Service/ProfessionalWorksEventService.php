<?php

declare(strict_types=1);

namespace App\Service;

use App\DTO\UserEvent;
use App\Gateway\ProfessionalworksGatewayFactory;
use Carbon\Carbon;
use Exception;

class ProfessionalWorksEventService
{
    public const BASE_URL = '/events/event/events';
    public const EVENT_TYPE_USER_DELETED = 'user_deleted';
    public const EVENT_TYPE_USER_SOFT_DELETED = 'user_soft_deleted';

    public function __construct(private readonly ProfessionalworksGatewayFactory $gatewayFactory)
    {
    }

    /**
     * @param array<string>|null $eventTypes
     *
     * @return array<UserEvent>
     *
     * @throws Exception
     */
    public function getUserEvents(Carbon $fromDate, array $eventTypes = null): array
    {
        $params = [];
        $params['fromDate'] = urlencode($fromDate->toDateTimeString());

        if ($eventTypes !== null) {
            $params['types'] = $eventTypes;
        }

        $gateway = $this->gatewayFactory->createForSystem();
        $response = $gateway->get(self::BASE_URL, $params);

        if ($response->getStatusCode() !== 200) {
            throw new Exception("Error while fetching user account events: " . $response->getStatusCode());
        }

        $data = json_decode($response->getBody()->getContents(), true);

        if (!is_array($data)) {
            throw new Exception("Error decoding response: ");
        }

        if (json_last_error() !== 0) {
            throw new Exception("Error decoding response: " . json_last_error_msg());
        }

        if (($data['status'] ?? '') !== 'success') {
            throw new Exception("Error in status, did not receive a success: " . $data['status']);
        }

        if (!isset($data['data'])) {
            throw new Exception("Error in response, did not receive data");
        }

        $events = [];

        foreach ($data['data'] as $event) {
            if (!isset($event['type'], $event['payload']['id'], $event['created_at'])) {
                throw new Exception("Error in response, did not receive expected keys. Given keys: "
                                    . print_r(array_keys($event), true));
            }

            $date = Carbon::createFromFormat('Y-m-d H:i:s', $event['created_at']);
            $events[] = new UserEvent(
                eventType: $event['type'],
                userId: $event['payload']['id'],
                createdAt: $date instanceof Carbon ? $date : null
            );
        }

        return $events;
    }
}
