<?php

declare(strict_types=1);

namespace App\Service;

use App\Entity\Path;
use DateTimeImmutable;
use League\Flysystem\FilesystemOperator;
use WeakMap;

class EmailFileService
{
    /** @var WeakMap<Path, string|null> */
    private WeakMap $cache;

    public function __construct(private readonly FilesystemOperator $defaultStorage)
    {
        $this->cache = new WeakMap();
    }

    public function getContent(Path $path): ?string
    {
        return $this->cache[$path] ??= $this->defaultStorage->read($path->getPath());
    }

    public function setContent(Path $path, string $content): void
    {
        $this->defaultStorage->write($path->getPath(), $content);
        $this->cache->offsetSet($path, $content);
    }

    /**
     * Returns the size of the file in bytes.
     */
    public function getFileSize(Path $path): int
    {
        return $this->defaultStorage->fileSize($path->getPath());
    }

    public function getEncodedFileSize(Path $path): int
    {
        return strlen(base64_encode((string)$this->getContent($path)));
    }

    public static function createPathForEmailContent(string $type, string $userId): Path
    {
        $date = new DateTimeImmutable();

        return new Path(sprintf(
            'email/userid-%s/%s/%s-%s-%s',
            $userId,
            $date->format('Y-m-d'),
            $date->format('H:i:s'),
            uuid_create(),
            $type
        ));
    }
}
