<?php

declare(strict_types=1);

namespace App\Service;

use App\Converter\EmailDtoConverter;
use App\DTO\Request\Email as EmailDto;
use App\Entity\Email;
use App\Enum\EmailStatus;
use App\Logging\EmailUuidProcessor;
use App\Security\UserInterface;
use Doctrine\ORM\EntityManagerInterface;

readonly class EmailService
{
    public function __construct(
        private EmailDtoConverter $emailDtoConverter,
        private EntityManagerInterface $entityManager
    ) {
    }

    public function createEmailEntityFromDto(EmailDto $emailDto, UserInterface $user): Email
    {
        EmailUuidProcessor::setProcessedMailUuid($emailDto->uuid);

        $email = $this->emailDtoConverter->toEmailEntity($emailDto, $user);
        $email->setStatus(EmailStatus::QUEUED);
        $this->entityManager->persist($email);
        $this->entityManager->flush();

        return $email;
    }
}
