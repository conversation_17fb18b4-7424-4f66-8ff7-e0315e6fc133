<?php

declare(strict_types=1);

namespace App\Mailbox;

use App\Converter\IncomingEmailConverter;
use App\DTO\Response\EmailReceived;
use App\DTO\Response\EmailReceivedSummary;
use App\Util\ExceptionHelper;
use App\Util\Result;
use DateTimeInterface;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Webklex\PHPIMAP\Client as Mailbox;
use Webklex\PHPIMAP\Exceptions\ImapServerErrorException;
use Webklex\PHPIMAP\Folder;
use Webklex\PHPIMAP\Message;
use Webklex\PHPIMAP\Query\WhereQuery;
use Webklex\PHPIMAP\Support\MessageCollection;

use function Sentry\captureException;

class ImapMailbox implements MailboxInterface
{
    private const FOLDER_NAME = 'INBOX';

    public function __construct(private readonly Mailbox $mailbox, private readonly LoggerInterface $logger)
    {
    }

    /** @return array<EmailReceivedSummary> */
    public function searchEmailsSince(DateTimeInterface $since, ?string $searchSubject = null): array
    {
        $validity = $this->mailbox->checkFolder(self::FOLDER_NAME)['uidvalidity'];
        $folder = $this->mailbox->getFolder(self::FOLDER_NAME);

        if ($folder === null) {
            throw new Exception(sprintf("Folder %s can not be found in this mailbox", self::FOLDER_NAME));
        }

        /** @var WhereQuery $query */
        $query = $folder->query()
            ->since($since->format('d.F.Y'))
            ->setFetchBody(false)
            ->leaveUnread();

        if ($searchSubject !== null) {
            $query->subject($searchSubject);
        }

        /** @var MessageCollection $mailCollection*/
        $mailCollection = $query->get();

        if ($mailCollection->count() <= 0) {
            return [];
        }

        /** @var EmailReceivedSummary[] $mappedMails */
        $mappedMails = $mailCollection->map(
            static function ($mailInfo) use ($validity): ?EmailReceivedSummary {
                /** @var Message $mailInfo */
                return IncomingEmailConverter::toEmailReceivedSummary($mailInfo, (string) $validity);
            },
        )->filter()->toArray();

        return $mappedMails;
    }

    public function getMailById(string $id): EmailReceived
    {
        $folder = $this->mailbox->getFolder(self::FOLDER_NAME);

        [$validity, $emailId] = $this->extractEmailId($id);

        if ($folder === null) {
            throw new ConflictHttpException(
                sprintf("Folder %s has not been found in Mailbox", self::FOLDER_NAME)
            );
        }

        if (!($this->mailbox->checkFolder(self::FOLDER_NAME)['uidvalidity'] === $validity)) {
            throw new ConflictHttpException(
                "Uidvalidity of {$folder->path} changed. Uid of mail #{$emailId} need to be fetched again."
            );
        }

        $mailbox = $folder
            ->query()
            ->leaveUnread()
            ->getMessage($emailId);

        return IncomingEmailConverter::toEmailReceived($mailbox, $id);
    }

    public function getRawMailById(string $id): string
    {
        $folder = $this->mailbox->getFolder(self::FOLDER_NAME);
        [$validity, $emailId] = $this->extractEmailId($id);

        if ($folder === null) {
            throw new ConflictHttpException(
                sprintf("Folder %s has not been found in Mailbox", self::FOLDER_NAME)
            );
        }

        if (!($this->mailbox->checkFolder(self::FOLDER_NAME)['uidvalidity'] === $validity)) {
            throw new ConflictHttpException(
                "Uidvalidity of {$folder->path} changed. Uid of mail #{$emailId} need to be fetched again."
            );
        }

        $message = $folder
            ->query()
            ->leaveUnread()
            ->getMessage($emailId);

        return $message->getHeader()?->raw . $message->getRawBody();
    }

    public function saveEmailInSentFolder(string $email): void
    {
        try {
            $folders = $this->mailbox->getFolders(soft_fail: true);
        } catch (ImapServerErrorException $e) {
            captureException($e);

            return;
        }

        $sentFolderName = null;

        /** @var Folder $folder */
        foreach ($folders as $folder) {
            if (SentFolderNames::tryFrom($folder->name) !== null) {
                $sentFolderName = $folder->name;

                break;
            }
        }

        if ($sentFolderName === null) {
            try {
                $this->logger->info('Sent folder not found. Try creating new sent folder', [
                    'mailbox' => $this->mailbox->getFolders(),
                    'activeFolder' => $this->mailbox->getActiveFolder(),
                ]);
                $sentFolderName = SentFolderNames::GESENDET->value;
                $this->mailbox->createFolder('INBOX.' . $sentFolderName);
                $this->logger->info('Folder was created');
            } catch (Exception $e) {
                captureException($e);

                return;
            }
        }

        $this->mailbox->getFolder($sentFolderName)?->appendMessage($email);
    }

    public function testConnection(): Result
    {
        // reduce the socket timeout when trying to establish (aka testing) a connection
        // this can be rather short as we are not sending a lot of data
        $this->mailbox->timeout = 5;

        try {
            $this->mailbox->connect();
        } catch (Exception $e) {
            return Result::err('Test of imap connection failed: ' . ExceptionHelper::getMessageChain($e));
        }

        return $this->mailbox->isConnected()
            ? Result::ok(null)
            : Result::err('Test of imap connection failed.');
    }

    /**
     * @return array{int, int}
     */
    public function extractEmailId(string $id): array
    {
        if (!str_contains($id, '-')) {
            throw new Exception('Email-Id is invalid as it does not contain the expected separator');
        }

        [$validity, $emailId] = explode('-', $id);

        if (!is_numeric($validity) || !is_numeric($emailId)) {
            throw new Exception('Email-Id is invalid as it is partly not numeric');
        }

        return [(int) $validity, (int) $emailId];
    }
}
