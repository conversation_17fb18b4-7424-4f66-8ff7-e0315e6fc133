<?php

declare(strict_types=1);

namespace App\Mailbox;

use App\DTO\Response\EmailReceived;
use App\DTO\Response\EmailReceivedSummary;
use App\Util\Result;
use DateTimeInterface;

interface MailboxInterface
{
    /** @return array<EmailReceivedSummary> */
    public function searchEmailsSince(DateTimeInterface $since, ?string $searchSubject = null): array;

    public function getMailById(string $id): EmailReceived;

    public function getRawMailById(string $id): string;

    /** @return Result<null, string> */
    public function testConnection(): Result;
}
