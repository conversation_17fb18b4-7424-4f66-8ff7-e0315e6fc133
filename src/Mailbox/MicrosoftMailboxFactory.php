<?php

declare(strict_types=1);

namespace App\Mailbox;

use App\Entity\OAuth2Connection;
use App\OAuth\MicrosoftProvider;

class MicrosoftMailboxFactory
{
    public function __construct(private readonly MicrosoftProvider $microsoftProvider)
    {
    }

    public function create(OAuth2Connection $oAuth2Connection): MicrosoftMailbox
    {
        return new MicrosoftMailbox($oAuth2Connection, $this->microsoftProvider);
    }
}
