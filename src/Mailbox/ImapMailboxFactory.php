<?php

declare(strict_types=1);

namespace App\Mailbox;

use App\Entity\SmtpImapConnection;
use Exception;
use Psr\Log\LoggerInterface;
use Webklex\PHPIMAP\ClientManager;

class ImapMailboxFactory
{
    public function __construct(private readonly LoggerInterface $logger)
    {
    }

    public function create(SmtpImapConnection $smtpImapConnection): ImapMailbox
    {
        if ($smtpImapConnection->getImapHost() === null || $smtpImapConnection->getImapPort() === null) {
            throw new Exception('Cannot create mailbox as imap host or port or both are missing.');
        }

        $cm = new ClientManager();

        $mailbox = $cm->make([
            'host' => $smtpImapConnection->getImapHost(),
            'port' => $smtpImapConnection->getImapPort(),
            'encryption' => 'ssl',
            'validate_cert' => true,
            'username' => $smtpImapConnection->getUsername(),
            'password' => $smtpImapConnection->getPassword(),
            'protocol' => 'imap',
        ]);

        return new ImapMailbox($mailbox, $this->logger);
    }
}
