<?php

declare(strict_types=1);

namespace App\Mailbox;

use App\Entity\EmailSettings;
use App\Entity\OAuth2Connection;
use App\Entity\SmtpImapConnection;
use Exception;

class MailboxFactory
{
    public function __construct(
        private readonly MicrosoftMailboxFactory $microsoftMailboxFactory,
        private readonly GmailMailboxFactory $gmailMailboxFactory,
        private readonly ImapMailboxFactory $imapMailboxFactory,
    ) {
    }

    public function createMailboxFromEmailSettings(EmailSettings $settings): MailboxInterface
    {
        try {
            return match (true) {
                $settings instanceof SmtpImapConnection => $this->imapMailboxFactory->create($settings),
                $settings instanceof OAuth2Connection => $this->createOAuthAuthenticatedMailbox($settings),
                default => throw new Exception('Mailbox creation for the provided emailSettings is not yet supported.'),
            };
        } catch (Exception $e) {
            throw new MailboxCreationException('During creation from email settings. ', previous: $e);
        }
    }

    private function createOAuthAuthenticatedMailbox(OAuth2Connection $settings): MailboxInterface
    {
        return match ($settings->getProvider()) {
            'microsoft' => $this->microsoftMailboxFactory->create($settings),
            'google' => $this->gmailMailboxFactory->create($settings),
            default => throw new Exception('Mailbox creation for this Provider not implemented yet.'),
        };
    }
}
