<?php

declare(strict_types=1);

namespace App\Mailbox;

use App\Converter\MicrosoftMessageConverter;
use App\DTO\Response\EmailReceived;
use App\DTO\Response\EmailReceivedSummary;
use App\Entity\OAuth2Connection;
use App\OAuth\MicrosoftProvider;
use App\Util\Result;
use DateTimeInterface;
use Exception;
use Microsoft\Graph\Graph;
use Microsoft\Graph\Http\GraphResponse;
use Microsoft\Graph\Model\Message;

class MicrosoftMailbox implements MailboxInterface
{
    private Graph $graph;

    public function __construct(
        private readonly OAuth2Connection $oAuth2Connection,
        private readonly MicrosoftProvider $microsoftProvider,
    ) {
    }

    /** @return array<EmailReceivedSummary> */
    public function searchEmailsSince(DateTimeInterface $since, ?string $searchSubject = null): array
    {
        $this->initializeGraph();

        $date = $since->format('Y-m-d\TH:i:s\Z');
        $filter = "receivedDateTime ge {$date}";
        $folder = 'Inbox';

        if ($searchSubject !== null) {
            $filter .= " and contains(subject, '{$searchSubject}')";
        }

        //Only Fetch the required Data:
        $select = "from,toRecipients,ccRecipients,bccRecipients,subject,receivedDateTime,internetMessageId";

        $messages = $this->graph
            ->createCollectionRequest(
                'GET',
                "/me/mailFolders('{$folder}')/messages?\$select={$select}&\$filter={$filter}"
            )
            ->setReturnType(Message::class)
            ->setPageSize(10);

        $emails = [];

        do {
            /** @var Message $message */
            foreach ($messages->getPage() as $message) {
                $emailReceivedSummary = MicrosoftMessageConverter::toEmailReceivedSummary($message);

                if ($emailReceivedSummary !== null) {
                    $emails[] = $emailReceivedSummary;
                }
            }
        } while (!$messages->isEnd());

        return $emails;
    }

    public function getMailById(string $id): EmailReceived
    {
        $this->initializeGraph();

        //Only Fetch the required Data:
        $select = implode(',', [
            'from',
            'sender',
            'toRecipients',
            'ccRecipients',
            'bccRecipients',
            'subject',
            'receivedDateTime',
            'internetMessageId',
            'parentFolderId',
            'body',
        ]);

        /** @var Message $message */
        $message = $this->graph
            ->createRequest('GET', "/me/messages/{$id}?\$select={$select}")
            ->setReturnType(Message::class)
            ->execute();

        return MicrosoftMessageConverter::toEmailReceived($message);
    }

    public function getRawMailById(string $id): string
    {
        $this->initializeGraph();

        /** @var GraphResponse $rawEmail */
        $rawEmail = $this->graph
            ->createRequest('GET', "/me/messages/{$id}/\$value")
            ->execute();

        //Needs to be casted to string
        //  (see https://github.com/microsoftgraph/msgraph-sdk-php/issues/187#issuecomment-1079140313)
        return (string) $rawEmail->getRawBody();
    }

    public function testConnection(): Result
    {
        // If the "Mail.ReadWrite"-scope is missing in the token we cannot read the mailbox
        $tokenData = json_decode($this->oAuth2Connection->getData() ?? '', associative: true);

        if (
            !is_array($tokenData)
            || !isset($tokenData['scope'])
            || !in_array('https://graph.microsoft.com/Mail.ReadWrite', explode(' ', $tokenData['scope']), strict: true)
        ) {
            return Result::err('The Mail.ReadWrite Scope is missing for the refresh-token');
        }

        try {
            $this->initializeGraph();
            /** @var GraphResponse $response */
            $response = $this->graph->createRequest('GET', '/me')->execute();

            /** @phpstan-ignore-next-line The documentation of the Package seems to be wrong. */
            return $response->getStatus() === 200
                ? Result::ok(null)
                : Result::err((string) $response->getRawBody());
        } catch (Exception $exception) {
            return Result::err($exception->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function initializeGraph(): void
    {
        if (!isset($this->graph)) {
            $this->graph = $this->microsoftProvider->getGraphForConnection($this->oAuth2Connection);
        }
    }
}
