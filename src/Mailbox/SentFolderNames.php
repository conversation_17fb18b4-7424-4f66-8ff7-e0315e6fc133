<?php

declare(strict_types=1);

namespace App\Mailbox;

/**
 * Name variants of the sent folder name for different providers
 */
enum SentFolderNames: string
{
    case GESENDET = 'Gesendet'; // GMX
    case SENT = 'Sent';
    case SENT_ITEMS = 'Sent Items'; // Strato
    case SENT_MESSAGES = 'Sent Messages'; // AppleMail
    case SENT_MAIL = 'Sent Mail'; // GoogleMail
    case SENT_OBJECTS = 'Sent Objects'; // Ionos
    case SENT_ELEMENTS = 'Sent Elements'; // Ionos
    case GESENDETE_OBJEKTE = 'Gesendete Objekte'; // Ionos
    case GESENDETE_ELEMENTE = 'Gesendete Elemente'; // Outlook
}
