<?php

declare(strict_types=1);

namespace App\Mailbox;

use App\Entity\OAuth2Connection;
use App\OAuth\GoogleProvider;

class GmailMailboxFactory
{
    public function __construct(private readonly GoogleProvider $googleProvider)
    {
    }

    public function create(OAuth2Connection $oAuth2Connection): GmailMailbox
    {
        return new GmailMailbox($oAuth2Connection, $this->googleProvider);
    }
}
