<?php

declare(strict_types=1);

namespace App\Mailbox;

use App\Converter\GmailMessageConverter;
use App\DTO\Response\EmailReceived;
use App\DTO\Response\EmailReceivedSummary;
use App\Entity\OAuth2Connection;
use App\OAuth\GoogleProvider;
use App\Util\Result;
use DateTimeInterface;
use Exception;
use Google\Client;
use Google\Service\Gmail;
use Google_Service_Gmail;

class GmailMailbox implements MailboxInterface
{
    private Client $googleClient;

    public function __construct(
        private readonly OAuth2Connection $oAuth2Connection,
        private readonly GoogleProvider $googleProvider,
    ) {
    }

    /** @return array<EmailReceivedSummary> */
    public function searchEmailsSince(DateTimeInterface $since, ?string $searchSubject = null): array
    {
        $date = $since->format('Y/m/d');
        $filter = "after:{$date}";
        $folder = 'Inbox';

        if ($searchSubject !== null) {
            $filter .= " subject: '{$searchSubject}'";
        }

        //It seems like Gmail is moving emails from the inbox by itself. So we might remove this filter:
        $filter .= " in:$folder";

        $gmailService = $this->getGmailService();

        $messages = $gmailService->users_messages->listUsersMessages('me', ['q' => $filter])->getMessages();

        $emails = [];

        foreach ($messages as $message) {
            $messageWithMetadata = $gmailService->users_messages->get('me', $message->id, ['format' => 'metadata',]);
            $emailReceivedSummary = GmailMessageConverter::toEmailReceivedSummary($messageWithMetadata);

            if ($emailReceivedSummary !== null) {
                $emails[] = $emailReceivedSummary;
            }
        }

        return $emails;
    }

    public function getMailById(string $id): EmailReceived
    {
        $gmailService = $this->getGmailService();

        $fullMessage = $gmailService->users_messages->get('me', $id, ['format' => 'full']);

        return GmailMessageConverter::toEmailReceived($fullMessage);
    }

    private function gmailBodyDecode(string $data): string
    {
        /** @phpstan-ignore-next-line */
        $data = base64_decode(str_replace(['-', '_'], ['+', '/'], $data), false); // phpcs:ignore
        //from php.net/manual/es/function.base64-decode.php#118244

        return $data;
    }

    public function getRawMailById(string $id): string
    {
        $gmailService = $this->getGmailService();

        $rawMessage = $gmailService->users_messages->get('me', $id, ['format' => 'raw']);

        return $this->gmailBodyDecode($rawMessage->getRaw());
    }

    public function testConnection(): Result
    {
        // If the "gmail.readonly"-scope is missing in the token we cannot read the mailbox
        $tokenData = json_decode($this->oAuth2Connection->getData() ?? '', associative: true);

        if (
            !is_array($tokenData)
            || !isset($tokenData['scope'])
            || !in_array(Google_Service_Gmail::GMAIL_READONLY, explode(' ', $tokenData['scope']), strict: true)
        ) {
            return Result::err('The gmail.readonly Scope is missing for the refresh-token');
        }

        try {
            $httpClient = $this->getGoogleClient()->authorize();
            $response = $httpClient->request('GET', 'https://www.googleapis.com/oauth2/v3/userinfo');

            return $response->getStatusCode() === 200
                ? Result::ok(null)
                : Result::err((string) $response->getBody());
        } catch (Exception $exception) {
            return Result::err($exception->getMessage());
        }
    }

    private function getGoogleClient(): Client
    {
        if (!isset($this->googleClient)) {
            $this->googleClient = $this->googleProvider->getClientForConnection($this->oAuth2Connection);
        }

        return $this->googleClient;
    }

    private function getGmailService(): Gmail
    {
        return new Gmail($this->getGoogleClient());
    }
}
