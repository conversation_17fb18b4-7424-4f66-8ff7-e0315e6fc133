<?php

declare(strict_types=1);

namespace App\Mailbox;

use App\Enum\ConnectionStatus;
use App\Error\ConnectionError;
use App\Error\ConnectionErrorType;
use App\Logging\ErrorLogContext;
use App\Repository\EmailSettingsRepository;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class MailboxService
{
    private const MAX_CONNECTION_RETRIES_FOR_RECEIVING = 5;

    public function __construct(
        private readonly Security $security,
        private readonly EmailSettingsRepository $emailSettingsRepository,
        private readonly MailboxFactory $mailboxFactory,
        private readonly EntityManagerInterface $entityManager,
        private readonly LoggerInterface $logger,
    ) {
    }

    #[IsGranted('ROLE_USER')]
    public function createForUserFromDb(): ?MailboxInterface
    {
        $user = $this->security->getUser();

        if ($user === null) {
            throw new AccessDeniedHttpException();
        }

        return $this->getAndCheckMailboxForUser($user);
    }

    public function getAndCheckMailboxForUser(UserInterface $user, bool $failOnError = false): ?MailboxInterface
    {
        if (!$user instanceof \App\Security\UserInterface) {
            throw new Exception('User is not an instance of UserInterface');
        }

        $emailSettings = $this->emailSettingsRepository->find($user->getUserId());

        if ($emailSettings === null) {
            $this->logger->info("No email settings found for user {$user->getUserId()}");

            return null;
        }

        if (!$emailSettings->canReceive()) {
            $this->logger->info("Email settings for user {$user->getUserId()} are not complete for receiving");

            return null;
        }

        if ($emailSettings->getReceivingStatus() !== ConnectionStatus::ACTIVE) {
            $this->logger->info("Email settings for user {$user->getUserId()} are not"
                . " active for receiving (status: {$emailSettings->getSendingStatus()?->value})");

            return null;
        }

        try {
            $mailbox = $this->mailboxFactory->createMailboxFromEmailSettings($emailSettings);
            $resultReceiving = $mailbox->testConnection();

            if ($resultReceiving->isErr()) {
                throw new Exception($resultReceiving->getError());
            }

            $emailSettings->setReceivingFailedConnectionsAttempts(0);
            $this->entityManager->flush();
        } catch (Exception $e) {
            $emailSettings->setReceivingFailedConnectionsAttempts(
                $emailSettings->getReceivingFailedConnectionsAttempts() + 1
            );

            if (
                $failOnError
                || $emailSettings->getReceivingFailedConnectionsAttempts() >= self::MAX_CONNECTION_RETRIES_FOR_RECEIVING
            ) {
                $emailSettings->setReceivingStatus(ConnectionStatus::FAILED);
                $emailSettings->setReceivingFailedConnectionsAttempts(0);
            }

            $logContext = new ErrorLogContext(
                error: new ConnectionError(ConnectionErrorType::RECEIVING, $e->getMessage())
            );
            $this->logger->error('', context: [ErrorLogContext::IDENTIFIER => $logContext]);

            $this->entityManager->flush();

            return null;
        }

        return $mailbox;
    }
}
