<?php

declare(strict_types=1);

namespace App\MessageHandler;

use App\Entity\Email;
use App\Message\SendEmailMessage;
use App\Service\SendEmailService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;

#[AsMessageHandler]
readonly class SendEmailMessageHandler
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SendEmailService $sendEmailService,
        private LoggerInterface $logger
    ) {
    }

    public function __invoke(SendEmailMessage $message): void
    {
        $email = $this->entityManager->find(Email::class, $message->emailId);

        if ($email === null) {
            $errorMessage = static::class . ": Email with id {$message->emailId} not found";
            $this->logger->error($errorMessage);

            throw new UnrecoverableMessageHandlingException($errorMessage);
        }

        $this->sendEmailService->handle($email);
    }
}
