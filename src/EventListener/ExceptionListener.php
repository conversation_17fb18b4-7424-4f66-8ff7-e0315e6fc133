<?php

declare(strict_types=1);

namespace App\EventListener;

use App\DTO\Response\ApiErrorCollectionDto;
use App\DTO\Response\ApiErrorDto;
use App\Exception\WithStatusCode\ExceptionWithStatusCodeInterface;
use Symfony\Component\Console\Event\ConsoleErrorEvent;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;

class ExceptionListener
{
    public function onConsoleError(ConsoleErrorEvent $event): void
    {
        \Sentry\captureException($event->getError());
    }

    public function onKernelException(ExceptionEvent $event): void
    {
        $exception = $event->getThrowable();

        if ($exception instanceof ExceptionWithStatusCodeInterface) {
            $response = new JsonResponse(
                data: new ApiErrorCollectionDto([new ApiErrorDto($exception->getMessage())]),
                status: $exception->getStatusCode(),
            );
            $event->setResponse($response);
        }

        \Sentry\captureException($exception);
    }
}
