<?php

declare(strict_types=1);

namespace App\EventListener;

use App\Converter\EmailEntityConverter;
use App\Entity\Email;
use App\Enum\EmailStatus;
use App\Error\GatewayErrorType;
use App\Error\ValidationError;
use App\Error\ValidationErrorType;
use App\Util\Cast;
use App\Util\EmailHistoryLogger;
use App\Util\ReflectionHelper;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsEntityListener;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Throwable;

#[AsEntityListener]
class EmailListener
{
    private const STATUS = 'status';

    public function __construct(
        private readonly HttpClientInterface $httpClient,
        private readonly EmailHistoryLogger $logger,
        private readonly EmailEntityConverter $emailEntityConverter,
    ) {
    }

    public function preUpdate(Email $email, PreUpdateEventArgs $args): void
    {
        try {
            $statusCallbackUrl = $email->getStatusCallbackUrl();

            if (!$args->getObject() instanceof Email) {
                return;
            }

            if (!ReflectionHelper::doesPropertyExist($args->getObject(), self::STATUS)) {
                $this->logger->validationError(new ValidationError(
                    ValidationErrorType::PROPERTY_INVALID,
                    'Email status property was renamed, so the status cannot be pushed.'
                ));

                return;
            }

            if (!$args->hasChangedField(self::STATUS) || $statusCallbackUrl === null) {
                return;
            }

            $newStatus = Cast::toStringOrThrow($args->getNewValue(self::STATUS));
            $oldStatus = Cast::toStringOrThrow($args->getOldValue(self::STATUS));

            if (!EmailStatus::from($newStatus)->hasToCallBackClient()) {
                return;
            }

            // Do not send status update for queued emails that were just created
            //  as this information is returned synchronously
            if ($newStatus === EmailStatus::QUEUED->value && $oldStatus === EmailStatus::CREATED->value) {
                return;
            }

            $response = $this->httpClient->request(
                'POST',
                $statusCallbackUrl,
                ['json' => $this->emailEntityConverter->toResponseDto($email)]
            );

            if (!($response->getStatusCode() === 200 || $response->getStatusCode() === 204)) {
                $this->logger->gatewayError(
                    GatewayErrorType::EMAIL_STATUS_UPDATE_FAILED,
                    (string) $response->getStatusCode()
                );
            }
        } catch (Throwable $exception) {
            $this->logger->unknownError($exception->getMessage());
        }
    }
}
