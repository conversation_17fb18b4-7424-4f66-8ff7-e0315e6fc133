<?php

declare(strict_types=1);

namespace App\Email;

use App\Email\Provider\ProviderConfigInterface;
use App\Enum\EmailTransportScheme;
use App\Transport\TransportInterface;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\Transport\AbstractTransportFactory;
use Symfony\Component\Mailer\Transport\Dsn;
use Symfony\Component\Mailer\Transport\Smtp\SmtpTransport as SymfonySmtpTransport;
use Symfony\Component\Mailer\Transport\TransportFactoryInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class SmtpTransportFactory extends AbstractTransportFactory
{
    /**
     * @param iterable<ProviderConfigInterface> $providerConfig
     */
    public function __construct(
        private readonly TransportFactoryInterface $esmtpTransportFactory,
        private readonly iterable $providerConfig,
        EventDispatcherInterface $dispatcher = null,
        HttpClientInterface $client = null,
        LoggerInterface $logger = null,
    ) {
        parent::__construct($dispatcher, $client, $logger);
    }

    public function create(Dsn $dsn): TransportInterface
    {
        /** @var SymfonySmtpTransport $symfonySmtpTransport */
        $symfonySmtpTransport = $this->esmtpTransportFactory->create($dsn);

        $providerConfig = $this->getProviderConfigForDsn($dsn);

        // Configure Transport for specific Provider
        if ($providerConfig !== null) {
            if (($restartThreshold = $providerConfig->getMaxSentEmailsPerKeepAlive()) !== 0) {
                $symfonySmtpTransport->setRestartThreshold($restartThreshold);
            }

            return new SmtpTransport($symfonySmtpTransport, $providerConfig);
        }

        return new SmtpTransport($symfonySmtpTransport);
    }

    /**
     * @return string[]
     */
    protected function getSupportedSchemes(): array
    {
        return [
            EmailTransportScheme::SMTP->value,
            EmailTransportScheme::SMTPS->value,
        ];
    }

    private function getProviderConfigForDsn(Dsn $dsn): ?ProviderConfigInterface
    {
        foreach ($this->providerConfig as $provider) {
            if ($provider->supportsDsn($dsn)) {
                return $provider;
            }
        }

        return null;
    }
}
