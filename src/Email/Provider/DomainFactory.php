<?php

declare(strict_types=1);

namespace App\Email\Provider;

use App\DTO\Service\RateLimit\MaxUserEmailsPerMinutes;
use Symfony\Component\Mailer\Transport\Dsn;

// DomainFactory is not a factory class but an actual provider
class DomainFactory implements ProviderConfigInterface
{
    public function getMaxSentEmailsPerKeepAlive(): int
    {
        // No known limit
        return 0;
    }

    public function getRateLimits(): array
    {
        return [
            new MaxUserEmailsPerMinutes(60, 450),
        ];
    }

    public function supportsDsn(Dsn $dsn): bool
    {
        return $dsn->getHost() === 'df.eu' || str_ends_with($dsn->getHost(), '.df.eu');
    }

    public function getDsn(): string
    {
        return 'df.eu';
    }
}
