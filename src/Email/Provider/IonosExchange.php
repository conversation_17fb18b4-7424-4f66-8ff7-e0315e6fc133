<?php

declare(strict_types=1);

namespace App\Email\Provider;

use App\DTO\Service\RateLimit\MaxUserEmailsPerMinutes;
use Symfony\Component\Mailer\Transport\Dsn;

class IonosExchange implements ProviderConfigInterface
{
    public function getMaxSentEmailsPerKeepAlive(): int
    {
        return 20;
    }

    public function getRateLimits(): array
    {
        return [
            new MaxUserEmailsPerMinutes(1, 5),
//            Technically, these are rate limits, but they are so high that we can probably ignore them
//            Leaving this entry here in case we need it someday:
//            new MaxNumberOfRecipientsPerDay(16000),
//            new MaxNumberOfRecipientsPerEmail(500)
        ];
    }

    public function supportsDsn(Dsn $dsn): bool
    {
        return str_ends_with($dsn->getHost(), 'exchange2019.ionos.de');
    }

    public function getDsn(): string
    {
        return 'exchange2019.ionos.de';
    }
}
