<?php

declare(strict_types=1);

namespace App\Email\Provider;

use App\DTO\Service\RateLimit\MaxUserEmailsPerMinutes;
use Symfony\Component\Mailer\Transport\Dsn;

class OneBlu implements ProviderConfigInterface
{
    public function getMaxSentEmailsPerKeepAlive(): int
    {
        // No known limit
        return 0;
    }

    public function getRateLimits(): array
    {
        return [
            new MaxUserEmailsPerMinutes(minutes: 60, limit: 135),
        ];
    }

    public function supportsDsn(Dsn $dsn): bool
    {
        return $dsn->getHost() === '1blu.de' || str_ends_with($dsn->getHost(), '.1blu.de');
    }

    public function getDsn(): string
    {
        return '1blu.de';
    }
}
