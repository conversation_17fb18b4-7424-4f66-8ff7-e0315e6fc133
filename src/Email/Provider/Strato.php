<?php

declare(strict_types=1);

namespace App\Email\Provider;

use App\DTO\Service\RateLimit\MaxSmtpConnectionErrorsPerDay;
use App\DTO\Service\RateLimit\MaxUserEmailsPerMinutes;
use App\DTO\Service\RateLimit\UserSmtpRateLimitError;
use Symfony\Component\Mailer\Transport\Dsn;

class Strato implements ProviderConfigInterface
{
    public function getMaxSentEmailsPerKeepAlive(): int
    {
        // No known limit
        return 0;
    }

    public function getRateLimits(): array
    {
        return [
            new UserSmtpRateLimitError(),
            // The value is taken from PW, it is unclear if the value is appropriate
            new MaxSmtpConnectionErrorsPerDay(20, $this->getDsn()),
            new MaxUserEmailsPerMinutes(1, 20),
            new MaxUserEmailsPerMinutes(60, 450),
        ];
    }

    public function supportsDsn(Dsn $dsn): bool
    {
        return $dsn->getHost() === 'strato.de' || str_ends_with($dsn->getHost(), '.strato.de');
    }

    public function getDsn(): string
    {
        return 'strato.de';
    }
}
