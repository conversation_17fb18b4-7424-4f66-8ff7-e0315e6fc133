<?php

declare(strict_types=1);

namespace App\Email\Provider;

use App\DTO\Service\RateLimit\MaxUserEmailsPerMinutes;
use Symfony\Component\Mailer\Transport\Dsn;

class Ionos implements ProviderConfigInterface
{
    public function getMaxSentEmailsPerKeepAlive(): int
    {
        return 20;
    }

    public function getRateLimits(): array
    {
        return [
            new MaxUserEmailsPerMinutes(1, 5),
        ];
    }

    public function supportsDsn(Dsn $dsn): bool
    {
        return in_array($dsn->getHost(), ['smtps.ionos.de', 'smtp.ionos.de', 'imap.ionos.de'], true);
    }

    public function getDsn(): string
    {
        return 'ionos.de';
    }
}
