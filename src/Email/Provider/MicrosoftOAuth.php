<?php

declare(strict_types=1);

namespace App\Email\Provider;

use App\DTO\Service\RateLimit\MaxUserEmailsPerMinutes;
use Symfony\Component\Mailer\Transport\Dsn;

class MicrosoftOAuth implements ProviderConfigInterface
{
    public function getMaxSentEmailsPerKeepAlive(): int
    {
        return 20;
    }

    public function getRateLimits(): array
    {
        return [
            new MaxUserEmailsPerMinutes(1, 5),
        ];
    }

    public function supportsDsn(Dsn $dsn): bool
    {
        return false;
    }

    public function getDsn(): string
    {
        return '!'; //invalid string on purpose to not accidentally match with other providers
    }
}
