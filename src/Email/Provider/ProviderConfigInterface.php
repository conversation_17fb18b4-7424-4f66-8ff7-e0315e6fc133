<?php

declare(strict_types=1);

namespace App\Email\Provider;

use App\DTO\Service\RateLimit\AbstractRateLimit;
use Symfony\Component\Mailer\Transport\Dsn;

interface ProviderConfigInterface
{
    /**
     * @description Um in einem SMTP-KeepAlive Zyklus nicht zu viele E-Mails zu verschicken,
     *   können für die Provider eine Obergrenze an maximal versendeten Mails in einem KeepAlive definiert werden.
     * Beispiel: Bei IONOS führt der Versand via keep alive dazu, dass nur 20 mails pro session versendet werden können.
     *   Session wird nicht erneuert.
     */
    public function getMaxSentEmailsPerKeepAlive(): int;

    /**
     * @return AbstractRateLimit[]
     *
     * @description Um eine Sperre der individuellen Zugangsdaten eines nutzers zu umgehen,
     *   können für die Provider eine Obergrenze an maximal versendeten Mails in einem Zeitraum definiert werden.
     * Beispiel: Bei IONOS (1&1) führt der Versand von vielen Mails (Serienbriefe) zur Sperre des Zugangs des Maklers.
     *   Genaues Limit ist nicht bekannt.
     *   Ausnahme per IP könnte wohl bei denen eingestellt werden, dazu brauchen wir allerdings eine dedizierte IP,
     *   welche die Makler auch nachhaltig per Ausnahme kommunizieren können
     */
    public function getRateLimits(): array;

    public function supportsDsn(Dsn $dsn): bool;

    public function getDsn(): string;
}
