<?php

declare(strict_types=1);

namespace App\Email;

use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mime\RawMessage;

class SesSmtpTransport extends SmtpTransport
{
    public function send(RawMessage $message, Envelope $envelope = null): ?SentMessage
    {
        $sentMessage = $this->smtpTransport->send($message, $envelope);

        $sentMessage?->setMessageId($sentMessage->getMessageId() . "@eu-central-1.amazonses.com");

        return $sentMessage;
    }
}
