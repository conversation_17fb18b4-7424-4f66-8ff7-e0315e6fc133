<?php

declare(strict_types=1);

namespace App\Email;

use App\Enum\EmailTransportScheme;
use App\OAuth\GoogleProvider;
use App\OAuth\MicrosoftProvider;
use App\Transport\TransportInterface;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\Exception\UnsupportedSchemeException;
use Symfony\Component\Mailer\Transport\AbstractTransportFactory;
use Symfony\Component\Mailer\Transport\Dsn;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class ApiTransportFactory extends AbstractTransportFactory
{
    public function __construct(
        private readonly GoogleProvider $googleProvider,
        private readonly MicrosoftProvider $microsoftProvider,
        EventDispatcherInterface $dispatcher = null,
        HttpClientInterface $client = null,
        LoggerInterface $logger = null,
    ) {
        parent::__construct($dispatcher, $client, $logger);
    }

    public function create(Dsn $dsn): TransportInterface
    {
        if ($dsn->getScheme() !== EmailTransportScheme::API->value) {
            throw new UnsupportedSchemeException($dsn, $dsn->getScheme(), $this->getSupportedSchemes());
        }

        $provider = $dsn->getOption('provider');

        $scope = is_string($dsn->getOption('scope')) ? $dsn->getOption('scope') : '';

        $userId = is_numeric($dsn->getOption('user_id')) ? (int) $dsn->getOption('user_id') : null;

        if ($userId === null) {
            throw new \InvalidArgumentException('User ID is required for API transport');
        }

        return match ($provider) {
            'google' => new GoogleOAuthTransport(
                googleProvider: $this->googleProvider,
                scope: $scope,
                userId: $userId,
                dispatcher: $this->dispatcher,
                logger: $this->logger,
            ),
            'microsoft' => new MicrosoftOAuthTransport(
                microsoftProvider: $this->microsoftProvider,
                scope: $scope,
                userId: $userId,
                dispatcher: $this->dispatcher,
                logger:$this->logger,
            ),
            default => throw new UnsupportedApiProviderException('Provider is not yet supported'),
        };
    }

    /**
     * @return string[]
     */
    protected function getSupportedSchemes(): array
    {
        return [EmailTransportScheme::API->value];
    }
}
