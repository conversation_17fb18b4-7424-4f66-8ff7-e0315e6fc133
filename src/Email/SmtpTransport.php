<?php

declare(strict_types=1);

namespace App\Email;

use App\Email\Provider\ProviderConfigInterface;
use App\Transport\TransportInterface;
use App\Util\ExceptionHelper;
use App\Util\Result;
use Exception;
use Symfony\Component\Mailer\Envelope;
use <PERSON>ymfony\Component\Mailer\SentMessage;
use Symfony\Component\Mailer\Transport\Smtp\SmtpTransport as SymfonySmtpTransport;
use Symfony\Component\Mailer\Transport\Smtp\Stream\SocketStream;
use Symfony\Component\Mime\RawMessage;

class SmtpTransport implements TransportInterface
{
    public function __construct(
        protected readonly SymfonySmtpTransport $smtpTransport,
        protected readonly ?ProviderConfigInterface $providerConfig = null
    ) {
    }

    /**
     * @return Result<null, string>
     */
    public function testConnection(): Result
    {
        // reduce the socket timeout when trying to establish (aka testing) a connection
        $this->setSocketTimeout(15);

        try {
            $this->smtpTransport->start();
        } catch (\Exception $e) {
            return Result::err('Test of smtp connection failed: ' . ExceptionHelper::getMessageChain($e));
        }

        return Result::ok(null);
    }

    public function send(RawMessage $message, Envelope $envelope = null): ?SentMessage
    {
        return $this->smtpTransport->send($message, $envelope);
    }

    public function setSocketTimeout(float $seconds, int $max = 60): void
    {
        $stream = $this->smtpTransport->getStream();

        if ($seconds <= 10.0) {
            throw new Exception('Remaining seconds for socket timeout are too few');
        }

        if ($stream instanceof SocketStream) {
            $stream->setTimeout(min($max, $seconds));
        }
    }

    public function getProviderConfig(): ?ProviderConfigInterface
    {
        return $this->providerConfig;
    }

    public function __toString(): string
    {
        return (string)$this->smtpTransport;
    }
}
