<?php

declare(strict_types=1);

namespace App\Email;

use Microsoft\Graph\Http\GraphResponse;
use Sentry\EventHint;
use Sentry\Severity;
use Symfony\Contracts\HttpClient\ResponseInterface;

use function Sentry\captureMessage;

readonly class MicrosoftResponse implements ResponseInterface
{
    public function __construct(private GraphResponse $response)
    {
    }

    public function getStatusCode(): int
    {
        return (int) $this->response->getStatus();
    }

    public function getHeaders(bool $throw = true): array
    {
        $headers = $this->response->getHeaders();

        if ($throw && $this->getStatusCode() >= 300) {
            throw new \Exception('Sending failed. Status code: ' . $this->getStatusCode());
        }

        if ($headers === null) {
            throw new \Exception('No headers were found');
        }

        return $headers;
    }

    public function getContent(bool $throw = true): string
    {
        if ($throw && $this->getStatusCode() >= 300) {
            throw new \Exception('Sending failed. Status code: ' . $this->getStatusCode());
        }

        return $this->response->getRawBody()?->getContents() ?? '';
    }

    /**
     * @return array<mixed>
     */
    public function toArray(bool $throw = true): array
    {
        if ($throw && $this->getStatusCode() >= 300) {
            throw new \Exception('Sending failed. Status code: ' . $this->getStatusCode());
        }

        return $this->response->getBody();
    }

    public function cancel(): void
    {
        // as this class only contains the GraphResponse, there is no possibility to close the response stream
    }

    public function getInfo(?string $type = null): mixed
    {
        // this method is meant to provide live data from the transport layer
        // but we don't as the GuzzleResponse is wrapped in a GraphResponse
        // we don't have those informations here
        // but we should check if that leads to any problems
        // therefore we log them to Sentry

        $hint = EventHint::fromArray([
            'extra' => [
                'headers' => $this->getHeaders(),
                'content' => $this->getContent(),
            ],
        ]);

        captureMessage('Tried to getInfo on ' . self::class . ' for type ' . $type . '.', Severity::info(), $hint);

        return null;
    }
}
