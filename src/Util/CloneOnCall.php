<?php

declare(strict_types=1);

namespace App\Util;

use Exception;

/**
 * @template T of object
 *
 * @mixin T
 */
class CloneOnCall
{
    /**
     * @param T $object
     */
    public function __construct(private object $object)
    {
    }

    /**
     * @param mixed[] $args
     *
     * @return mixed
     */
    public function __call(string $f, array $args)
    {
        $callback = [clone $this->object, $f];

        if (!is_callable($callback)) {
            throw new Exception('Method not found');
        }

        return call_user_func_array($callback, $args);
    }
}
