<?php

declare(strict_types=1);

namespace App\Util;

class ReflectionHelper
{
    public static function doesPropertyExist(object $object, string $propertyName): bool
    {
        $reflectedEmail = new \ReflectionClass($object::class);
        $properties = $reflectedEmail->getProperties();

        foreach ($properties as $property) {
            if ($property->getName() === $propertyName) {
                return true;
            }
        }

        return false;
    }
}
