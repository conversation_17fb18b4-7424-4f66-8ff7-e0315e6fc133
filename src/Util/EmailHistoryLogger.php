<?php

declare(strict_types=1);

namespace App\Util;

use App\Error\AuthorizationError;
use App\Error\AuthorizationErrorType;
use App\Error\GatewayError;
use App\Error\GatewayErrorType;
use App\Error\ProcessError;
use App\Error\ProcessErrorType;
use App\Error\TransportError;
use App\Error\TransportErrorType;
use App\Error\UnknownError;
use App\Error\ValidationError;
use App\Logging\ErrorLogContext;
use App\Logging\TransitionLogContext;
use App\Transport\TransportInterface;
use Psr\Log\AbstractLogger;
use Psr\Log\LoggerInterface;
use Sentry\EventHint;
use Sentry\Severity;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Throwable;

use function Sentry\captureMessage;

class EmailHistoryLogger extends AbstractLogger
{
    public function __construct(private LoggerInterface $logger)
    {
    }

    public function transition(?string $fromState = null, ?string $toState = null): void
    {
        $this->logger->info(
            message: 'Email State Change',
            context: [
                TransitionLogContext::IDENTIFIER => new TransitionLogContext(
                    fromState: $fromState,
                    toState: $toState
                ),
            ],
        );
    }

    public function unknownError(string $cause, ?string $debugInfo = null, ?string $transport = null): void
    {
        $hint = EventHint::fromArray([
            'extra' => [
                'debugInfo' => $debugInfo,
                'transport' => $transport,
            ],
        ]);

        captureMessage('Unknown error: ' . $cause, Severity::error(), $hint);

        $error = new UnknownError($cause);
        $context = new ErrorLogContext($error, $debugInfo, $transport);
        $this->logger->error('', [ErrorLogContext::IDENTIFIER => $context]);
    }

    public function transportError(TransportErrorType $type, ?string $cause = null, ?string $transport = null): void
    {
        $error = new TransportError($type);
        $logContext = new ErrorLogContext(error: $error, transport: $transport);
        $this->logger->error('', [ErrorLogContext::IDENTIFIER => $logContext]);
    }

    public function transportErrorFromThrowable(Throwable $e, TransportInterface $transport = null): void
    {
        $transportError = TransportError::fromSmtpErrorMessage($e->getMessage());

        $logContext = $e instanceof TransportExceptionInterface
            ? new ErrorLogContext(
                error: $transportError,
                debugInfo: $e->getDebug(),
                transport: (string)$transport,
            )
            : new ErrorLogContext(
                error: $transportError,
                transport: (string)$transport,
            );

        $this->logger->error('', [ErrorLogContext::IDENTIFIER => $logContext]);
    }

    public function validationError(ValidationError $validationError, ?string $transport = null): void
    {
        $context = new ErrorLogContext(
            error: $validationError,
            transport: (string)$transport,
        );
        $this->logger->error('', context: [ErrorLogContext::IDENTIFIER => $context]);
    }

    public function processError(ProcessErrorType $type, ?string $cause = null): void
    {
        $error = new ProcessError($type, $cause);
        $logContext = new ErrorLogContext($error);
        $this->logger->error('', context: [ErrorLogContext::IDENTIFIER => $logContext]);
    }

    public function authorizationError(AuthorizationErrorType $type, ?string $cause = null): void
    {
        $error = new AuthorizationError($type, $cause);
        $logContext = new ErrorLogContext($error);
        $this->logger->error('', context: [ErrorLogContext::IDENTIFIER => $logContext]);
    }

    public function gatewayError(GatewayErrorType $type, ?string $cause = null): void
    {
        $error = new GatewayError($type, $cause);
        $logContext = new ErrorLogContext($error);
        $this->logger->error('', context: [ErrorLogContext::IDENTIFIER => $logContext]);
    }

    /**
     * @param array<mixed> $context
     */
    public function log($level, string|\Stringable $message, array $context = []): void
    {
        $this->logger->log($level, $message, $context);
    }
}
