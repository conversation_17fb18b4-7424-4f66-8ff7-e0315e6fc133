<?php

declare(strict_types=1);

namespace App\Util;

/**
 * @template-covariant T
 * @template-covariant E
 */
final class Result
{
    /** @var T */
    private mixed $value;

    /** @var E */
    private mixed $error;

    private function __construct(public readonly ResultState $state, mixed $value)
    {
        match ($state) {
            ResultState::Ok => $this->value = $value,
            ResultState::Err => $this->error = $value,
        };
    }

    /**
     * @template V
     *
     * @param V $value
     *
     * @return self<V, never>
     */
    public static function ok(mixed $value): self
    {
        return new self(ResultState::Ok, $value);
    }

    /**
     * @template V
     *
     * @param V $value
     *
     * @return self<never, V>
     */
    public static function err(mixed $value): self
    {
        return new self(ResultState::Err, $value);
    }

    public function isOk(): bool
    {
        return $this->state === ResultState::Ok;
    }

    public function isErr(): bool
    {
        return $this->state === ResultState::Err;
    }

    /**
     * @param self<T, E> $result
     */
    public static function instanceIsOk(self $result): bool
    {
        return $result->state === ResultState::Ok;
    }

    /**
     * @param self<T, E> $result
     */
    public static function instanceIsErr(self $result): bool
    {
        return $result->state === ResultState::Err;
    }

    /**
     * @return T
     */
    public function getValue(): mixed
    {
        return $this->value;
    }

    /**
     * @return E
     */
    public function getError(): mixed
    {
        return $this->error;
    }
}
