<?php

declare(strict_types=1);

namespace App\Util;

/*
 * A deadline timer. Time is set in seconds through the constructor.
 */
class Timer
{
    private float $end;

    public function __construct(float $seconds)
    {
        $this->end = microtime(true) + $seconds;
    }

    public function isExpired(): bool
    {
        return microtime(true) > $this->end;
    }

    /**
     * @return float time left in seconds, precise to microseconds
     */
    public function getTimeLeft(): float
    {
        return max(0, $this->end - microtime(true));
    }
}
