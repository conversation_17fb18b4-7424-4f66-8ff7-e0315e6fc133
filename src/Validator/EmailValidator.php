<?php

declare(strict_types=1);

namespace App\Validator;

use App\DTO\Request\Email;
use App\Exception\MailerValidation\EmptyEmailException;
use App\Exception\MailerValidation\InvalidRecipientException;
use App\Exception\MailerValidation\MaxSizeExceededException;
use App\Exception\MailerValidation\NoRecipientsException;

class EmailValidator
{
    public const MAX_ACCEPTED_EMAIL_SIZE = 20 * 1024 * 1024;

    /**
     * @throws \App\Exception\MailerValidation\InvalidRecipientException
     * @throws \App\Exception\MailerValidation\MaxSizeExceededException
     * @throws \App\Exception\MailerValidation\NoRecipientsException
     * @throws \App\Exception\MailerValidation\EmptyEmailException
     */
    public function validateEmail(Email $email): void
    {
        $this->validateRecipients($email);
        $this->validateEmailContent($email);
        $this->validateEmailSize($email);
    }

    /**
     * Validate if one of the emails in $emailBatch is bigger than what we expect to be the limit of most providers for
     * incoming emails
     */
    private function validateEmailSize(Email $email): void
    {
        $emailSize = 0;

        if ($email->text !== null) {
            $emailSize += strlen($email->text);
        }

        if ($email->html !== null) {
            $emailSize += strlen($email->html);
        }

        foreach ($email->attachments as $attachment) {
            $emailSize += strlen(base64_encode($attachment->body));
        }

        if ($emailSize > self::MAX_ACCEPTED_EMAIL_SIZE) {
            throw new MaxSizeExceededException(sprintf(
                'size (%d) exceeds max size (%d)',
                $emailSize,
                self::MAX_ACCEPTED_EMAIL_SIZE
            ));
        }
    }

    private function validateRecipients(Email $email): void
    {
        if (count($email->to) === 0) {
            throw new NoRecipientsException('has no recipients');
        }

        foreach ($email->to as $recipient) {
            if (filter_var($recipient->address, FILTER_VALIDATE_EMAIL) === false) {
                throw new InvalidRecipientException(
                    sprintf('has invalid recipient: %s', $recipient->address)
                );
            }
        }
    }

    private function validateEmailContent(Email $email): void
    {
        if (($email->text === null || $email->text === '') && ($email->html === null || $email->html === '')) {
            throw new EmptyEmailException('has no content');
        }
    }
}
