<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\Email as EmailEntity;
use App\Enum\EmailStatus;
use App\Logging\EmailUuidProcessor;
use App\Logging\TransitionLogContext;
use App\Message\SendEmailMessage;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand(
    name: 'app:resend-email:ids',
    description: 'Resends Emails by Ids.',
    hidden: false,
)]
class ResendEmailsByIdsCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly MessageBusInterface $messageBus,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            // the command help shown when running the command with the "--help" option
            ->setHelp('This command allows you to re-queue Emails for sending.')
            ->addArgument('emailIds', InputArgument::REQUIRED, 'The Email Ids to be resent.')
            ->addOption(
                name: 'force',
                shortcut: 'f',
                mode: InputOption::VALUE_NONE,
                description: 'force resending of emails that have a status that disallows resending them',
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $emailsIdsArgument = $input->getArgument('emailIds');

        if (!is_string($emailsIdsArgument)) {
            $output->writeln([
                'Invalid Input: emailIds must be provided',
                '',
            ]);

            return Command::INVALID;
        }

        $forceSend = $input->getOption('force');

        if (!is_bool($forceSend)) {
            $output->writeln([
                'Invalid Input: force option not correct',
                '',
            ]);

            return Command::INVALID;
        }

        $forceSendText = $forceSend
            ? '<comment>true (The email is resend even if its status is "sent")</comment>'
            : 'false';

        $output->writeln([
            'Re-queueing  Emails for sending',
            '============',
            "Force send = {$forceSendText}",
            '',
        ]);


        $emailsIds = explode(',', $emailsIdsArgument);

        $emails = $this->em->getRepository(EmailEntity::class)->findBy(['id' => $emailsIds]);

        $emailsQueued = [];
        $emailsNotQueued = [];

        foreach ($emails as $email) {
            try {
                EmailUuidProcessor::setProcessedMailUuid($email->getUuid());
                $this->logger->info(message: 'Re-queueing email: start');

                // If the Email is already Sent we don't want to send it agail unless we force this behaviour
                if (!$forceSend && $email->getStatus() === EmailStatus::SENT) {
                    $this->logger->info(
                        message: 'Resending email: aborted - Email is already in status' .
                        ' sent and no --force option was used'
                    );
                    $emailsNotQueued[] = $email->getId();

                    continue;
                }

                $email->setStatus(EmailStatus::QUEUED);

                /** @var int $emailId */
                $emailId = $email->getId();
                $this->messageBus->dispatch(new SendEmailMessage($emailId));

                $this->logger->info(
                    message: 'Email State Change',
                    context: [
                        TransitionLogContext::IDENTIFIER => new TransitionLogContext(
                            toState: 'Queued',
                        ),
                    ],
                );

                $emailsQueued[] = $email->getId();
            } finally {
                $this->logger->info(message: 'Re-queueing email: end');
                EmailUuidProcessor::setProcessedMailUuid(null);
            }
        }

        $this->em->flush();

        $output->writeln([
            'Emails Queued: [' . implode(',', $emailsQueued) . ']',
            $emailsNotQueued !== []
                ? '<error>Emails not Queued: [' . implode(',', $emailsNotQueued) . ']</error>'
                : '',
        ]);

        return Command::SUCCESS;
    }
}
