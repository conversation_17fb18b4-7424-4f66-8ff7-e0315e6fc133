<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\Email;
use App\Service\EmailFileService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\ConsoleOutputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:get-email-content-hashes',
    description: 'Get hashes for the content of the specified emails',
    hidden: false,
)]
class GetEmailContentHashes extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly EmailFileService $fileService,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addArgument(
            'emailIds',
            InputArgument::REQUIRED,
            'IDs of emails whose content is to be hashed'
        );
        $this->addOption(
            'contentLength',
            'cl',
            InputOption::VALUE_REQUIRED,
            'The length of the content to be hashed',
            100
        );
    }

    /**
     * An id for which no email was found is silently dropped
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (!$output instanceof ConsoleOutputInterface) {
            throw new \LogicException('This command accepts only an instance of "ConsoleOutputInterface".');
        }

        $emailIdsArgument = $input->getArgument('emailIds');

        if (!is_string($emailIdsArgument)) {
            return Command::INVALID;
        }

        $emailIds = explode(',', $emailIdsArgument);
        $emails = $this->em->getRepository(Email::class)->findBy(['id' => $emailIds]);

        if (!is_scalar($input->getOption('contentLength'))) {
            $output->writeln(sprintf('<error>Input content length must be a scalar</error>'));

            return Command::FAILURE;
        }

        $contentLength = (int)$input->getOption('contentLength');

        $hasFailedYet = false;

        $emailIdsWithContentHashes = [];

        foreach ($emails as $i => $email) {
            if ($email->getHtmlPath() !== null) {
                $emailContent = $this->fileService->getContent($email->getHtmlPath());
            } elseif ($email->getTextPath() !== null) {
                $emailContent = $this->fileService->getContent($email->getTextPath());
            } else {
                $emailContent = null;
            }

            if ($emailContent !== null) {
                $cleanContent = str_replace("\r\n", "\n", trim($emailContent));
                $subContent = substr($cleanContent, 0, $contentLength);
                $contentHash = md5($subContent);
                $emailId = (string)$email->getId();


                $duplicate = array_search(
                    $contentHash,
                    array_combine(
                        array_keys($emailIdsWithContentHashes),
                        array_column($emailIdsWithContentHashes, 'contentHash')
                    ),
                    true
                );

                if ($duplicate !== false) {
                    $output->writeln("Found duplicate for email Id #{$emailId}: " . $subContent);

                    $emailIdsWithContentHashes[$duplicate]['duplicate'] = true;

                    $emailIdsWithContentHashes[] = [
                        'emailId' => $emailId,
                        'content' => $subContent,
                        'contentHash' => $contentHash,
                        'duplicate' => true,
                    ];

                    continue;
                }

                $emailIdsWithContentHashes[] = [
                    'emailId' => $emailId,
                    'content' => $subContent,
                    'contentHash' => $contentHash,
                    'duplicate' => false,
                ];
            } else {
                if (!$hasFailedYet) {
                    $output->writeln('No content found for the following emails:');
                    $hasFailedYet = true;
                }

                $output->writeln((string)$email->getId());
            }
        }

        $uniqueHashes = array_filter($emailIdsWithContentHashes, static fn($x) => $x['duplicate'] === false);

        $output->writeln(count($uniqueHashes) . ' unique hashes found.');

        $json = json_encode($uniqueHashes);

        if ($json !== false) {
            $output->writeln($json);

            return Command::SUCCESS;
        }

        return Command::FAILURE;
    }
}
