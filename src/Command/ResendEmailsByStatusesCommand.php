<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\Email as EmailEntity;
use App\Enum\EmailStatus;
use App\Logging\EmailUuidProcessor;
use App\Logging\TransitionLogContext;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:resend-email:statuses',
    description: 'Resends Emails by statuses',
    hidden: false,
)]
class ResendEmailsByStatusesCommand extends Command
{
    public function __construct(private readonly EntityManagerInterface $em, private readonly LoggerInterface $logger)
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            // the command help shown when running the command with the "--help" option
            ->setHelp(
                'All emails not older than 24hrs, that are in the declared status(es)' .
                ' will be set to queued and be sent by the queue worker later on.'
            )
            ->addArgument(
                'emailStatuses',
                InputArgument::REQUIRED | InputArgument::IS_ARRAY,
                'The statuses of emails that shall be resent.'
            )
            ->addOption(
                name: 'force',
                shortcut: 'f',
                mode: InputOption::VALUE_NONE,
                description: 'You need to set this option for statuses that are not intended to be resent.',
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $emailStatuses = $input->getArgument('emailStatuses');

        if (!is_array($emailStatuses)) {
            $output->writeln([
                'Invalid Input: email statuses argument is not an array',
                '',
            ]);

            return Command::INVALID;
        }

        $uniqueEmailStatuses = array_unique($emailStatuses);

        foreach ($uniqueEmailStatuses as $i => $emailStatus) {
            try {
                $uniqueEmailStatuses[$i] = EmailStatus::from($emailStatus);
            } catch (\ValueError $err) {
                $output->writeln([
                    'Invalid Input: email status ' . $emailStatus . ' is not valid',
                    '',
                ]);

                return Command::INVALID;
            }
        }

        $forceSend = $input->getOption('force');

        if (!is_bool($forceSend)) {
            $output->writeln([
                'Invalid Input: force option not correct',
                '',
            ]);

            return Command::INVALID;
        }

        if (!$this->em->getRepository(EmailEntity::class)->getResendLock()) {
            $output->writeln(
                'Another instance already locked resending'
            );

            return Command::FAILURE;
        }

        $forceSendText = $forceSend
            ? '<comment>true (emails will be resent, even if their status implies they shouldn\'t)</comment>'
            : 'false';

        $output->writeln([
            'Resending Emails',
            '============',
            "Force send = {$forceSendText}",
            '',
        ]);

        /** @var EmailEntity[] $emails */
        $emails = $this->em->getRepository(EmailEntity::class)->getEmailsToBeResent($uniqueEmailStatuses);

        $emailsQueued = [];
        $emailsNotQueued = [];

        foreach ($emails as $email) {
            try {
                EmailUuidProcessor::setProcessedMailUuid($email->getUuid());
                $this->logger->info(message: 'Resending email: start');

                // Currently only delivery_failed_temporarily is intended to be resent
                if (!$forceSend && !$email->getStatus()->isIntendedToBeResent()) {
                    $this->logger->info(
                        message: 'Resending email: aborted - Email is not in a status intended to ' .
                        'be resent. Use the command with --force option to resend it anyway.'
                    );
                    $emailsNotQueued[] = $email->getId();

                    continue;
                }

                $email->setStatus(EmailStatus::QUEUED);

                $this->logger->info(
                    message: 'Email State Change',
                    context: [
                        TransitionLogContext::IDENTIFIER => new TransitionLogContext(
                            toState: 'queued',
                        ),
                    ],
                );

                $emailsQueued[] = $email->getId();
            } finally {
                $this->logger->info(message: 'Resending email: end');
                EmailUuidProcessor::setProcessedMailUuid(null);
            }
        }

        $this->em->flush();


        $output->writeln([
            'Emails Queued: [' . implode(',', $emailsQueued) . ']',
            $emailsNotQueued !== []
                ? '<error>Emails not Queued: [' . implode(',', $emailsNotQueued) . ']</error>'
                : '',
        ]);

        return Command::SUCCESS;
    }
}
