<?php

declare(strict_types=1);

namespace App\Command;

use App\Converter\EmailEntityConverter;
use App\Entity\Email as EmailEntity;
use App\Exception\EmailMimeConversionException;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:export-email-raw',
    description: 'Export an email as raw MIME message for debugging',
    hidden: false,
)]
class ExportEmailRawCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly EmailEntityConverter $emailEntityConverter,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setHelp('This command takes an email ID and exports the fully composed raw MIME message, '
                . 'including all attachments from S3. This is useful for debugging RFC2231 encoding issues '
                . 'and other MIME problems.')
            ->addArgument('emailId', InputArgument::REQUIRED, 'The Email ID to export')
            ->addOption(
                'output-file',
                'o',
                InputOption::VALUE_REQUIRED,
                'Output file path. If not specified, outputs to stdout'
            )
            ->addOption(
                'headers-only',
                null,
                InputOption::VALUE_NONE,
                'Only output the headers, not the full message body'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $emailIdArgument = $input->getArgument('emailId');

        if (!is_string($emailIdArgument) && !is_numeric($emailIdArgument)) {
            $output->writeln('<error>Invalid Input: emailId must be provided as a number</error>');

            return Command::INVALID;
        }

        $emailId = (int) $emailIdArgument;
        $outputFile = $input->getOption('output-file');
        /** @var bool $headersOnly */
        $headersOnly = $input->getOption('headers-only');

        // Find the email entity
        $email = $this->em->getRepository(EmailEntity::class)->find($emailId);

        if ($email === null) {
            $output->writeln("<error>Email with ID {$emailId} not found</error>");

            return Command::FAILURE;
        }

        $output->writeln([
            'Exporting Email to Raw MIME Format',
            '==================================',
            "Email ID: {$emailId}",
            "Subject: " . ($email->getSubject() ?? '(no subject)'),
            "Status: {$email->getStatus()->value}",
            "User ID: " . ($email->getUserId() ?? 'system'),
            '',
        ]);

        try {
            // Convert the email entity to MIME email using the same process as sending
            $mimeEmail = $this->emailEntityConverter->toMimeEmail($email);

            if ($mimeEmail->isErr()) {
                $output->writeln('<error>Failed to convert email to MIME format:</error>');

                foreach ($mimeEmail->getError() as $error) {
                    $output->writeln("<error>- {$error->getType()->value}: {$error->getCause()}</error>");
                }

                return Command::FAILURE;
            }

            $mimeEmailObject = $mimeEmail->getValue();

            // Generate the raw MIME message
            $rawMessage = $mimeEmailObject->toString();

            if ($headersOnly) {
                // Extract only headers (everything before the first empty line)
                $parts = explode("\r\n\r\n", $rawMessage, 2);
                $rawMessage = $parts[0] . "\r\n\r\n";
            }

            // Output the raw message
            if ($outputFile !== null) {
                if (!is_string($outputFile)) {
                    $output->writeln('<error>Invalid output file path</error>');

                    return Command::INVALID;
                }

                $bytesWritten = file_put_contents($outputFile, $rawMessage);

                if ($bytesWritten === false) {
                    $output->writeln("<error>Failed to write to file: {$outputFile}</error>");

                    return Command::FAILURE;
                }

                $output->writeln([
                    "<info>Raw MIME message exported successfully!</info>",
                    "File: {$outputFile}",
                    "Size: " . number_format(strlen($rawMessage)) . " bytes",
                ]);
            } else {
                $output->writeln('Raw MIME Message:');
                $output->writeln('=================');
                $output->write($rawMessage);
            }

            // Show some debugging information
            $output->writeln([
                '',
                'Debug Information:',
                '==================',
                "Attachments: " . count($email->getAttachments()),
                "Has HTML: " . ($email->getHtmlPath() !== null ? 'Yes' : 'No'),
                "Has Text: " . ($email->getTextPath() !== null ? 'Yes' : 'No'),
            ]);

            if (count($email->getAttachments()) > 0) {
                $output->writeln('Attachment Details:');

                foreach ($email->getAttachments() as $i => $attachment) {
                    $output->writeln(sprintf(
                        "  %d. %s (%s, %s)",
                        $i + 1,
                        $attachment->getName(),
                        $attachment->getContentType(),
                        $attachment->getContentDisposition()->value
                    ));
                }
            }

            return Command::SUCCESS;
        } catch (EmailMimeConversionException $e) {
            $output->writeln('<error>Failed to convert email to MIME format due to validation errors</error>');

            return Command::FAILURE;
        } catch (\Throwable $e) {
            $output->writeln([
                '<error>An unexpected error occurred:</error>',
                "<error>{$e->getMessage()}</error>",
                "<error>File: {$e->getFile()}:{$e->getLine()}</error>",
            ]);

            return Command::FAILURE;
        }
    }
}
