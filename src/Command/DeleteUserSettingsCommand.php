<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\EmailSettings;
use App\Service\EmailSettingsService;
use Carbon\Carbon;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:delete-user-settings',
    description: 'This command calls the events api from professionalworks in order to fetch and delete user settings.',
    hidden: false,
)]
class DeleteUserSettingsCommand extends Command
{
    private const OPTION_FROM_DATE = 'fromDate';
    private const DEFAULT_SUB_MINUTES = 10;

    public function __construct(
        private readonly EmailSettingsService $emailSettingsService,
        private readonly EntityManagerInterface $em,
    ) {
        parent::__construct();
    }

    public function configure()
    {
        $this->addOption(
            self::OPTION_FROM_DATE,
            null,
            InputOption::VALUE_OPTIONAL,
            'The date from which to fetch and delete user settings.',
            Carbon::now()->subMinutes(self::DEFAULT_SUB_MINUTES)
        );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (!$this->em->getRepository(EmailSettings::class)->getDeleteLock()) {
            $output->writeln(
                'Another instance already locked deleting user settings'
            );

            return Command::FAILURE;
        }

        if ($input->getOption(self::OPTION_FROM_DATE) instanceof Carbon) {
            $this->emailSettingsService->fetchAndDelete($input->getOption(self::OPTION_FROM_DATE));
        } else {
            $this->emailSettingsService->fetchAndDelete(Carbon::now()->subMinutes(self::DEFAULT_SUB_MINUTES));
        }

        return Command::SUCCESS;
    }
}
