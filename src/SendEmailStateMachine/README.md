
# Overview of SendEmailStates

The initial state is `Posted`. States are looped as long as they don't reach a final state: `Sent` or `SendingFailed`. Thereby `tryToSend()` is called at each iteration. See `SendEmailStateInterface`.

```mermaid
graph TD
    A[Posted]
    B(Sent)
    C(SendingFailed)
    D[UserTransportFailedOrNull<br>Retry, use fallback]
    E[FallbackTransportFailed<br>Send to support, use fallback]
    F[AttachmentTooLarge<br>Zip attachments, use fallback]
    G[ZippedAttachmentTooLarge<br>Drop attachments, use fallback]
    H{User mayUseFailsafe<br>AND<br>EmailBatch useFailsafeTransport}
    I{classify error}
    J{classify error}
    K(RetryAt)

    A --> |successful| B
    A --> |failure| H
    H --> |false| C
    H --> |true| I
    I --> |ses timeout| K
    I --> |AttachmentTooLarge| F
    I --> |other| D
    D --> |sucess| B
    D --> |failure| E
    E --> |success| B
    E --> |failure| C
    F --> |success| B
    F --> |failure| J
    J --> |AttachmentTooLarge| G
    J --> |other| C
    G --> |success| B
    G --> |failure| C
```
### Rendering
##### PHPStorm
1. Open Settings (`Strg`+`Alt`+`S`)
2. Languages & Frameworks > Markdown (plugin has to be installed before)
3. Enable Mermaid
##### Online
[Mermaid Online Editor](https://mermaid.live/edit#pako:eNqFU11vmzAU_SuWnzqJRBAIIWiqlJSQtto6ac1eCntwwSRogJE_pGWQ_z47OEhQpvFi7rnn3Hvw5TYwISmGPjxSVJ_AIYgrIJ9N9IqrNK-OP7t4eydj_qkLHu50MkR5gVONBtEPhumBoorVhPIu942-iKL4_E7vv2NOzwYQDIMMFcU7Sn7p2rso1MBIrGSqE-AEMFGrxGSBMNpwjpJTKS0eCPmC6BEr7VteA9Rn2KR2H0lWjdPpCgEl_y_x2KgPByU6y1M5Zyi7qjcvgTp2pcS2iCcnpb4R-m-9dFWemqRAjOXZGWBKCdXw80dYTwjMZvegZSJJMGOZKFqwHWQy2UhQ3IJHbfMGF0yCDwOQUyGxJ-2kwz7eSAvCAYPwE6Yt0L9M0BuSfnozwcjMTs984L5n70Zs7TKcZocj9rO-sn_73w8Y2r9usp9ush9bggYsMZUzTeXWNIoUQ1moxDH05WuKMyQKHsO4ukgqEpy8nqsE-uqODSjqFHEc5EjuWwn96zQMiNOcE_q128TrQhqwRtUbIeVNKEPoN_A39C1r7q7N5cpzXNNd27ZrwDP0Z7ZnzZ217djeamGuTNu6GPDPtYA1XywtZ7HwHMv03JWzdC5_AaYaRK8)
