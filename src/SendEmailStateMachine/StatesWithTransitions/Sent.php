<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine\StatesWithTransitions;

use App\SendEmailStateMachine\Exceptions\ActionNotAllowedForFinalStateException;
use Sentry\EventHint;
use Sentry\Severity;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mime\Email;

use function Sentry\captureMessage;

class Sent extends AbstractSendEmailState
{
    public function __construct(private readonly ?SentMessage $sentMessage)
    {
        if (is_null($sentMessage)) {
            $sentryHint = new EventHint();
            captureMessage(
                message: "Sent state was created without SentMessage.",
                level: Severity::error(),
                hint: $sentryHint
            );
        }
    }

    public static function getIdentifier(): string
    {
        return 'Sent';
    }

    /**
     * @codeCoverageIgnore
     */
    public function isFinal(): bool
    {
        return true;
    }

    /**
     * @throws ActionNotAllowedForFinalStateException
     */
    protected function prepare(Email $email): Email
    {
        throw new ActionNotAllowedForFinalStateException(
            __FUNCTION__,
            (new \ReflectionClass($this))->getShortName()
        );
    }

    /**
     * @throws ActionNotAllowedForFinalStateException
     */
    public function tryToSend(Email $email): AbstractSendEmailState
    {
        throw new ActionNotAllowedForFinalStateException(
            __FUNCTION__,
            (new \ReflectionClass($this))->getShortName()
        );
    }

    public function getSentMessage(): ?SentMessage
    {
        return $this->sentMessage;
    }
}
