<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine\StatesWithTransitions;

use App\SendEmailStateMachine\SendEmailContext;
use App\SendEmailStateMachine\SendEmailStateHelper as Helper;
use Symfony\Component\Mime\Email;
use Throwable;

class ZippedAttachmentTooLarge extends AbstractSendEmailState
{
    public function __construct(private readonly SendEmailContext $context)
    {
    }

    public static function getIdentifier(): string
    {
        return 'ZippedAttachmentTooLarge';
    }

    /**
     * @codeCoverageIgnore
     */
    public function isFinal(): bool
    {
        return false;
    }

    protected function prepare(Email $email): Email
    {
        // clear attachments
        $newEmail = Helper::duplicateEmailWithoutAttachments($email);
        $newEmail->subject(
            "[Fehler beim Versand (Anhänge waren auch komprimiert zu groß). "
            . "{$this->context->getOriginalSender()} bitten den Anhang auf mehrere E-Mails zu verteilen.] "
            . $this->context->getOriginalSubject()
        );

        return $newEmail->to($this->context->fallbackRecipient);
    }

    public function tryToSend(Email $email): AbstractSendEmailState
    {
        $email = $this->prepare($email);

        try {
            return new Sent($this->context->getFallbackTransport()->send($email));
        } catch (Throwable) {
            return new SendingFailed();
        }
    }
}
