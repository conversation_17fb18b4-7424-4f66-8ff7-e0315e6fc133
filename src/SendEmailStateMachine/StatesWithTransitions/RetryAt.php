<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine\StatesWithTransitions;

use App\Error\TransportErrorType;
use App\SendEmailStateMachine\Exceptions\ActionNotAllowedForFinalStateException;
use Carbon\CarbonImmutable;
use Symfony\Component\Mime\Email;

class RetryAt extends AbstractSendEmailState
{
    public function __construct(
        public readonly CarbonImmutable $retryAt,
        public readonly ?TransportErrorType $transportErrorType = null,
    ) {
    }

    public static function getIdentifier(): string
    {
        return 'RetryAt';
    }

    public function isFinal(): bool
    {
        return true;
    }

    protected function prepare(Email $email): Email
    {
        throw new ActionNotAllowedForFinalStateException(
            __FUNCTION__,
            (new \ReflectionClass($this))->getShortName()
        );
    }

    public function tryToSend(Email $email): AbstractSendEmailState
    {
        throw new ActionNotAllowedForFinalStateException(
            __FUNCTION__,
            (new \ReflectionClass($this))->getShortName()
        );
    }
}
