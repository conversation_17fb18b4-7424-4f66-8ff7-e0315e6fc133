<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine\StatesWithTransitions;

use App\Error\TransportError;
use App\Error\TransportErrorType;
use App\SendEmailStateMachine\SendEmailContext;
use App\SendEmailStateMachine\SendEmailStateHelper as Helper;
use Symfony\Component\Mime\Email;
use Throwable;

class AttachmentTooLarge extends AbstractSendEmailState
{
    public function __construct(public readonly SendEmailContext $context)
    {
    }

    public static function getIdentifier(): string
    {
        return 'AttachmentTooLarge';
    }

    /**
     * @codeCoverageIgnore
     */
    public function isFinal(): bool
    {
        return false;
    }

    protected function prepare(Email $email): Email
    {
        $attachments = $email->getAttachments();
        $pathToZip = Helper::zip($attachments);
        $newEmail = Helper::duplicateEmailWithoutAttachments($email);
        $newEmail->attachFromPath($pathToZip);

        $newEmail->subject(
            "[<PERSON><PERSON> beim <PERSON>and (<PERSON><PERSON><PERSON><PERSON> zu groß). "
            . "Bitte manuell an [{$this->context->getOriginalRecipientsAsList()}] weiterleiten.] "
            . $this->context->getOriginalSubject()
        );

        return $newEmail->to($this->context->fallbackRecipient);
    }

    public function tryToSend(Email $email): AbstractSendEmailState
    {
        $email = $this->prepare($email);

        try {
            return new Sent($this->context->getFallbackTransport()->send($email));
        } catch (Throwable $e) {
            return $this->handleTransportError(TransportError::fromSmtpErrorMessage($e->getMessage()));
        }
    }

    private function handleTransportError(TransportError $error): AbstractSendEmailState
    {
        return match ($error->getType()) {
            TransportErrorType::ATTACHMENT_TOO_LARGE => new ZippedAttachmentTooLarge($this->context),
            default => new SendingFailed()
        };
    }
}
