<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine\StatesWithTransitions;

use App\Error\TransportError;
use App\Error\TransportErrorType;
use App\SendEmailStateMachine\SendEmailContext;
use App\Transport\ParentFallbackTransportFactory;
use Carbon\CarbonImmutable;
use Symfony\Component\Mime\Email;
use Throwable;

class UserTransportFailedOrNull extends AbstractSendEmailState
{
    public function __construct(
        private SendEmailContext $context,
        private ParentFallbackTransportFactory $parentFallbackTransportFactory
    ) {
    }

    public static function getIdentifier(): string
    {
        return 'UserTransportFailedOrNull';
    }

    /**
     * @codeCoverageIgnore
     */
    public function isFinal(): bool
    {
        return false;
    }

    protected function prepare(Email $email): Email
    {
        return $email;
    }

    public function tryToSend(Email $email): AbstractSendEmailState
    {
        //Fallback to parent transport
        $parentTransport = $this->parentFallbackTransportFactory->createForUser($this->context->getUser());

        if ($parentTransport !== null) {
            try {
                $sent = $parentTransport->send($email);

                if ($sent !== null) {
                    return new Sent($sent);
                }
            } catch (Throwable) {
                // Do nothing. The Error is Logged by the LoggedTransport decorator.
                // Keep trying with the fallbacktransport if "allowed".
            }
        }

        // only proceed if the user is allowed to use the failsafe transport
        if (!$this->context->user->mayUseFailsafeTransport() || !$this->context->useFailsafeTransport) {
            return new SendingFailed();
        }

        // Fallback to SystemFallback transport
        try {
            $sentMessage = $this->context->getFallbackTransport()->send($email);

            if ($sentMessage === null) {
                return new FallbackTransportFailed($this->context);
            }

            return new Sent($sentMessage);
        } catch (Throwable $t) {
            return $this->handleTransportError(TransportError::fromSmtpErrorMessage($t->getMessage()));
        }
    }

    private function handleTransportError(TransportError $err): AbstractSendEmailState
    {
        return match ($err->getType()) {
            TransportErrorType::TIMEOUT_SES => new RetryAt((new CarbonImmutable())->addMinutes(3)),
            default => new FallbackTransportFailed($this->context),
        };
    }
}
