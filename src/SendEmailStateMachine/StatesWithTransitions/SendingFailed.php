<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine\StatesWithTransitions;

use App\SendEmailStateMachine\Exceptions\ActionNotAllowedForFinalStateException;
use Symfony\Component\Mime\Email;

class SendingFailed extends AbstractSendEmailState
{
    /**
     * @codeCoverageIgnore
     */
    public function isFinal(): bool
    {
        return true;
    }

    public static function getIdentifier(): string
    {
        return 'SendingFailed';
    }

    /**
     * @throws ActionNotAllowedForFinalStateException
     */
    protected function prepare(Email $email): Email
    {
        throw new ActionNotAllowedForFinalStateException(
            __FUNCTION__,
            (new \ReflectionClass($this))->getShortName()
        );
    }

    /**
     * @throws ActionNotAllowedForFinalStateException
     */
    public function tryToSend(Email $email): AbstractSendEmailState
    {
        throw new ActionNotAllowedForFinalStateException(
            __FUNCTION__,
            (new \ReflectionClass($this))->getShortName()
        );
    }
}
