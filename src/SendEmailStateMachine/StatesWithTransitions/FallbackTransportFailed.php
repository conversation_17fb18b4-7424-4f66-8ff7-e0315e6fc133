<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine\StatesWithTransitions;

use App\SendEmailStateMachine\SendEmailContext;
use Symfony\Component\Mime\Email;
use Throwable;

class FallbackTransportFailed extends AbstractSendEmailState
{
    public function __construct(public readonly SendEmailContext $context)
    {
    }

    public static function getIdentifier(): string
    {
        return 'FallbackTransportFailed';
    }

    /**
     * @codeCoverageIgnore
     */
    public function isFinal(): bool
    {
        return false;
    }

    protected function prepare(Email $email): Email
    {
        $email->subject(
            "[<PERSON><PERSON> beim <PERSON>, bitte manuell an {$this->context->getOriginalRecipientsAsList()} weiterleiten]"
            . $this->context->getOriginalSubject()
        );

        return $email->to($this->context->fallbackRecipient);
    }

    public function tryToSend(Email $email): AbstractSendEmailState
    {
        $email = $this->prepare($email);

        try {
            return new Sent($this->context->getFallbackTransport()->send($email));
        } catch (Throwable $e) {
            return new SendingFailed();
        }
    }
}
