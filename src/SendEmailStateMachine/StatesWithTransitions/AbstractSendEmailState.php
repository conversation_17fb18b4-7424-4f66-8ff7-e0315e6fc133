<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine\StatesWithTransitions;

use Symfony\Component\Mime\Email;

abstract class AbstractSendEmailState
{
    abstract public function isFinal(): bool;

    abstract protected function prepare(Email $email): Email;

    abstract public function tryToSend(Email $email): AbstractSendEmailState;

    abstract public static function getIdentifier(): string;
}
