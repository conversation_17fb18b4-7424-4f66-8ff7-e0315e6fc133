<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine\Exceptions;

use LogicException;
use Throwable;

class ActionNotAllowedForFinalStateException extends LogicException
{
    public function __construct(
        string $action,
        string $state,
        string $additions = "",
        int $code = 0,
        ?Throwable $previous = null
    ) {
        $message = "As '{$state}' is a final state, it's not possible to call '{$action}' from this state.";

        parent::__construct($message . $additions, $code, $previous);
    }
}
