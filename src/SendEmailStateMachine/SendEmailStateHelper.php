<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine;

use DateTimeImmutable;
use Demv\Tmpdir\TmpDirRegistry;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Part\DataPart;
use ZipArchive;

class SendEmailStateHelper
{
    public static function duplicateEmailWithoutAttachments(Email $oldMessage): Email
    {
        $message = new Email();

        $message->from(...$oldMessage->getFrom())
            ->to(...$oldMessage->getTo())
            ->replyTo(...$oldMessage->getReplyTo())
            ->cc(...$oldMessage->getCc())
            ->subject($oldMessage->getSubject() ?? '')
            ->html($oldMessage->getHtmlBody())
            ->text($oldMessage->getTextBody())
            ->priority($oldMessage->getPriority())
            ->date($oldMessage->getDate() ?? new DateTimeImmutable());

        if ($oldMessage->getReturnPath() !== null) {
            $message->returnPath($oldMessage->getReturnPath());
        }

        if ($oldMessage->getSender() !== null) {
            $message->sender($oldMessage->getSender());
        }

        return $message;
    }

    /**
     * Make sure there are actually attachments and not only an empty array
     * otherwise this method creates an empty zip archive
     *
     * @param array|DataPart[] $attachments
     *
     * @return string Contains the path of the zip file
     */
    public static function zip(array $attachments): string
    {
        $tmpDir = TmpDirRegistry::instance()->createDirInSystemTmp('mail/attachments/');
        $filename = uniqid('Anhaenge-');
        $path = $tmpDir . $filename . '.zip';
        $za = new ZipArchive();
        $za->open($path, ZipArchive::CREATE | ZipArchive::OVERWRITE);

        foreach ($attachments as $i => $attachment) {
            $za->addFromString(
                $attachment->getName() ?? $filename . '_' . ($i + 1),
                $attachment->getBody()
            );
        }

        $za->close();

        return $path;
    }
}
