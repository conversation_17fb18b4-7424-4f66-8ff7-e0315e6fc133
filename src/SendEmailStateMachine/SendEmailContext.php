<?php

declare(strict_types=1);

namespace App\SendEmailStateMachine;

use App\Entity\EmailSettings;
use App\Security\UserInterface;
use App\Transport\TransportInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;

class SendEmailContext
{
    public readonly Email $originalEmail;

    public function __construct(
        Email $email,
        public readonly UserInterface $user,
        private readonly ?TransportInterface $userTransport,
        private readonly TransportInterface $fallbackTransport,
        public readonly Address $fallbackRecipient,
        public readonly bool $useFailsafeTransport,
        public readonly ?EmailSettings $emailSettings,
    ) {
        $this->originalEmail = clone $email;
    }

    public function getUserTransport(): ?TransportInterface
    {
        return $this->userTransport;
    }

    public function getFallbackTransport(): TransportInterface
    {
        return $this->fallbackTransport;
    }

    public function getOriginalSubject(): string
    {
        return $this->originalEmail->getSubject() ?? '';
    }

    public function getOriginalSender(): string
    {
        return $this->originalEmail->getFrom()[0]->toString();
    }

    public function getOriginalRecipientsAsList(): string
    {
        return implode(
            ', ',
            array_map(static fn($address) => $address->toString(), $this->originalEmail->getTo())
        );
    }

    public function getUser(): UserInterface
    {
        return $this->user;
    }
}
