<?php

declare(strict_types=1);

namespace App\Entity;

use App\Enum\EmailHistoryType;
use App\Repository\EmailHistoryRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EmailHistoryRepository::class)]
#[ORM\InheritanceType('JOINED')]
#[ORM\DiscriminatorColumn(name:'email_history_type', type:'string')]
#[ORM\DiscriminatorMap(['state_change' => EmailHistoryTransition::class, 'error' => EmailHistoryError::class])]
#[ORM\HasLifecycleCallbacks]
class EmailHistory
{
    use UpdatedAtCreatedAtTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    protected ?int $id = null;

    #[ORM\Column(name: 'user_id', nullable: true)]
    protected ?int $userId;

    #[ORM\Column(name: 'service_id', nullable: true)]
    protected ?string $serviceId;

    #[ORM\Column(name: 'uid', nullable: true)]
    protected ?string $uId;

    #[ORM\ManyToOne(inversedBy: 'history')]
    protected ?Email $email = null;

    protected EmailHistoryType $emailHistoryType;

    //The Server on which the error happened
    #[ORM\Column(length: 255)]
    protected string $server;

    public function __construct(
        string $server,
        EmailHistoryType $emailHistoryType,
        ?string $uId = null,
        ?int $userId = null,
        ?string $serviceId = null,
    ) {
        $this->server = $server;
        $this->uId = $uId;
        $this->userId = $userId;
        $this->serviceId = $serviceId;
        $this->emailHistoryType = $emailHistoryType;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmail(): ?Email
    {
        return $this->email;
    }

    public function setEmail(?Email $email): static
    {
        $this->email = $email;

        return $this;
    }

    public function getUId(): ?string
    {
        return $this->uId;
    }

    public function setUId(?string $uId): EmailHistory
    {
        $this->uId = $uId;

        return $this;
    }

    public function getServer(): string
    {
        return $this->server;
    }

    public function setServer(string $server): static
    {
        $this->server = $server;

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setUserId(?int $userId): EmailHistory
    {
        $this->userId = $userId;

        return $this;
    }

    public function getServiceId(): ?string
    {
        return $this->serviceId;
    }

    public function setServiceId(?string $serviceId): EmailHistory
    {
        $this->serviceId = $serviceId;

        return $this;
    }

    public function getEmailHistoryType(): EmailHistoryType
    {
        return $this->emailHistoryType;
    }
}
