<?php

declare(strict_types=1);

namespace App\Entity;

use Ambta\DoctrineEncryptBundle\Configuration\Encrypted;
use App\Enum\ConnectionStatus;
use App\Enum\EmailTransportScheme;
use App\Repository\SmtpImapConnectionRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: SmtpImapConnectionRepository::class)]
class SmtpImapConnection extends EmailSettings
{
    protected EmailTransportScheme $transportScheme = EmailTransportScheme::SMTP;

    #[ORM\Column(name: 'smtp_host', type: 'string', length: 255)]
    private string $smtpHost;

    #[ORM\Column(name: 'smtp_port', type: 'integer')]
    private int $smtpPort;

    #[ORM\Column(name: 'imap_host', type: 'string', length: 255, nullable: true)]
    private ?string $imapHost;

    #[ORM\Column(name: 'imap_port', type: 'integer', nullable: true)]
    private ?int $imapPort;

    #[ORM\Column(type: 'string', length: 255)]
    private string $username;

    #[Encrypted]
    #[ORM\Column(type: 'string', length: 1024)]
    private string $password;

    public function __construct(
        int $userId,
        string $email,
        string $smtpHost,
        int $smtpPort,
        string $username,
        #[\SensitiveParameter] string $password,
        ?string $imapHost = null,
        ?int $imapPort = null,
        ?string $senderName = null,
        ?ConnectionStatus $sendingStatus = null,
        ?ConnectionStatus $receivingStatus = null,
    ) {
        parent::__construct(
            $userId,
            $email,
            EmailTransportScheme::SMTP,
            $senderName,
            $sendingStatus,
            $receivingStatus,
        );

        $this->smtpHost = $smtpHost;
        $this->smtpPort = $smtpPort;
        $this->imapHost = $imapHost;
        $this->imapPort = $imapPort;
        $this->username = $username;
        $this->password = $password;
    }

    public function getSmtpHost(): string
    {
        return $this->smtpHost;
    }

    public function setSmtpHost(string $smtpHost): self
    {
        $this->smtpHost = $smtpHost;

        return $this;
    }

    public function getSmtpPort(): ?int
    {
        return $this->smtpPort;
    }

    public function setSmtpPort(int $smtpPort): self
    {
        $this->smtpPort = $smtpPort;

        return $this;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function setUsername(string $username): self
    {
        $this->username = $username;

        return $this;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function setPassword(#[\SensitiveParameter] string $password): self
    {
        $this->password = $password;

        return $this;
    }

    public function canSend(): bool
    {
        return !$this->isIncomplete() &&
            $this->smtpHost !== '' &&
            $this->username !== '' &&
            $this->password !== '' &&
            $this->smtpPort !== 0;
    }

    public function setImapHost(string $imapHost): SmtpImapConnection
    {
        $this->imapHost = $imapHost;

        return $this;
    }

    public function getImapHost(): ?string
    {
        return $this->imapHost;
    }

    public function setImapPort(int $imapPort): SmtpImapConnection
    {
        $this->imapPort = $imapPort;

        return $this;
    }

    public function getImapPort(): ?int
    {
        return $this->imapPort;
    }

    public function canReceive(): bool
    {
        return $this->getImapPort() !== null && $this->getImapHost() !== null;
    }
}
