<?php

declare(strict_types=1);

namespace App\Entity;

use DateTimeImmutable;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Mapping as ORM;

trait UpdatedAtCreatedAtTrait
{
    #[ORM\Column(type: 'datetime_immutable')]
    protected ?DateTimeImmutable $created_at = null;

    #[ORM\Column(type: 'datetime_immutable')]
    protected ?DateTimeImmutable $updated_at = null;

    #[ORM\PrePersist]
    public function prePersist(): void
    {
        $this->created_at = $this->updated_at = new DateTimeImmutable();
    }

    #[ORM\PreUpdate]
    public function preUpdate(PreUpdateEventArgs $event): void
    {
        $this->updated_at = new DateTimeImmutable();
    }

    public function getCreatedAt(): ?DateTimeImmutable
    {
        return $this->created_at;
    }

    public function setCreatedAt(DateTimeImmutable $created_at): self
    {
        $this->created_at = $created_at;

        return $this;
    }

    public function getUpdatedAt(): ?DateTimeImmutable
    {
        return $this->updated_at;
    }

    public function setUpdatedAt(DateTimeImmutable $updated_at): self
    {
        $this->updated_at = $updated_at;

        return $this;
    }
}
