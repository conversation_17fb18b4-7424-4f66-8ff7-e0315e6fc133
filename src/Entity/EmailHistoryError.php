<?php

declare(strict_types=1);

namespace App\Entity;

use App\Enum\EmailHistoryType;
use App\Error\ErrorInterface;
use App\Repository\EmailHistoryErrorRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EmailHistoryErrorRepository::class)]
#[ORM\HasLifecycleCallbacks]
class EmailHistoryError extends EmailHistory
{
    #[ORM\Column(type: 'string')]
    protected string $kind;

    #[ORM\Column(type: 'string')]
    private string $type;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $cause;

    #[ORM\Column(name: 'debug_info', type: Types::TEXT, nullable: true)]
    private ?string $debugInfo;

    #[ORM\Column(length: 511, nullable: true)]
    protected ?string $dsn;

    protected EmailHistoryType $emailHistoryType = EmailHistoryType::ERROR;

    public function __construct(
        ErrorInterface $error,
        string $server,
        ?string $debugInfo = null,
        ?string $uId = null,
        ?int $userId = null,
        ?string $serviceId = null,
        ?string $dsn = null,
    ) {
        parent::__construct(
            server: $server,
            emailHistoryType: EmailHistoryType::ERROR,
            uId: $uId,
            userId: $userId,
            serviceId: $serviceId,
        );

        $this->kind = $error->getKind();
        $this->type = $error->getTypeString();
        $this->cause = $error->getCause();
        $this->dsn = $dsn;
        $this->debugInfo = $debugInfo;
    }

    public function getDebugInfo(): ?string
    {
        return $this->debugInfo;
    }

    public function setDebugInfo(?string $debugInfo): self
    {
        $this->debugInfo = $debugInfo;

        return $this;
    }

    public function getKind(): string
    {
        return $this->kind;
    }

    public function setKind(string $kind): static
    {
        $this->kind = $kind;

        return $this;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getCause(): ?string
    {
        return $this->cause;
    }

    public function setCause(?string $cause): self
    {
        $this->cause = $cause;

        return $this;
    }
}
