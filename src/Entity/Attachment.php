<?php

declare(strict_types=1);

namespace App\Entity;

use App\Enum\AttachmentContentDisposition;
use App\Repository\AttachmentRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: AttachmentRepository::class)]
class Attachment
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private int $id;

    #[ORM\ManyToOne(targetEntity: Email::class, inversedBy: 'attachments')]
    #[ORM\JoinColumn(name: 'email_id', referencedColumnName: 'id')]
    private Email $email;

    #[ORM\Column(type: 'string', length: 255)]
    private string $name;

    #[ORM\Column(name: "content_type", type: 'string', length: 255)]
    private string $contentType;

    #[ORM\Column(name: "body_path", type: 'path', length: 255, nullable: true)]
    private ?Path $bodyPath = null;

    #[ORM\Column(
        name: "content_disposition",
        length: 255,
        enumType: AttachmentContentDisposition::class,
        options: ['default' => AttachmentContentDisposition::ATTACHMENT]
    )]
    private AttachmentContentDisposition $contentDisposition;

    #[ORM\Column(name: "content_id", type: 'string', length: 255, nullable: true)]
    private ?string $contentId;

    #[ORM\Column(name: "size", type: 'integer', nullable: true)]
    private ?int $size;

    public function __construct(
        string $name,
        string $contentType,
        AttachmentContentDisposition $contentDisposition = AttachmentContentDisposition::ATTACHMENT,
        ?string $contendId = null
    ) {
        $this->name = $name;
        $this->contentType = $contentType;
        $this->contentDisposition = $contentDisposition;
        $this->contentId = $contendId;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getContentType(): string
    {
        return $this->contentType;
    }

    public function setContentType(string $ContentType): self
    {
        $this->contentType = $ContentType;

        return $this;
    }

    public function getBodyPath(): ?Path
    {
        return $this->bodyPath;
    }

    public function setBodyPath(Path $bodyPath): self
    {
        $this->bodyPath = $bodyPath;

        return $this;
    }

    public function setEmail(Email $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getEmail(): Email
    {
        return $this->email;
    }

    public function setContentDisposition(AttachmentContentDisposition $contentDisposition): Attachment
    {
        $this->contentDisposition = $contentDisposition;

        return $this;
    }

    public function getContentDisposition(): AttachmentContentDisposition
    {
        return $this->contentDisposition;
    }

    public function setContentId(?string $contentId): Attachment
    {
        $this->contentId = $contentId;

        return $this;
    }

    public function getContentId(): ?string
    {
        return $this->contentId;
    }

    public function setSize(?int $size): Attachment
    {
        $this->size = $size;

        return $this;
    }

    public function getSize(): ?int
    {
        return $this->size;
    }
}
