<?php

declare(strict_types=1);

namespace App\Entity;

use App\Enum\EmailHistoryType;
use App\Repository\EmailHistoryTransitionRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EmailHistoryTransitionRepository::class)]
#[ORM\HasLifecycleCallbacks]
class EmailHistoryTransition extends EmailHistory
{
    #[ORM\Column(type: 'string')]
    private ?string $fromState;

    #[ORM\Column(type: 'string')]
    private ?string $toState;

    protected EmailHistoryType $emailHistoryType = EmailHistoryType::TRANSITION;

    public function __construct(
        string $server,
        string $fromState,
        string $toState,
        ?string $uId = null,
        ?int $userId = null,
        ?string $serviceId = null,
    ) {
        parent::__construct(
            server: $server,
            emailHistoryType: EmailHistoryType::TRANSITION,
            uId: $uId,
            userId: $userId,
            serviceId: $serviceId,
        );

        $this->fromState = $fromState;
        $this->toState = $toState;
    }

    public function getFromState(): ?string
    {
        return $this->fromState;
    }

    public function getToState(): ?string
    {
        return $this->toState;
    }
}
