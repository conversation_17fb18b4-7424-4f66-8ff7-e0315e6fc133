<?php

declare(strict_types=1);

namespace App\Entity;

use Ambta\DoctrineEncryptBundle\Configuration\Encrypted;
use App\Enum\ConnectionStatus;
use App\Enum\EmailTransportScheme;
use App\Repository\OAuth2ConnectionRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OAuth2ConnectionRepository::class)]
class OAuth2Connection extends EmailSettings
{
    protected EmailTransportScheme $transportScheme = EmailTransportScheme::API;

    #[ORM\Column(type: 'string', length: 255)]
    private string $provider;

    #[Encrypted]
    #[ORM\Column(type: 'text', length: 65535)]
    private string $access_token;

    #[Encrypted]
    #[ORM\Column(type: 'text', length: 65535)]
    private string $refresh_token;

    #[ORM\Column(type: 'datetime_immutable')]
    private DateTimeImmutable $expires_at;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $data;

    #[ORM\Column(type: 'string', length: 255)]
    private ?string $provider_user_id;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $provider_nickname;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $provider_name;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $provider_email;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $provider_avatar;

    public function __construct(
        int $userId,
        string $email,
        string $provider,
        #[\SensitiveParameter] string $access_token,
        #[\SensitiveParameter] string $refresh_token,
        DateTimeImmutable $expires_at,
        ?string $data,
        ?string $provider_user_id,
        ?string $provider_nickname,
        ?string $provider_name,
        ?string $provider_email,
        ?string $provider_avatar,
        ?string $senderName = null,
        ?ConnectionStatus $sendingStatus = null,
        ?ConnectionStatus $receivingStatus = null,
    ) {
        parent::__construct($userId, $email, EmailTransportScheme::API, $senderName, $sendingStatus, $receivingStatus);

        $this->provider = $provider;
        $this->access_token = $access_token;
        $this->refresh_token = $refresh_token;
        $this->expires_at = $expires_at;
        $this->data = $data;
        $this->provider_user_id = $provider_user_id;
        $this->provider_nickname = $provider_nickname;
        $this->provider_name = $provider_name;
        $this->provider_email = $provider_email;
        $this->provider_avatar = $provider_avatar;
    }

    public function getProvider(): string
    {
        return $this->provider;
    }

    public function setProvider(string $provider): self
    {
        $this->provider = $provider;

        return $this;
    }

    public function getAccessToken(): ?string
    {
        return $this->access_token;
    }

    public function setAccessToken(string $access_token): self
    {
        $this->access_token = $access_token;

        return $this;
    }

    public function getRefreshToken(): ?string
    {
        return $this->refresh_token;
    }

    public function setRefreshToken(string $refresh_token): self
    {
        $this->refresh_token = $refresh_token;

        return $this;
    }

    public function getExpiresAt(): ?DateTimeImmutable
    {
        return $this->expires_at;
    }

    public function setExpiresAt(DateTimeImmutable $expires_at): self
    {
        $this->expires_at = $expires_at;

        return $this;
    }

    public function getData(): ?string
    {
        return $this->data;
    }

    public function setData(?string $data): self
    {
        $this->data = $data;

        return $this;
    }

    public function getProviderUserId(): ?string
    {
        return $this->provider_user_id;
    }

    public function setProviderUserId(?string $provider_user_id): self
    {
        $this->provider_user_id = $provider_user_id;

        return $this;
    }

    public function getProviderNickname(): ?string
    {
        return $this->provider_nickname;
    }

    public function setProviderNickname(?string $provider_nickname): self
    {
        $this->provider_nickname = $provider_nickname;

        return $this;
    }

    public function getProviderName(): ?string
    {
        return $this->provider_name;
    }

    public function setProviderName(?string $provider_name): self
    {
        $this->provider_name = $provider_name;

        return $this;
    }

    public function getProviderEmail(): ?string
    {
        return $this->provider_email;
    }

    public function setProviderEmail(?string $provider_email): self
    {
        $this->provider_email = $provider_email;

        return $this;
    }

    public function getProviderAvatar(): ?string
    {
        return $this->provider_avatar;
    }

    public function setProviderAvatar(?string $provider_avatar): self
    {
        $this->provider_avatar = $provider_avatar;

        return $this;
    }

    public function canSend(): bool
    {
        return !$this->isIncomplete()
            && $this->provider !== ''
            && $this->access_token !== ''
            && $this->refresh_token !== '';
    }

    public function canReceive(): bool
    {
        return $this->provider !== ''
            && $this->access_token !== ''
            && $this->refresh_token !== '';
    }
}
