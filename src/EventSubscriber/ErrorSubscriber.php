<?php

declare(strict_types=1);

namespace App\EventSubscriber;

use App\DTO\Response;
use App\Util\EmailHistoryLogger;
use Sentry\Event;
use Sentry\EventHint;
use Sentry\ExceptionMechanism;
use Symfony\Component\Console\Event\ConsoleErrorEvent;
use Symfony\Component\ErrorHandler\Error\OutOfMemoryError;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

use function Sentry\captureEvent;

class ErrorSubscriber implements EventSubscriberInterface
{
    public function __construct(private readonly EmailHistoryLogger $logger)
    {
    }

    public function onKernelException(ExceptionEvent $event): void
    {
        $exception = $event->getThrowable();

        // NotFoundHttpException occur very often and there is no point in logging them
        if ($exception instanceof NotFoundHttpException) {
            return;
        }

        if ($exception instanceof HttpException) {
            $status = $exception->getStatusCode();
        } elseif ($exception instanceof OutOfMemoryError) {
            $status = SymfonyResponse::HTTP_INSUFFICIENT_STORAGE;
        } else {
            $this->logger->unknownError($exception->getMessage(), $exception->getTraceAsString());
            $this->logToSentry($exception);
        }

        $response = new JsonResponse(Response::error('Request konnte nicht verarbeitet werden.'), $status ?? 500);

        $event->setResponse($response);
    }

    public function onConsoleError(ConsoleErrorEvent $event): void
    {
        $error = $event->getError();
        $this->logger->unknownError($error->getMessage(), $error->getTraceAsString());
        $this->logToSentry($error);

        $output = $event->getOutput();
        $output->writeln(PHP_EOL . 'ABORTED! Due to the following error: ' . $error->getMessage() . PHP_EOL);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ExceptionEvent::class => 'onKernelException',
            ConsoleErrorEvent::class => 'onConsoleError',
        ];
    }

    private function logToSentry(Throwable $exception): void
    {
        $hint = EventHint::fromArray([
            'exception' => $exception,
            'mechanism' => new ExceptionMechanism(ExceptionMechanism::TYPE_GENERIC, false),
        ]);

        captureEvent(Event::createEvent(), $hint);
    }
}
