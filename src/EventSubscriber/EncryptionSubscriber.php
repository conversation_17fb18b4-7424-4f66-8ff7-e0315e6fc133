<?php

declare(strict_types=1);

namespace App\EventSubscriber;

use Ambta\DoctrineEncryptBundle\Subscribers\DoctrineEncryptSubscriber;
use App\Entity\EmailSettings;
use Doctrine\ORM\EntityManagerInterface;

class EncryptionSubscriber extends DoctrineEncryptSubscriber
{
    public function processFields(
        object $entity,
        EntityManagerInterface $entityManager,
        bool $isEncryptOperation = true
    ): ?object {
        if (!$entity instanceof EmailSettings) {
            return null;
        }

        return parent::processFields($entity, $entityManager, $isEncryptOperation);
    }
}
