<?php

declare(strict_types=1);

namespace App\Logging;

use Monolog\LogRecord;
use Symfony\Component\Uid\Uuid;

class EmailUuidProcessor
{
    private static ?string $processedMailUuid = null;

    public static function setProcessedMailUuid(?Uuid $processedMailUuid): void
    {
        self::$processedMailUuid = (string)$processedMailUuid;
    }

    public function __invoke(LogRecord $record): LogRecord
    {
        $record->extra['email_uuid'] = self::$processedMailUuid;

        return $record;
    }
}
