<?php

declare(strict_types=1);

namespace App\Logging;

use App\Entity\Email;
use App\Entity\EmailHistoryError;
use App\Entity\EmailHistoryTransition;
use Doctrine\ORM\EntityManagerInterface;
use Monolog\Handler\AbstractHandler;
use Monolog\Level;
use Monolog\LogRecord;
use Sentry\Event;
use Sentry\EventHint;
use Sentry\ExceptionMechanism;

use function Sentry\captureEvent;

class EmailHistoryHandler extends AbstractHandler
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        int|string|Level $level = Level::Debug,
        bool $bubble = true,
    ) {
        parent::__construct($level, $bubble);
    }

    public function handle(LogRecord $record): bool
    {
        try {
            $server = $record->extra['server'] ?? 'unknown';
            $userId = $record->extra['user_id'] ?? null;
            $serviceId = $record->extra['service_id'] ?? null;
            $emailUuid = $record->extra['email_uuid'] ?? null;
            $uId = $record->extra['uid'] ?? null;

            $email = $emailUuid !== null ?
                $this->em->getRepository(Email::class)->findOneBy(['uuid' => $emailUuid])
                : null;

            if (isset($record->context[ErrorLogContext::IDENTIFIER])) {
                /** @var ErrorLogContext $errorLogContext */
                $errorLogContext = $record->context[ErrorLogContext::IDENTIFIER];
                $history = new EmailHistoryError(
                    error: $errorLogContext->error,
                    server: is_string($server) ? $server : 'unknown',
                    debugInfo: $errorLogContext->debugInfo,
                    uId: is_string($uId) ? $uId : null,
                    userId: is_numeric($userId) ? (int)$userId : null,
                    serviceId: is_string($serviceId) ? $serviceId : null,
                    dsn: $errorLogContext->transport,
                );
                $email?->addHistory($history);
                $this->em->persist($history);
            }

            if (isset($record->context[TransitionLogContext::IDENTIFIER])) {
                /** @var TransitionLogContext $transitionLogContext */
                $transitionLogContext = $record->context[TransitionLogContext::IDENTIFIER];
                $history = new EmailHistoryTransition(
                    server: is_string($server) ? $server : 'unknown',
                    fromState: $transitionLogContext->fromState ?? '',
                    toState: $transitionLogContext->toState ?? '',
                    uId: is_string($uId) ? $uId : null,
                    userId: is_numeric($userId) ? (int)$userId : null,
                    serviceId: is_string($serviceId) ? $serviceId : null,
                );
                $email?->addHistory($history);
                $this->em->persist($history);
            }

            return false;
        } catch (\Throwable $exception) {
            $hint = EventHint::fromArray([
                'exception' => $exception,
                'mechanism' => new ExceptionMechanism(ExceptionMechanism::TYPE_GENERIC, true),
            ]);

            captureEvent(Event::createEvent(), $hint);

            return false;
        }
    }
}
