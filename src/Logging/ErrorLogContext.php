<?php

declare(strict_types=1);

namespace App\Logging;

use App\Error\ErrorInterface;

class ErrorLogContext
{
    public const IDENTIFIER = "email_error";

    public function __construct(
        public readonly ErrorInterface $error,
        public readonly ?string $debugInfo = null,
        public readonly ?string $transport = null,
    ) {
    }

    /**
     * @return array<mixed>
     */
    public function toArray(): array
    {
        $data = (array)$this;
        $data['errorType'] = "{$this->error->getKind()}: {$this->error->getTypeString()}";

        return $data;
    }
}
