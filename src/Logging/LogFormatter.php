<?php

declare(strict_types=1);

namespace App\Logging;

use Monolog\Formatter\JsonFormatter;
use Monolog\LogRecord;

class LogFormatter extends JsonFormatter
{
    public function normalizeRecord(LogRecord $record): array
    {
        $normalized = parent::normalizeRecord($record);
        $normalized['user_id'] = is_array($normalized['extra'])
            ? $normalized['extra']['user_id'] ?? null
            : null;
        $normalized['level'] = $record->level->toRFC5424Level();

        return $normalized;
    }
}
