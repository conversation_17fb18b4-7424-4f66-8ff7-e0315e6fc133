<?php

declare(strict_types=1);

namespace App\Logging;

use App\Security\UserInterface;
use Monolog\LogRecord;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class UserIdProcessor
{
    private static ?int $userId = null;

    public function __construct(private readonly TokenStorageInterface $tokenStorage)
    {
    }

    public static function setUserId(?int $userId): void
    {
        self::$userId = $userId;
    }

    public function __invoke(LogRecord $record): LogRecord
    {
        // If there is no Token the process was started by the mailer (presumably a cli command)
        $record->extra['service_id'] = 'mailer';

        if (self::$userId !== null) {
            $record->extra['user_id'] = self::$userId;

            return $record;
        }

        $token = $this->tokenStorage->getToken();

        if ($token === null) {
            return $record;
        }

        if ($token->getUser() instanceof UserInterface) {
            $record->extra['service_id'] = $token->getUser()->getServiceId();
        }

        $record->extra['user_id'] = $token->getUserIdentifier();

        return $record;
    }
}
