<?php

declare(strict_types=1);

namespace App\Exception\WithStatusCode;

use Symfony\Component\HttpFoundation\Response;
use Throwable;

class UnhealthyInstanceException extends \Exception implements ExceptionWithStatusCodeInterface
{
    public function __construct(string $message = '', int $code = 0, ?Throwable $previous = null)
    {
        parent::__construct(
            $message === '' ? 'Server error' : $message,
            $code,
            $previous
        );
    }

    public function getStatusCode(): int
    {
        return Response::HTTP_INTERNAL_SERVER_ERROR;
    }
}
