<?php

declare(strict_types=1);

namespace App\Converter\Response;

use App\DTO\Request\Email;
use App\DTO\Response\EmailQueued;
use App\DTO\Response\QueuedEmailBatchResponse;

class EmailResponseConverter
{
    /**
     * @param Email[] $queued
     * @param array<int, array{reason:string, email:Email}> $skipped
     */
    public function toAsyncEmailResponse(array $queued, array $skipped): QueuedEmailBatchResponse
    {
        return new QueuedEmailBatchResponse(
            0, // Todo: https://demvsystems.atlassian.net/browse/MAILER-540
            count($queued),
            count($skipped),
            array_map(
                static fn($emailDto) => new EmailQueued(
                    $emailDto->uuid,
                    $emailDto->to,
                    $emailDto->subject,
                    $emailDto->traceId
                ),
                $queued
            ),
            array_map(
                static fn($skippedEmail) => [
                    'reason' => $skippedEmail['reason'],
                    'email' => new EmailQueued(
                        $skippedEmail['email']->uuid,
                        $skippedEmail['email']->to,
                        $skippedEmail['email']->subject,
                        $skippedEmail['email']->traceId
                    ),
                ],
                $skipped
            ),
        );
    }
}
