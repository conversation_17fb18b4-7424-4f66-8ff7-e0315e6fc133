<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO\Request\EmailSettings as EmailSettingsDTO;
use App\DTO\Request\OAuth2AuthConnection as OAuth2ConnectionDTO;
use App\DTO\Request\SmtpImapConnection as SmtpImapConnectionDTO;
use App\Entity\EmailSettings;
use App\Entity\OAuth2Connection;
use App\Entity\SmtpImapConnection;
use App\Enum\EmailTransportScheme;

class EmailSettingsRequestDTOConverter
{
    public function toEmailSettingsEntity(EmailSettingsDTO $emailSettingsDto, int $userId): EmailSettings
    {
        return match ($emailSettingsDto->scheme) {
            EmailTransportScheme::SMTP,
            EmailTransportScheme::SES_SMTP,
            EmailTransportScheme::SES_SMTPS =>
                $this->convertEmailSettingsDtoToSmtpConnectionEntity(
                    $emailSettingsDto,
                    $userId
                ),
            EmailTransportScheme::API => $this->convertEmailSettingsToOAuthConnectionEntity($emailSettingsDto, $userId),
            EmailTransportScheme::SMTPS => throw new \Exception('To be implemented'),
        };
    }

    private function convertEmailSettingsDtoToSmtpConnectionEntity(
        EmailSettingsDTO $emailSettingsDto,
        int $userId
    ): SmtpImapConnection {
        $connection = $emailSettingsDto->smtpImapConnection;

        if (!($connection instanceof SmtpImapConnectionDTO)) {
            throw new \Exception('Cannot convert Dto');
        }

        $emailSettings = new SmtpImapConnection(
            userId: $userId,
            email: trim($emailSettingsDto->email),
            smtpHost: trim($connection->smtpHost),
            smtpPort: $connection->smtpPort,
            username: trim($connection->username),
            password: trim($connection->password),
            imapHost: isset($connection->imapHost) ? trim($connection->imapHost) : null,
            imapPort: $connection->imapPort,
            senderName: isset($emailSettingsDto->senderName) ? trim($emailSettingsDto->senderName) : null,
            sendingStatus: $emailSettingsDto->sendingStatus,
            receivingStatus: $emailSettingsDto->receivingStatus,
        );
        $emailSettings->setForceUtf8($emailSettingsDto->forceUtf8);

        return $emailSettings;
    }

    private function convertEmailSettingsToOAuthConnectionEntity(
        EmailSettingsDTO $emailSettingsDto,
        int $userId
    ): OAuth2Connection {
        $connection = $emailSettingsDto->oAuthConnection;

        if (!($connection instanceof OAuth2ConnectionDTO)) {
            throw new \Exception('Cannot convert Dto');
        }

        $emailSettings = new OAuth2Connection(
            userId: $userId,
            email: $emailSettingsDto->email,
            provider: $connection->provider,
            access_token: $connection->access_token,
            refresh_token: $connection->refresh_token,
            expires_at: $connection->expires_at,
            data: $connection->data,
            provider_user_id: $connection->provider_user_id,
            provider_nickname: $connection->provider_nickname,
            provider_name: $connection->provider_name,
            provider_email: $connection->provider_email,
            provider_avatar: $connection->provider_avatar,
            senderName: $emailSettingsDto->senderName,
            sendingStatus: $emailSettingsDto->sendingStatus,
            receivingStatus: $emailSettingsDto->receivingStatus,
        );
        $emailSettings->setForceUtf8($emailSettingsDto->forceUtf8);

        return $emailSettings;
    }
}
