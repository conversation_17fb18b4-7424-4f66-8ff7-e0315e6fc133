<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO\Response;
use App\Entity\Address;
use App\Util\Result;
use Exception;
use Symfony\Component\Mime;

class AddressEntityConverter
{
    /**
     * @param Address[] $addresses The array of addresses that should be converted
     *
     * @return Result<Mime\Address[], string[]>
     */
    public static function toMimeAddresses(array $addresses, bool $forceUtf8): Result
    {
        $result = [];
        $errors = [];

        foreach ($addresses as $address) {
            $convertAddressResult = self::toMimeAddress($address, $forceUtf8);

            if ($convertAddressResult->isErr()) {
                $errors[] = $convertAddressResult->getError();
            } else {
                $result[] = $convertAddressResult->getValue();
            }
        }

        return $errors === [] ? Result::ok($result) : Result::err($errors);
    }

    /**
     * @param Address $address The address that should be converted
     *
     * @return Result<Mime\Address, string>
     */
    public static function toMimeAddress(Address $address, bool $forceUtf8): Result
    {
        try {
            return Result::ok(
                new Mime\Address(
                    $address->address,
                    $forceUtf8 ? EmailEntityConverter::rfc20247Utf8Encode($address->name) : $address->name
                )
            );
        } catch (Exception $e) {
            return Result::err($e->getMessage());
        }
    }

    public static function toResponseDto(Address $address): Response\Address
    {
        return new Response\Address($address->address, $address->name);
    }

    /**
     * @param Address[] $addresses
     *
     * @return Response\Address[]
     */
    public static function toResponseDtos(array $addresses): array
    {
        return array_map(self::toResponseDto(...), $addresses);
    }
}
