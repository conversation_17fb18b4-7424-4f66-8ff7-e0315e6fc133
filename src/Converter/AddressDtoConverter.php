<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO;
use App\Entity\Address;

class AddressDtoConverter
{
    /**
     * @param DTO\Request\Address[] $addresses The array of addresses that should be converted
     *
     * @return Address[]
     */
    public static function toAddressEntities(array $addresses): array
    {
        return array_map(self::toAddressEntity(...), $addresses);
    }

    /**
     * @param DTO\Request\Address $address The address that should be converted
     */
    public static function toAddressEntity(DTO\Request\Address $address): Address
    {
        return new Address($address->address, $address->name);
    }
}
