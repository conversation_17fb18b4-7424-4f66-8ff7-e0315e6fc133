<?php

declare(strict_types=1);

namespace App\Converter;

use App\Entity\Address as AddressEntity;
use Microsoft\Graph\Model\EmailAddress;
use Microsoft\Graph\Model\Recipient;
use Symfony\Component\Mime\Address as MimeAddress;

class AddressMimeConverter
{
    public static function toAddressEntity(MimeAddress $mimeAddress): AddressEntity
    {
        return new AddressEntity($mimeAddress->getAddress(), $mimeAddress->getName());
    }

    public static function toMicrosoftRecipient(MimeAddress $address): Recipient
    {
        $emailAddress = new EmailAddress();
        $emailAddress->setAddress($address->getAddress());
        $emailAddress->setName($address->getName());

        $recipient = new Recipient();
        $recipient->setEmailAddress($emailAddress);

        return $recipient;
    }

    /**
     * @param MimeAddress[] $addresses
     *
     * @return Recipient[]
     */
    public static function toMicrosoftRecipients(array $addresses): array
    {
        return array_map(static fn($recipient) => self::toMicrosoftRecipient($recipient), $addresses);
    }
}
