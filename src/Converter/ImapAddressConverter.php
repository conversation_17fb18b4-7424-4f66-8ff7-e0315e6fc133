<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO\Response\Address;

class ImapAddressConverter
{
    /**
     * @param array<string, mixed> $addresses
     *
     * @return Address[]
     */
    public static function toResponseDtos(array $addresses): array
    {
        $result = [];

        foreach ($addresses as $address) {
            if (!($address instanceof \Webklex\PHPIMAP\Address)) {
                throw new \InvalidArgumentException(
                    'Address is not an instance of \Webklex\PHPIMAP\Address: ' . var_export($address, true),
                );
            }

            $result[] = self::toResponseDto($address);
        }

        return $result;
    }

    public static function toResponseDto(\Webklex\PHPIMAP\Address $address): Address
    {
        return new Address($address->mail, $address->personal ?? '');
    }
}
