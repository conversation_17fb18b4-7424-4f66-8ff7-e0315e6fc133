<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO\Response\EmailReceived;
use App\DTO\Response\EmailReceivedSummary;
use Exception;
use Sentry\EventHint;
use Webklex\PHPIMAP\Message;

use function Sentry\captureException;

class IncomingEmailConverter
{
    public static function toEmailReceived(Message $incomingMail, string $uid): EmailReceived
    {
        $header = $incomingMail->getHeader();

        if ($header === null) {
            throw new Exception('Header for incoming Mail could not be retrieved.');
        }

        /** @var \Webklex\PHPIMAP\Address $from */
        $from = $header->get('from')[0];
        /** @var \Webklex\PHPIMAP\Address $sender */
        $sender = $header->get('sender')[0];

        return new EmailReceived(
            uid: $uid,
            subject: $incomingMail->getSubject()->toString(),
            date: $incomingMail->getDate()->toDate()->toDateString(),
            mailboxFolder: $incomingMail->getFolderPath(),
            mimeVersion: $header->get('mime_version')->toString(),
            contentType: $header->get('content_type')->toString(),
            fromHost: $from->host,
            fromName: $from->personal,
            fromAddress: $from->mail,
            senderHost: $sender->host,
            senderName: $sender->personal,
            senderAddress: $sender->mail,
            xOriginalTo: $header->get('x-original-to')->toString(),
            messageId: $incomingMail->getMessageId()->toString(),
            to: ImapAddressConverter::toResponseDtos($incomingMail->getTo()->toArray()),
            cc: ImapAddressConverter::toResponseDtos($incomingMail->getCc()->toArray()),
            bcc: ImapAddressConverter::toResponseDtos($incomingMail->getBcc()->toArray()),
        );
    }

    public static function toEmailReceivedSummary(Message $mailInfo, string $validity): ?EmailReceivedSummary
    {
        /** @var \Webklex\PHPIMAP\Address|null $fromAddress */
        $fromAddress = $mailInfo->getFrom()->first();

        if ($fromAddress === null) {
            return null;
        }

        try {
            return new EmailReceivedSummary(
                subject: $mailInfo->getSubject()->toString(),
                timestamp: (int) $mailInfo->getDate()->toDate()->timestamp,
                uid: $validity . '-' . $mailInfo->getUid(),
                from: ImapAddressConverter::toResponseDto($fromAddress),
                to: ImapAddressConverter::toResponseDtos($mailInfo->getTo()->toArray()),
                cc: ImapAddressConverter::toResponseDtos($mailInfo->getCc()->toArray()),
                bcc: ImapAddressConverter::toResponseDtos($mailInfo->getBcc()->toArray()),
                message_id: trim($mailInfo->getMessageId()->toString(), '<> '),
            );
        } catch (\Throwable $exception) {
            $eventHint = EventHint::fromArray(['extra' => ['mailInfo' => var_export($mailInfo, true)]]);
            captureException($exception, $eventHint);

            throw $exception;
        }
    }
}
