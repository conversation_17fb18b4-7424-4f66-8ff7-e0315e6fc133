<?php

declare(strict_types=1);

namespace App\Converter;

use DateTime;
use LogicException;
use Microsoft\Graph\Model\ItemBody;
use Microsoft\Graph\Model\Message;
use Symfony\Component\Mime\Email;

class EmailMimeConverter
{
    public static function toMicrosoftMessage(Email $symfonyMimeEmail): Message
    {
        $message = new Message();

        $message->setSubject($symfonyMimeEmail->getSubject() ?? '');
        $message->setFrom(AddressMimeConverter::toMicrosoftRecipient($symfonyMimeEmail->getFrom()[0]));
        $message->setToRecipients(AddressMimeConverter::toMicrosoftRecipients($symfonyMimeEmail->getTo()));
        $message->setCcRecipients(AddressMimeConverter::toMicrosoftRecipients($symfonyMimeEmail->getCc()));
        $message->setBccRecipients(AddressMimeConverter::toMicrosoftRecipients($symfonyMimeEmail->getBcc()));

        if ($symfonyMimeEmail->getTextBody() !== null) {
            $preparedBody = new ItemBody([
                'contentType' => 'text',
                'content' => $symfonyMimeEmail->getTextBody(),
            ]);
        }

        if ($symfonyMimeEmail->getHtmlBody() !== null) {
            $preparedBody = new ItemBody([
                'contentType' => 'html',
                'content' => $symfonyMimeEmail->getHtmlBody(),
            ]);
        }

        if (!isset($preparedBody)) {
            throw new \InvalidArgumentException('The body of the email was neither text nor html.');
        }

        $message->setBody($preparedBody);
        $message->setReplyTo(AddressMimeConverter::toMicrosoftRecipients($symfonyMimeEmail->getReplyTo()));
        $message->setAttachments(AttachmentMimeConverter::toMicrosoftAttachments($symfonyMimeEmail->getAttachments()));
        $message->setSentDateTime(
            DateTime::createFromImmutable($symfonyMimeEmail->getDate() ?? new \DateTimeImmutable('now'))
        );

        return $message;
    }

    public static function toStringWithBcc(Email $symfonyMimeEmail): string
    {
        // mostly copied from Symfony\Component\Mime\Message but left out the removing of the bcc header

        $headers = clone $symfonyMimeEmail->getHeaders();

        if (!$headers->has('From')) {
            if (!$headers->has('Sender')) {
                throw new LogicException('An email must have a "From" or a "Sender" header.');
            }

            /** @phpstan-ignore-next-line Copied from vendor package */
            $headers->addMailboxListHeader('From', [$headers->get('Sender')->getAddress()]);
        }

        if (!$headers->has('MIME-Version')) {
            $headers->addTextHeader('MIME-Version', '1.0');
        }

        if (!$headers->has('Date')) {
            $headers->addDateHeader('Date', new \DateTimeImmutable());
        }

        /** @phpstan-ignore-next-line Copied from vendor package */
        if (!$headers->has('Sender') && \count($froms = $headers->get('From')?->getAddresses()) > 1) {
            $headers->addMailboxHeader('Sender', $froms[0]);
        }

        if (!$headers->has('Message-ID')) {
            $headers->addIdHeader('Message-ID', $symfonyMimeEmail->generateMessageId());
        }

        $body = $symfonyMimeEmail->getBody();

        return $headers->toString() . $body->toString();
    }
}
