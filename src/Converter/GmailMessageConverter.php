<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO\Response\Address;
use App\DTO\Response\EmailReceived;
use App\DTO\Response\EmailReceivedSummary;
use DateTime;
use Google\Service\Gmail\Message;

class GmailMessageConverter
{
    public static function toEmailReceived(Message $message): EmailReceived
    {
        $headerMap = self::createHeaderMap($message);
        $from = self::addressesFromString($headerMap['from'] ?? null)[0] ?? null;
        $date = $headerMap['date'] ?? null;

        if ($date !== null) {
            $date = new DateTime($date);
        }

        return new EmailReceived(
            uid: $message->getId(),
            subject: $headerMap['subject'] ?? '',
            date: $date?->format('Y-m-d H:i:s'),
            mailboxFolder: null,
            mimeVersion: $headerMap['mime-version'] ?? '',
            contentType: $headerMap['content-type'] ?? '',
            fromHost: null,
            fromName: $from?->name,
            fromAddress: $from?->address,
            senderHost: null,
            senderName: null,
            senderAddress: null,
            xOriginalTo: null,
            messageId: trim($headerMap['message-id'] ?? '', '<> '),
            to: self::addressesFromString($headerMap['to'] ?? null),
            cc: self::addressesFromString($headerMap['cc'] ?? null),
            bcc: self::addressesFromString($headerMap['bcc'] ?? null),
        );
    }

    public static function toEmailReceivedSummary(Message $message): ?EmailReceivedSummary
    {
        $headerMap = self::createHeaderMap($message);
        $date = $headerMap['date'] ?? null;

        if ($date !== null) {
            $date = (new DateTime($date))->getTimestamp();
        }

        return new EmailReceivedSummary(
            subject: $headerMap['subject'] ?? '',
            timestamp: $date ?? 0,
            uid: $message->getId(),
            from: self::addressesFromString($headerMap['from'])[0] ?? null,
            to: self::addressesFromString($headerMap['to'] ?? null),
            cc: self::addressesFromString($headerMap['cc'] ?? null),
            bcc: self::addressesFromString($headerMap['bcc'] ?? null),
            message_id: trim($headerMap['message-id'] ?? '', '<>'),
        );
    }

    /**
     * @return Address[]
     */
    private static function addressesFromString(?string $addressesString): array
    {
        $result = [];

        if ($addressesString === null) {
            return $result;
        }

        $addresses = explode(',', $addressesString);

        foreach ($addresses as $address) {
            preg_match('/<(.*?)>/', $address, $matches);

            $email = count($matches) > 1 ? $matches[1] : $address;
            $name = trim(trim(str_replace($matches[0] ?? '', '', $address)), '"');

            $result[] = new Address($email, $name);
        }

        return $result;
    }

    /**
     * @return string[]
     */
    private static function createHeaderMap(Message $message): array
    {
        $map = [];

        foreach ($message->getPayload()->getHeaders() as $header) {
            $map[strtolower($header->getName())] = $header->getValue();
        }

        return $map;
    }
}
