<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO;
use App\Entity\Attachment;
use App\Entity\Email;
use App\Enum\AttachmentContentDisposition;
use App\Security\UserInterface;
use App\Service\EmailFileService;
use League\HTMLToMarkdown\HtmlConverterInterface;
use Psr\Log\LoggerInterface;

class EmailDtoConverter
{
    public function __construct(
        private readonly EmailFileService $emailFileService,
        private readonly HtmlConverterInterface $htmlToTextConverter,
        private readonly LoggerInterface $logger,
    ) {
    }

    public function toEmailEntity(DTO\Request\Email $emailDto, UserInterface $user): Email
    {
        $email = new Email(userId: $user->getUserId());
        $email->setUuid($emailDto->uuid);
        $email->setServiceId($user->getServiceId());

        $email->setSubject($emailDto->subject);
        $email->setUseFailsafeTransport($emailDto->useFailsafeTransport);

        if ($emailDto->from !== null && $user->mayUseCustomFromAddress()) {
            $email->setFromAddress(AddressDtoConverter::toAddressEntity($emailDto->from));
        }

        if ($emailDto->replyTo !== null) {
            $email->setReplyToAddress(AddressDtoConverter::toAddressEntity($emailDto->replyTo));
        }

        $email->setToAddresses(AddressDtoConverter::toAddressEntities($emailDto->to));
        $email->setCcAddresses(AddressDtoConverter::toAddressEntities($emailDto->cc));
        $email->setBccAddresses(AddressDtoConverter::toAddressEntities($emailDto->bcc));

        foreach ($emailDto->attachments as $attachmentDto) {
            $contentId = $attachmentDto->contentId;

            // The Content-Id should be globally unique, and follow the same syntax as message-ids.
            //   If the given contentId is missing the "@" character, we add it together with the mailer suffix.
            //   We also have to change the contentId in the email text and html content.
            if ($attachmentDto->contentDisposition === AttachmentContentDisposition::INLINE && $contentId !== null) {
                if (!str_contains($contentId, '@')) {
                    $newContentId = $contentId . '@mailer.demv.systems';
                    $emailDto->text = $emailDto->text !== null
                        ? str_replace($contentId, $newContentId, $emailDto->text) : null;
                    $emailDto->html = $emailDto->html !== null
                        ? str_replace($contentId, $newContentId, $emailDto->html) : null;
                    $contentId = $newContentId;
                }
            }

            $attachment = new Attachment(
                name: $attachmentDto->name,
                contentType: $attachmentDto->contentType ?? '',
                contentDisposition: $attachmentDto->contentDisposition ?? AttachmentContentDisposition::ATTACHMENT,
                contendId: $contentId,
            );

            $email->addAttachment($attachment);
            $attachmentPath = EmailFileService::createPathForEmailContent('attachment', $user->getUserIdentifier());
            $attachment->setBodyPath($attachmentPath);
            // AttachmentDTO->body is already base64 decoded, see AttachmentNormalizer
            $this->emailFileService->setContent($attachmentPath, $attachmentDto->body);

            try {
                $attachment->setSize($this->emailFileService->getFileSize($attachmentPath));
            } catch (\Throwable $throwable) {
                // We don't want to fail the whole email creation because of a failed file size retrieval
                $this->logger->warning('Could not get file size for attachment', [
                    'attachment' => json_encode($attachmentDto),
                    'message' => $throwable->getMessage(),
                ]);
            }
        }

        if ($emailDto->text !== null) {
            $textPath = EmailFileService::createPathForEmailContent('text', $user->getUserIdentifier());
            $email->setTextPath($textPath);
            $this->emailFileService->setContent($textPath, $emailDto->text);
        } elseif ($emailDto->html !== null) {
            $textPath = EmailFileService::createPathForEmailContent('text', $user->getUserIdentifier());
            $email->setTextPath($textPath);
            $this->emailFileService->setContent(
                $textPath,
                $this->htmlToTextConverter->convert($emailDto->html)
            );
        }

        if ($emailDto->html !== null) {
            $htmlPath = EmailFileService::createPathForEmailContent('html', $user->getUserIdentifier());
            $email->setHtmlPath($htmlPath);

            $this->emailFileService->setContent($htmlPath, $emailDto->html);
        }

        if ($emailDto->statusCallbackUrl !== null) {
            $email->setStatusCallbackUrl($emailDto->statusCallbackUrl);
        }

        return $email;
    }
}
