<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO\Response\Attachment as AttachmentResponseDto;
use App\Entity\Attachment;

class AttachmentEntityConverter
{
    public static function toResponseDto(Attachment $attachment): ?AttachmentResponseDto
    {
        return $attachment->getId() !== null ? new AttachmentResponseDto(
            id: $attachment->getId(),
            name: $attachment->getName(),
            contentType: $attachment->getContentType(),
            size: $attachment->getSize(),
            contentDisposition: $attachment->getContentDisposition()->value,
            contentId: $attachment->getContentId(),
        ) : null;
    }
}
