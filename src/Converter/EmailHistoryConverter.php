<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO\Response\Error;
use App\Entity\EmailHistory;
use App\Entity\EmailHistoryError;
use App\Enum\EmailHistoryType;

class EmailHistoryConverter
{
    public static function toErrorResponseDto(EmailHistory $emailHistory): ?Error
    {
        if (
            $emailHistory->getEmailHistoryType() !== EmailHistoryType::ERROR
            || !($emailHistory instanceof EmailHistoryError)
        ) {
            return null;
        }

        return new Error(
            $emailHistory->getKind(),
            $emailHistory->getType(),
            $emailHistory->getCause(),
            $emailHistory->getCreatedAt()?->format('d.m.Y H:i:s'),
        );
    }
}
