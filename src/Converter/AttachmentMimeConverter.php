<?php

declare(strict_types=1);

namespace App\Converter;

use App\Enum\AttachmentContentDisposition;
use GuzzleHttp\Psr7\Stream;
use GuzzleHttp\Psr7\Utils;
use Microsoft\Graph\Model\FileAttachment;
use Symfony\Component\Mime\Part\DataPart;

class AttachmentMimeConverter
{
    public static function toMicrosoftAttachment(DataPart $mimeAttachment): FileAttachment
    {
        $stream = Utils::streamFor(base64_encode($mimeAttachment->getBody()));

        if (!$stream instanceof Stream) {
            throw new \InvalidArgumentException('Stream for attachment must be of type ' . Stream::class);
        }

        $attachment = new FileAttachment();
        $attachment->setContentBytes($stream);
        $attachment->setContentType($mimeAttachment->getContentType());
        $attachment->setName($mimeAttachment->getName() ?? '');

        if ($mimeAttachment->getDisposition() === AttachmentContentDisposition::INLINE->value) {
            $attachment->setIsInline(true);
            $attachment->setContentId($mimeAttachment->getContentId());
        }

        $attachment->setODataType('#microsoft.graph.fileAttachment');

        return $attachment;
    }

    /**
     * @param DataPart[] $mimeAttachments
     *
     * @return FileAttachment[]
     */
    public static function toMicrosoftAttachments(array $mimeAttachments): array
    {
        return array_map(static fn ($attachment) => self::toMicrosoftAttachment($attachment), $mimeAttachments);
    }
}
