<?php

declare(strict_types=1);

namespace App\Converter;

use App\DTO\Response\Address;
use App\DTO\Response\EmailReceived;
use App\DTO\Response\EmailReceivedSummary;
use Microsoft\Graph\Model\Message;

class MicrosoftMessageConverter
{
    public static function toEmailReceived(Message $message): EmailReceived
    {
        return new EmailReceived(
            uid: $message->getId(),
            subject: $message->getSubject() ?? '',
            date: $message->getReceivedDateTime()?->format('Y-m-d H:i:s'),
            mailboxFolder: $message->getParentFolderId(),
            mimeVersion: null,
            contentType: $message->getBody()?->getContentType()?->value(),
            fromHost: null,
            fromName: $message->getFrom()?->getEmailAddress()?->getName(),
            fromAddress: $message->getFrom()?->getEmailAddress()?->getAddress(),
            senderHost: null,
            senderName: $message->getSender()?->getEmailAddress()?->getName(),
            senderAddress: $message->getSender()?->getEmailAddress()?->getAddress(),
            xOriginalTo: null,
            messageId: trim($message->getInternetMessageId() ?? '', '<> '),
            to: self::addressesFromArray($message->getToRecipients()),
            cc: self::addressesFromArray($message->getCcRecipients()),
            bcc: self::addressesFromArray($message->getBccRecipients()),
        );
    }

    public static function toEmailReceivedSummary(Message $message): ?EmailReceivedSummary
    {
        if ($message->getId() === null) {
            return null;
        }

        return new EmailReceivedSummary(
            subject: $message->getSubject() ?? '',
            timestamp: $message->getReceivedDateTime()?->getTimestamp() ?? 0,
            uid: $message->getId(),
            from: new Address(
                $message->getFrom()?->getEmailAddress()?->getAddress() ?? '',
                $message->getFrom()?->getEmailAddress()?->getName() ?? ''
            ),
            to: self::addressesFromArray($message->getToRecipients()),
            cc: self::addressesFromArray($message->getCcRecipients()),
            bcc: self::addressesFromArray($message->getBccRecipients()),
            message_id: trim($message->getInternetMessageId() ?? '', '<> '),
        );
    }

    /**
     * @param array<mixed[]>|null $addresses
     *
     * @return Address[]
     */
    private static function addressesFromArray(?array $addresses): array
    {
        $result = [];

        foreach ($addresses ?? [] as $address) {
            if (!is_array($address['emailAddress'] ?? null) && !isset($address['emailAddress']['address'])) {
                continue;
            }

            $result[] = new Address(
                $address['emailAddress']['address'],
                $address['emailAddress']['name'] ?? ''
            );
        }

        return $result;
    }
}
