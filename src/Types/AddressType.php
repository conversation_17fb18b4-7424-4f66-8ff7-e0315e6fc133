<?php

declare(strict_types=1);

namespace App\Types;

use App\Entity\Address;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\Type;

class AddressType extends Type
{
    private const ADDRESS = 'address';

    public function getSQLDeclaration(array $column, AbstractPlatform $platform): string
    {
        return $platform->getJsonTypeDeclarationSQL($column);
    }

    public function getName(): string
    {
        return self::ADDRESS;
    }

    public function convertToPHPValue($value, AbstractPlatform $platform): ?Address
    {
        if (!is_string($value)) {
            return null;
        }

        $decoded = json_decode($value, false);

        if (!is_object($decoded) || !isset($decoded->address)) {
            throw new \Exception('Invalid value in database');
        }

        return new Address($decoded->address, $decoded->name ?? '');
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value === null) {
            return null;
        }

        if (!($value instanceof Address)) {
            throw new \Exception('Invalid Value');
        }

        $json = json_encode($value);

        return $json !== false ? $json : null;
    }
}
