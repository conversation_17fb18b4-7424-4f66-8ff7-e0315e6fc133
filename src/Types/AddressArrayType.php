<?php

declare(strict_types=1);

namespace App\Types;

use App\Entity\Address;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\Type;

class AddressArrayType extends Type
{
    private const ADDRESS_ARRAY = 'address_array';

    public function getSQLDeclaration(array $column, AbstractPlatform $platform): string
    {
        return $platform->getJsonTypeDeclarationSQL($column);
    }

    public function getName(): string
    {
        return self::ADDRESS_ARRAY;
    }

    /**
     * @return Address[]
     */
    public function convertToPHPValue($value, AbstractPlatform $platform): array
    {
        if (!is_string($value)) {
            return [];
        }

        $decoded = json_decode($value, false);

        if (!is_array($decoded)) {
            throw new \Exception('Invalid value in database');
        }

        return array_map(
            static function ($addressStdObject) {
                if (!is_object($addressStdObject) || !isset($addressStdObject->address)) {
                    throw new \Exception('Invalid value in database');
                }

                return new Address($addressStdObject->address, $addressStdObject->name ?? '');
            },
            $decoded
        );
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value === null) {
            return null;
        }

        if (!is_array($value)) {
            throw new \Exception('Invalid Value');
        }

        foreach ($value as $address) {
            if (!($address instanceof Address)) {
                throw new \Exception('Invalid Value');
            }
        }

        $json = json_encode($value);

        return $json !== false ? $json : null;
    }
}
