<?php

declare(strict_types=1);

namespace App\Types;

use App\Entity\Path;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\Type;

class PathType extends Type
{
    private const TYPE = 'path';

    public function getSQLDeclaration(array $column, AbstractPlatform $platform): string
    {
        return $platform->getStringTypeDeclarationSQL($column);
    }

    public function getName(): string
    {
        return self::TYPE;
    }

    public function convertToPHPValue($value, AbstractPlatform $platform): ?Path
    {
        if (!is_string($value)) {
            return null;
        }

        return new Path($value);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value === null) {
            return null;
        }

        if (!($value instanceof Path)) {
            throw new \Exception('Invalid Value');
        }

        return $value->getPath();
    }
}
