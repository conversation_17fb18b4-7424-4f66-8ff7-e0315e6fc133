<?php

declare(strict_types=1);

namespace App\Serializer;

use App\DTO\Request\Attachment;
use <PERSON><PERSON><PERSON>ny\Component\Serializer\Exception\NotNormalizableValueException;
use S<PERSON>fony\Component\Serializer\Normalizer\DenormalizerInterface;
use S<PERSON>fony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\TypeInfo\Type;

class AttachmentDenormalizer implements DenormalizerInterface
{
    public function __construct(private ObjectNormalizer $normalizer)
    {
    }

    public function denormalize(mixed $data, string $type, string $format = null, array $context = []): Attachment
    {
        /** @var Attachment $attachmentDto */
        $attachmentDto = $this->normalizer->denormalize($data, $type, $format, $context);

        $decodedAttachmentBody = base64_decode($attachmentDto->body, true);

        if ($decodedAttachmentBody === false) {
            throw NotNormalizableValueException::createForUnexpectedDataType(
                sprintf(
                    "%s could not be denormalized, as it contains characters outside the base64 charset",
                    $attachmentDto->name
                ),
                $data,
                [Type::string()],
                is_string($context['deserialization_path']) ? $context['deserialization_path'] : null,
                true
            );
        }

        $attachmentDto->body = $decodedAttachmentBody;

        return $attachmentDto;
    }

    public function supportsDenormalization(
        mixed $data,
        string $type,
        ?string $format = null,
        array $context = []
    ): bool {
        return $type === Attachment::class;
    }

    /**
     * @return array<string, true>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            Attachment::class => true,
        ];
    }
}
