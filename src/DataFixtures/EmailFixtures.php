<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Address;
use App\Entity\Email;
use App\Enum\EmailStatus;
use App\Service\EmailFileService;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use League\Flysystem\FilesystemOperator;

class EmailFixtures extends Fixture
{
    public function __construct(private readonly FilesystemOperator $defaultStorage)
    {
    }

    public function load(ObjectManager $manager): void
    {
        $statusesArray = EmailStatus::cases();

        // generate emails for different pw users
        for ($u = 1; $u <= 3; $u++) {
            for ($i = 1; $i <= 9; $i++) {
                $toAddress = new Address("to{$i}<EMAIL>", "Test Address");
                $fromAddress = new Address("from{$u}<EMAIL>", "Test Address User {$u}");
                $email = new Email($u, "TestUserEmailSubject{$i}", $fromAddress);
                $email->setToAddresses([$toAddress]);
                $textPath = EmailFileService::createPathForEmailContent('text', "userTestId");
                $email->setTextPath($textPath);
                $text = <<<'EOT'
                Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut
                labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et
                ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum
                dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore
                magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
                clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.
                EOT;

                $this->defaultStorage->write($textPath->getPath(), $text);
                $current = current($statusesArray);
                $email->setStatus($current);

                if (next($statusesArray) === false) {
                    reset($statusesArray);
                }

                $email->setStatusCallbackUrl('url://' . $u . $i . 'd');

                // has to be done before setCreatedAt as this field is automatically set at prePersist
                $manager->persist($email);

                $email->setCreatedAt($this->createDateTime($u, $i));
                $email->setSentAt($this->createDateTime($u, $i));
            }
        }

        // generate emails for system user
        for ($i = 1; $i <= 9; $i++) {
            $address = new Address("<EMAIL>", "Test Address");
            $email = new Email(null, "TestSystemEmailSubject", $address);
            $email->setToAddresses([$address]);
            $textPath = EmailFileService::createPathForEmailContent('text', "userTestId");
            $email->setTextPath($textPath);
            $text = <<<'EOT'
                Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut
                labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et
                ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum
                dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore
                magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
                clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.
                EOT;

            $this->defaultStorage->write($textPath->getPath(), $text);
            $currentState = current($statusesArray);
            $email->setStatus($currentState);

            if (next($statusesArray) === false) {
                reset($statusesArray);
            }

            $email->setStatusCallbackUrl('url://u' . $i . 'd');

            // has to be done before setCreatedAt as this field is automatically set at prePersist
            $manager->persist($email);

            $email->setCreatedAt($this->createDateTime(9, $i));
            $email->setSentAt($this->createDateTime(9, $i));
        }

        $manager->flush();
    }

    private function createDateTime(int $month, int $day): \DateTimeImmutable
    {
        if ($month > 9 || $month < 1) {
            throw new \Exception('Month needs to be a number between 1 and 9');
        }

        if ($day > 9 || $day < 1) {
            throw new \Exception('Day needs to be a number between 1 and 9');
        }

        $dateTime = \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', "2023-0{$month}-{$day} 09:30:00");

        if (!$dateTime instanceof \DateTimeImmutable) {
            throw new \Exception('Creation of DateTimeImmutable failed');
        }

        return $dateTime;
    }
}
