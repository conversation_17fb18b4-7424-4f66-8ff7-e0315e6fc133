<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\SmtpImapConnection;
use App\Enum\ConnectionStatus;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class EmailSettingsFixtures extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        $smtpImapSettings = new SmtpImapConnection(
            userId: 1,
            email: '<EMAIL>',
            smtpHost: 'demvHost',
            smtpPort: 123456,
            username: 'TestUser',
            password: 'Testpassword',
            sendingStatus: ConnectionStatus::ACTIVE,
            receivingStatus: ConnectionStatus::ACTIVE,
        );
        $this->setReference('user1_smtp_imap_connection', $smtpImapSettings);
        $manager->persist($smtpImapSettings);

        $manager->flush();
    }
}
