<?php

declare(strict_types=1);

namespace App\DTO\Request;

use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'user_and_password_auth', required: [
    'provider', 'access_token', 'refresh_token', 'expires_at', 'provider_user_id', 'provider_email',
])]
class UserPasswordAuth
{
    public function __construct(
        #[OA\Property] public readonly string $username,
        #[OA\Property] public readonly string $password,
    ) {
    }
}
