<?php

declare(strict_types=1);

namespace App\DTO\Request;

use App\Enum\HashAlgorithm;
use App\Enum\SnsMessageType;
use App\Service\SignedMessageInterface;
use <PERSON>Api\Attributes\Property;
use <PERSON>Api\Attributes\Schema;
use Symfony\Component\Validator\Constraints as Assert;

#[Schema(schema: 'sns_message')]
class SnsMessage implements SignedMessageInterface
{
    public function __construct(
        #[Property]
        #[Assert\Choice(choices: [SnsMessageType::NOTIFICATION, SnsMessageType::SUBSCRIPTION_CONFIRMATION])]
        public readonly SnsMessageType $Type,
        #[Property] public readonly string $MessageId,
        #[Property] public readonly ?string $Token,
        #[Property] public readonly string $TopicArn,
        #[Property] public readonly ?string $Subject,
        #[Property] public readonly string $Message,
        #[Property] public readonly ?string $SubscribeURL,
        #[Property] public readonly string $Timestamp,
        #[Property] public readonly string $SignatureVersion,
        #[Property] public readonly string $Signature,
        #[Property] public readonly string $SigningCertURL,
        #[Property] public readonly ?string $UnsubscribeURL,
    ) {
    }

    /**
     * This is not the MessageId of the bounced email!
     * To find that have a look at $this->Message->mail->messageId
     */
    public function getIdentifier(): string
    {
        return $this->MessageId;
    }

    public function getSignFormat(): string
    {
        $typeString = $this->Type->value;

        if ($this->Type === SnsMessageType::SUBSCRIPTION_CONFIRMATION) {
            return <<<EOT
                Message
                $this->Message
                MessageId
                $this->MessageId
                SubscribeURL
                $this->SubscribeURL
                Timestamp
                $this->Timestamp
                Token
                $this->Token
                TopicArn
                $this->TopicArn
                Type
                $typeString

                EOT;
        }

        // SnsMessageType::NOTIFICATION

        if ($this->Subject === null) {
            return <<<EOT
                    Message
                    $this->Message
                    MessageId
                    $this->MessageId
                    Timestamp
                    $this->Timestamp
                    TopicArn
                    $this->TopicArn
                    Type
                    $typeString

                    EOT;
        }

        return <<<EOT
                Message
                $this->Message
                MessageId
                $this->MessageId
                Subject
                $this->Subject
                Timestamp
                $this->Timestamp
                TopicArn
                $this->TopicArn
                Type
                $typeString

                EOT;
    }

    public function getCertificateURL(): string
    {
        return $this->SigningCertURL;
    }

    public function getSignature(): string
    {
        return $this->Signature;
    }

    public function getHashAlgorithm(): HashAlgorithm
    {
        return $this->SignatureVersion === '1' ? HashAlgorithm::SHA1 : HashAlgorithm::SHA256;
    }
}
