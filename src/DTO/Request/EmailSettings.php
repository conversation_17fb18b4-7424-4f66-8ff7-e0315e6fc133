<?php

declare(strict_types=1);

namespace App\DTO\Request;

use App\Enum\ConnectionStatus;
use App\Enum\EmailTransportScheme;
use OpenApi\Attributes as OA;
use Symfony\Component\Validator\Constraints as Assert;

#[OA\Schema(schema: 'email_settings_request', required: ['email', 'scheme'])]
class EmailSettings
{
    public function __construct(
        #[OA\Property]
        #[Assert\NotBlank]
        #[Assert\Email(message: 'The email {{ value }} is not a valid email.')]
        public readonly string $email,
        #[OA\Property] public readonly EmailTransportScheme $scheme,
        #[OA\Property] public readonly ?string $senderName,
        #[OA\Property]
        #[Assert\Choice(choices: [ConnectionStatus::ACTIVE, ConnectionStatus::INACTIVE])]
        public readonly ?ConnectionStatus $sendingStatus,
        #[OA\Property]
        #[Assert\Choice(choices: [ConnectionStatus::ACTIVE, ConnectionStatus::INACTIVE])]
        public readonly ?ConnectionStatus $receivingStatus,
        #[OA\Property] public readonly ?SmtpImapConnection $smtpImapConnection,
        #[OA\Property] public readonly ?OAuth2AuthConnection $oAuthConnection,
        #[OA\Property] public readonly bool $forceUtf8 = false,
    ) {
    }
}
