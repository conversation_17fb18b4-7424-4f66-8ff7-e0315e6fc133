<?php

declare(strict_types=1);

namespace App\DTO\Request;

use DateTimeImmutable;
use OpenApi\Attributes as OA;
use Symfony\Component\Validator\Constraints as Assert;

#[OA\Schema(schema: 'oauth2_connection', required: [
    'provider', 'access_token', 'refresh_token', 'expires_at', 'provider_user_id', 'provider_email',
])]
class OAuth2AuthConnection
{
    public function __construct(
        #[OA\Property] #[Assert\NotBlank()] public readonly string $provider,
        #[OA\Property] #[Assert\NotBlank()] public readonly string $access_token,
        #[OA\Property] #[Assert\NotBlank()] public readonly string $refresh_token,
        #[OA\Property] public readonly DateTimeImmutable $expires_at,
        #[OA\Property] public readonly ?string $data,
        #[OA\Property] public readonly ?string $provider_user_id,
        #[OA\Property] public readonly ?string $provider_nickname,
        #[OA\Property] public readonly ?string $provider_name,
        #[OA\Property]
        #[Assert\NotBlank()]
        #[Assert\Email(message: 'The email {{ value }} is not a valid email.')]
        public readonly string $provider_email,
        #[OA\Property] public readonly ?string $provider_avatar,
    ) {
    }
}
