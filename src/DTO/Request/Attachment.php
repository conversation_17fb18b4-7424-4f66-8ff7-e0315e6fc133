<?php

declare(strict_types=1);

namespace App\DTO\Request;

use App\Enum\AttachmentContentDisposition;
use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'attachment')]
class Attachment
{
    public function __construct(
        #[OA\Property] public string $name,
        #[OA\Property(description: 'Must be base64 encoded')] public string $body, // is decoded during deserialization
        #[OA\Property] public ?string $contentType = null,
        #[OA\Property(default: 'attachment', enum: AttachmentContentDisposition::class)]
        public ?AttachmentContentDisposition $contentDisposition = AttachmentContentDisposition::ATTACHMENT,
        #[OA\Property] public ?string $contentId = null,
    ) {
    }
}
