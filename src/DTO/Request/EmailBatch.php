<?php

declare(strict_types=1);

namespace App\DTO\Request;

use App\Security\UserStatus;
use OpenApi\Attributes as OA;
use Symfony\Component\Validator\Constraints as Assert;

#[OA\Schema(schema: 'email_batch', required: ['emails'])]
class EmailBatch
{
    #[OA\Property(description: "If the user email transport fails, send the email via the system email transport")]
    public bool $useFailsafeTransport = true;

    /** @var array<UserStatus> $alsoSendIfUserStatusIsIn */
    #[OA\Property(
        description: "Emails are only sent if the User is \"active\". " .
        "By providing this array, the emails are also sent if the user status is in the array.",
        items: new OA\Items(enum: UserStatus::class)
    )]
    #[Assert\All(new Assert\Type(UserStatus::class))]
    public array $alsoSendIfUserStatusIsIn = [];

    /** @var Email[] */
    #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/email'))]
    public array $emails = [];
}
