<?php

declare(strict_types=1);

namespace App\DTO\Request;

use OpenApi\Attributes as OA;
use <PERSON>ymfony\Component\Uid\Uuid;

#[OA\Schema(schema: 'email', required: ['subject'])]
class Email
{
    #[OA\Property(description: <<<EOF
        The 'from' property in the Email represents the sender of the email.
        This property can only be set for application-generated emails.
        For user-generated emails, the sender is always the pre-configured email address.
        If a 'from' address is provided for user emails,
        the system will disregard it and use the pre-configured email address instead.
        EOF
    )]
    public ?Address $from = null;

    /** @var Address[] */
    #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/address'))]
    public array $to = [];

    /** @var Address[] */
    #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/address'))]
    public array $cc = [];

    /** @var Address[] */
    #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/address'))]
    public array $bcc = [];

    #[OA\Property]
    public ?Address $replyTo = null;

    #[OA\Property]
    public string $subject;

    #[OA\Property]
    public ?string $text = null;

    #[OA\Property]
    public ?string $html = null;

    /** @var Attachment[] */
    #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/attachment'))]
    public array $attachments = [];

    #[OA\Property(description: <<<EOF
        The statusCallbackUrl property is a URL that will receive information about the email
        once the status of the email changes. The callbackUrl will be sent a POST request with the same Email object as
        it is returned by the Email Send API. Internal statuses are not published to the statusCallbackUrl.
        Internal statuses are: 'created', 'sending', 'initialized'.
        Also the queued is only published if an email gets into the status after it has already been processed before.
        EOF
    )]
    public ?string $statusCallbackUrl = null;

    public Uuid $uuid;

    #[OA\Property(
        description: "If the user email transport fails, send the email via the system email transport",
        default: true
    )]
    public bool $useFailsafeTransport = true;

    public ?string $traceId = null;

    public function __construct(string $subject)
    {
        $this->subject = $subject;
        $this->uuid = Uuid::v4();
    }
}
