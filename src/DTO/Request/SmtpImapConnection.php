<?php

declare(strict_types=1);

namespace App\DTO\Request;

use OpenApi\Attributes as OA;
use Symfony\Component\Validator\Constraints as Assert;

#[OA\Schema(schema: 'email_config', required: ['smtpHost', 'smtpPort', 'username', 'password'])]
class SmtpImapConnection
{
    public function __construct(
        #[OA\Property] #[Assert\NotBlank()] public readonly string $smtpHost,
        #[OA\Property] public readonly int $smtpPort,
        #[OA\Property] #[Assert\NotBlank()] public readonly string $username,
        #[OA\Property] #[Assert\NotBlank()] public readonly string $password,
        #[OA\Property] public readonly ?string $imapHost,
        #[OA\Property] public readonly ?int $imapPort,
    ) {
    }
}
