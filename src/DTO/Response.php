<?php

declare(strict_types=1);

namespace App\DTO;

use App\Enum\ResponseStatus;
use JsonSerializable;
use OpenApi\Attributes as OA;

/**
 * @template-covariant T
 * @template-covariant E
 */
#[OA\Schema(schema: 'response')]
class Response implements JsonSerializable
{
    /** @var T */
    #[OA\Property]
    private mixed $response;

    /** @var E */
    #[OA\Property]
    private mixed $error;

    private function __construct(
        #[OA\Property(type: 'enum', enum: [ResponseStatus::Success, ResponseStatus::Error])]
        public readonly ResponseStatus $status,
        mixed $value
    ) {
        match ($status) {
            ResponseStatus::Success => $this->response = $value,
            ResponseStatus::Error => $this->error = $value,
        };
    }

    /**
     * @template V
     *
     * @param V $value
     *
     * @return self<V, never>
     */
    public static function success(mixed $value = null): self
    {
        return new self(ResponseStatus::Success, $value);
    }

    /**
     * @template V
     *
     * @param V $value
     *
     * @return self<never, V>
     */
    public static function error(mixed $value = null): self
    {
        return new self(ResponseStatus::Error, $value);
    }

    /**
     * @return T
     */
    public function getResponse(): mixed
    {
        return $this->response;
    }

    /**
     * @return E
     */
    public function getError(): mixed
    {
        return $this->error;
    }

    /**
     * @return array<string, ResponseStatus|T|E>
     */
    public function jsonSerialize(): array
    {
        return match ($this->status) {
            ResponseStatus::Error => ['status' => $this->status, 'error' => $this->error],
            ResponseStatus::Success => ['status' => $this->status, 'response' => $this->response]
        };
    }
}
