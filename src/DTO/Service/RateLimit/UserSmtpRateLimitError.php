<?php

declare(strict_types=1);

namespace App\DTO\Service\RateLimit;

/**
 * This rate limit checks for rate limit errors that were returned from providers
 * For strato for example we check the error message if it contains a rate limit notice,
 * then we set a key in redis under this user, so we know that this user can't send emails for a while
 */
readonly class UserSmtpRateLimitError extends AbstractRateLimit
{
    public function __construct()
    {
    }
}
