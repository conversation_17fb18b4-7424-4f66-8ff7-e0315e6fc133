<?php

declare(strict_types=1);

namespace App\DTO\Service\RateLimit;

/**
 * @description Um eine Sperre unserer IP bei den Providern zu umgehen,
 *   können wir eine Obergrenze an fehlgeschlagenen Verbindungsversuchen pro Server definieren.
 *   Beispiel: Bei Strato führen zu viele Verbindungsversuche mit falschen Credentials
 *   innerhalb von 24h zu einer Sperre unsere IP-Adresse.
 *   Ein Nutzer kann damit den Mailversand von allen derzeit ~350 weiteren Strato Kunden lahmlegen.
 */
readonly class MaxSmtpConnectionErrorsPerDay extends AbstractRateLimit
{
    public function __construct(public int $limit, public string $dsn)
    {
    }
}
