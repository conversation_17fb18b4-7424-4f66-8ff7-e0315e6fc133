<?php

declare(strict_types=1);

namespace App\DTO\Response;

use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'send_email_batch_response')]
readonly class EmailSentBatch implements ResponseDtoInterface
{
    /**
     * @param EmailSent[] $emails
     */
    public function __construct(
        #[OA\Property(example: 2)] public int $sentEmailCount,
        #[OA\Property(example: 1)] public int $queuedEmailCount,
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/email_sent'))]
        public array $emails,
    ) {
    }
}
