<?php

declare(strict_types=1);

namespace App\DTO\Response;

use App\Enum\ConnectionStatus;
use App\Enum\EmailTransportScheme;
use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'email_settings_response', required: ['userId', 'email', 'scheme'])]
readonly class EmailSettings
{
    public function __construct(
        #[OA\Property] public int $userId,
        #[OA\Property] public string $email,
        #[OA\Property] public EmailTransportScheme $scheme,
        #[OA\Property] public ?string $senderName,
        #[OA\Property] public ?ConnectionStatus $sendingStatus = null,
        #[OA\Property] public ?ConnectionStatus $receivingStatus = null,
        #[OA\Property] public ?OAuth2AuthConnection $oAuth2AuthConnection = null,
        #[OA\Property] public ?SmtpImapConnection $smtpImapConnection = null,
        #[OA\Property] public bool $forceUtf8 = false,
    ) {
    }
}
