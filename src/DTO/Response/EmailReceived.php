<?php

declare(strict_types=1);

namespace App\DTO\Response;

use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'email_received')]
readonly class EmailReceived
{
    /**
     * @param array<Address> $to
     * @param array<Address> $cc
     * @param array<Address> $bcc
     */
    public function __construct(
        #[OA\Property] public ?string $uid,
        #[OA\Property] public ?string $subject,
        #[OA\Property] public ?string $date,
        #[OA\Property] public ?string $mailboxFolder,
        #[OA\Property] public ?string $mimeVersion,
        #[OA\Property] public ?string $contentType,
        #[OA\Property] public ?string $fromHost,
        #[OA\Property] public ?string $fromName,
        #[OA\Property] public ?string $fromAddress,
        #[OA\Property] public ?string $senderHost,
        #[OA\Property] public ?string $senderName,
        #[OA\Property] public ?string $senderAddress,
        #[OA\Property] public ?string $xOriginalTo,
        #[OA\Property] public ?string $messageId,
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/address_response'))]
        public array $to = [],
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/address_response'))]
        public array $cc = [],
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/address_response'))]
        public array $bcc = [],
    ) {
    }
}
