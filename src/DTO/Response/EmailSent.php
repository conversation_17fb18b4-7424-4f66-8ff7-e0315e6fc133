<?php

declare(strict_types=1);

namespace App\DTO\Response;

use App\Enum\EmailStatus;
use DateTimeImmutable;
use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'email_sent')]
readonly class EmailSent implements ResponseDtoInterface
{
    /**
     * @param Address[] $toAddress
     * @param Address[] $ccAddress
     * @param Address[] $bccAddress
     * @param Attachment[] $attachments
     * @param Error[] $errors
     */
    public function __construct(
        #[OA\Property] public int $id,
        #[OA\Property] public string $uuid,
        #[OA\Property] public ?int $userId,
        #[OA\Property] public string $subject,
        #[OA\Property] public EmailStatus $status,
        #[OA\Property] public ?Address $fromAddress = null,
        #[OA\Property] public ?Address $actuallySentFrom = null,
        #[OA\Property] public ?Address $replyTo = null,
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/address_response'))]
        public array $toAddress = [],
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/address_response'))]
        public array $ccAddress = [],
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/address_response'))]
        public array $bccAddress = [],
        #[OA\Property] public ?string $bodyHtml = null,
        #[OA\Property] public ?string $bodyText = null,
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/attachment_response'))]
        public ?array $attachments = null,
        #[OA\Property] public ?DateTimeImmutable $sentAt = null,
        #[OA\Property] public ?DateTimeImmutable $createdAt = null,
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/error_response'))]
        public array $errors = [],
    ) {
    }
}
