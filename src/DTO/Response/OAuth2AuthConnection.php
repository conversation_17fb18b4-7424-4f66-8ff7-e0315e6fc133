<?php

declare(strict_types=1);

namespace App\DTO\Response;

use DateTimeImmutable;
use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'oauth2_settings', required: [
    'email',
    'provider',
    'access_token',
    'refresh_token',
    'expires_at',
    'provider_user_id',
    'provider_email',
])]
readonly class OAuth2AuthConnection
{
    public function __construct(
        #[OA\Property] public string $provider,
        #[OA\Property] public ?DateTimeImmutable $expires_at,
        #[OA\Property] public ?string $data,
        #[OA\Property] public ?string $provider_user_id,
        #[OA\Property] public ?string $provider_nickname,
        #[OA\Property] public ?string $provider_name,
        #[OA\Property] public ?string $provider_email,
        #[OA\Property] public ?string $provider_avatar,
    ) {
    }
}
