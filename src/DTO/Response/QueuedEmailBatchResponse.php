<?php

declare(strict_types=1);

namespace App\DTO\Response;

use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'queued_email_batch_response')]
readonly class QueuedEmailBatchResponse
{
    /**
     * @param EmailQueued[] $queuedEmails
     * @param array<int, array{reason:string, email:EmailQueued}> $skippedEmails
     */
    public function __construct(
        #[OA\Property(example: 2)] public int $sentEmailCount,
        #[OA\Property(example: 1)] public int $queuedEmailCount,
        #[OA\Property(example: 3)] public int $skippedEmailCount,
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/email_sent'))]
        public array $queuedEmails,
        #[OA\Property(properties: ([
            new OA\Property(property: 'reason', type: 'string'),
            new OA\Property(
                property: 'email',
                type: 'array',
                items: new OA\Items(ref: '#/components/schemas/email_sent')
            ),
        ]), type: 'object')]
        public array $skippedEmails,
    ) {
    }
}
