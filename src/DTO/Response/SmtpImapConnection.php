<?php

declare(strict_types=1);

namespace App\DTO\Response;

use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'smtp_imap_settings')]
readonly class SmtpImapConnection
{
    public function __construct(
        #[OA\Property] public string $smtpHost,
        #[OA\Property] public ?int $smtpPort,
        #[OA\Property] public string $username,
        #[OA\Property] public ?string $imapHost,
        #[OA\Property] public ?int $imapPort,
    ) {
    }
}
