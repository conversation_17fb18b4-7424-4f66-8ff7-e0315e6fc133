<?php

declare(strict_types=1);

namespace App\DTO\Response;

use OpenApi\Attributes as OA;
use Symfony\Component\Uid\Uuid;

#[OA\Schema(schema: 'email_queued')]
readonly class EmailQueued implements ResponseDtoInterface
{
    /**
     * @param \App\DTO\Request\Address[] $to
     * TODO: add SHA256 email hash (MAILER-600)
     */
    public function __construct(
        #[OA\Property] public Uuid $uuid,
        #[OA\Property(items: new OA\Items(ref: '#/components/schemas/address'))] public array $to,
        #[OA\Property] public string $subject,
        #[OA\Property] public ?string $traceId,
    ) {
    }
}
