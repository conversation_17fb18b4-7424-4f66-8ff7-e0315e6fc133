<?php

declare(strict_types=1);

namespace App\DTO\Response;

use App\Enum\AttachmentContentDisposition;
use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'attachment_response')]
readonly class Attachment
{
    public function __construct(
        #[OA\Property(example: 1)] public int $id,
        #[OA\Property(example: 'test.txt')] public string $name,
        #[OA\Property(example: 'text/plain')] public string $contentType,
        #[OA\Property(example: 1024)] public ?int $size,
        #[OA\Property(enum: AttachmentContentDisposition::class, example: 'inline')]
        public string $contentDisposition,
        #[OA\Property(example: 'asdjkn342jnkasasvß****************')] public ?string $contentId,
    ) {
    }
}
