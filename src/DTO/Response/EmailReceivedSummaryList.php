<?php

declare(strict_types=1);

namespace App\DTO\Response;

use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'inbox_email_list')]
readonly class EmailReceivedSummaryList
{
    /**
     * @param EmailReceivedSummary[] $emails
     */
    public function __construct(
        #[OA\Property(type: 'array', items: new OA\Items(ref: '#/components/schemas/inbox_email'))]
        public array $emails,
    ) {
    }
}
