<?php

declare(strict_types=1);

namespace App\Enum;

use OpenApi\Attributes as OA;

#[OA\Schema(schema: 'email_status_enum')]
enum EmailStatus: string
{
    case CREATED = 'created';
    case INITIALIZED = 'initialized';
    case QUEUED = 'queued';
    case SENDING = 'sending';
    case SENT = 'sent';
    case WONT_SEND = 'wont_send';
    /**@deprecated */
    case FAILED = 'failed';
    case SENDING_FAILED = 'sending_failed';
    case DELIVERY_FAILED_TEMPORARILY = 'delivery_failed_temporarily';
    case DELIVERY_FAILED_PERMANENTLY = 'delivery_failed_permanently';
    case DELIVERY_FAILED_UNDETERMINED = 'delivery_failed_undetermined';

    public const REGEX = '/^created|initialized|queued|sending|sent|wont_send|failed|delivery_failed_temporarily|'
        . 'delivery_failed_permanently|delivery_failed_undetermined$/';

    public function isIntendedToBeResent(): bool
    {
        return match ($this) {
            self::DELIVERY_FAILED_TEMPORARILY,
            self::DELIVERY_FAILED_UNDETERMINED,
            self::SENDING_FAILED => true,
            default => false
        };
    }

    public function hasToCallBackClient(): bool
    {
        return match ($this) {
            self::INITIALIZED,
            self::CREATED,
            self::SENDING => false,
            default => true
        };
    }
}
