<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\EmailHistory;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EmailHistory>
 * @method EmailHistory|null find($id, $lockMode = null, $lockVersion = null)
 * @method EmailHistory|null findOneBy(array $criteria, array $orderBy = null)
 * @method EmailHistory[] findAll()
 * @method EmailHistory[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EmailHistoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmailHistory::class);
    }

    public function add(EmailHistory $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(EmailHistory $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
