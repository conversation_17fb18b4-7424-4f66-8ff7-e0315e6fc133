<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\SmtpImapConnection;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SmtpImapConnection>
 * @method SmtpImapConnection|null find($id, $lockMode = null, $lockVersion = null)
 * @method SmtpImapConnection|null findOneBy(array $criteria, array $orderBy = null)
 * @method SmtpImapConnection[] findAll()
 * @method SmtpImapConnection[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SmtpImapConnectionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SmtpImapConnection::class);
    }

    public function add(SmtpImapConnection $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(SmtpImapConnection $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
