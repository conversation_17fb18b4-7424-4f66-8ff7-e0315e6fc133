<?php

declare(strict_types=1);

namespace App\Repository;

use App\Enum\Lock;

trait DatabaseLockTrait
{
    private function getLock(Lock $lock): bool
    {
        $result = $this->getEntityManager()
            ->getConnection()
            ->prepare("SELECT GET_LOCK('" . $lock->value . "',0)")
            ->executeQuery()
            ->fetchOne();

        return $result === 1;
    }

    private function isLockFree(Lock $lock): bool
    {
        $result = $this->getEntityManager()
            ->getConnection()
            ->prepare("SELECT IS_FREE_LOCK('" . $lock->value . "')")
            ->executeQuery()
            ->fetchOne();

        return $result === 1;
    }
}
