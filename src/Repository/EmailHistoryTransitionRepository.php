<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\EmailHistoryTransition;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EmailHistoryTransition>
 * @method EmailHistoryTransition|null find($id, $lockMode = null, $lockVersion = null)
 * @method EmailHistoryTransition|null findOneBy(array $criteria, array $orderBy = null)
 * @method EmailHistoryTransition[] findAll()
 * @method EmailHistoryTransition[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EmailHistoryTransitionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmailHistoryTransition::class);
    }

    public function add(EmailHistoryTransition $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(EmailHistoryTransition $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
