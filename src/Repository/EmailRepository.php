<?php

declare(strict_types=1);

namespace App\Repository;

use App\DTO\EmailCount;
use App\Entity\Email;
use App\Enum\EmailStatus;
use App\Enum\EmailType;
use App\Enum\Lock;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * @extends ServiceEntityRepository<Email>
 * @method Email|null find($id, $lockMode = null, $lockVersion = null)
 * @method Email|null findOneBy(array $criteria, array $orderBy = null)
 * @method Email[] findAll()
 * @method Email[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EmailRepository extends ServiceEntityRepository
{
    use DatabaseLockTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Email::class);
    }

    public function add(Email $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Email $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getByMessageId(string $messageId): ?Email
    {
        return $this->findOneBy(['messageId' => $messageId]);
    }

    public function getLockedEmailIdFromQueue(): ?int
    {
        $now = new \DateTimeImmutable();

        $id = $this->getEntityManager()
            ->getConnection()
            ->prepare(<<<EOSQL
                       select
                            id
                       from email
                       where
                            status = 'queued' and
                            (retry_at is null or retry_at <= "{$now->format('c')}")
                       order by user_id asc
                       limit 1
                       for update skip locked;
                    EOSQL)
            ->executeQuery()
            ->fetchOne();

        return is_numeric($id) ? (int) $id : null;
    }

    /**
     * It will be released automatically when the current connection is closed
     */
    public function getResendLock(): bool
    {
        return $this->getLock(Lock::RESEND);
    }

    public function countEmailsSentInTimespanByUser(int $userId, int $timespanInMinutes = 60): EmailCount
    {
        $countFromTime = new \DateTimeImmutable('-' . $timespanInMinutes . ' minutes');

        /** @var array{count: string, last_sent_at: string|null} $result */
        $result = $this->getEntityManager()
            ->getConnection()
            ->prepare(<<<EOSQL
                select
                    count(*) as count, MAX(sent_at) as last_sent_at
                from
                    email
                inner join
                    (select email from email_settings where user_id = :userId) es
                where
                    status = 'sent' and
                    sent_at >= "{$countFromTime->format('Y-m-d H:i')}" and
                    es.email = JSON_VALUE(email.actually_sent_from_address, '$.address');
            EOSQL)
            ->executeQuery(['userId' => $userId])
            ->fetchAssociative();

        $lastSentAt = $result['last_sent_at'] === null
            ? null
            : new \DateTimeImmutable($result['last_sent_at']);

        return new EmailCount((int) $result['count'], $lastSentAt);
    }

    public function isResendLockFree(): bool
    {
        return $this->isLockFree(Lock::RESEND);
    }

    /**
     * Returns all email entities with the given statuses, that are not older than 24hrs (from email.created_at) and
     * whose delay has already expired according to their retry level.
     *
     * The delay is calculated as (5^retriesSoFar)*10. RetriesSoFar is determined by the amount of PostedStates the
     * email has already been in, means how often it was tried to send.
     *
     * 0 retries yet => 10min delay
     * 1 retry yet => 50min delay
     * 2 retries yet => 250min delay
     * 3 retries yet => 1250min delay
     *
     * So each email is resend three times at most, because everything above exceeds the 24hr limit.
     *
     * The delay is measured from email.created_at as email.sent_at is updated every time the email is sent again.
     *
     * @param EmailStatus[] $statuses
     *
     * @return Email[]
     */
    public function getEmailsToBeResent(array $statuses): array
    {
        $statusesString = "'" . implode("','", array_column($statuses, 'value')) . "'";

        if ($this->isResendLockFree()) {
            throw new \Exception(
                'EmailRepository::getEmailsToBeResent is not concurrency safe. You need to get the resend lock first!'
            );
        }

        $now = new \DateTimeImmutable();

        $ids = $this->getEntityManager()
            ->getConnection()
            ->prepare(<<<EOSQL
                SELECT email.id
                FROM email, (
                    SELECT email.id as emailId, cast(10*power(5,count(email.id)-1) as int) as delayForRetryInMinutes
                    FROM email
                        JOIN email_history eh ON email.id = eh.email_id
                        JOIN email_history_transition eht ON eh.id = eht.id
                    WHERE email.status IN ({$statusesString})
                        AND email.created_at > "{$now->modify('-1 day')->format('Y-m-d H:i:s')}"
                        AND eht.to_state = 'Posted'
                    GROUP BY email.id
                ) retryEmail
                WHERE email.id = retryEmail.emailId
                  AND email.created_at + INTERVAL retryEmail.delayForRetryInMinutes MINUTE <=
                  "{$now->format('Y-m-d H:i:s')}";
                EOSQL)
            ->executeQuery()
            ->fetchAllNumeric();

        return $this->findBy(['id' => array_column($ids, 0)]);
    }

    /**
     * @return Paginator<Email>
     */
    public function getPaginatedForUser(int $userId, int $resultsPerPage = 50, int $page = 1): Paginator
    {
        $query = $this
            ->createQueryBuilder('e')
            ->where('e.userId = :userId')
            ->orderBy('e.id', 'DESC')
            ->setFirstResult(($page - 1) * $resultsPerPage)
            ->setMaxResults($resultsPerPage !== 0 ? $resultsPerPage : null)
            ->setParameter('userId', $userId);

        return new Paginator(query: $query);
    }

    /**
     * @return Paginator<Email>
     */
    public function getPaginatedForAllUsers(int $resultsPerPage = 50, int $page = 1): Paginator
    {
        $query = $this
            ->createQueryBuilder('e')
            ->orderBy('e.id', 'DESC')
            ->setFirstResult(($page - 1) * $resultsPerPage)
            ->setMaxResults($resultsPerPage !== 0 ? $resultsPerPage : null);

        return new Paginator(query: $query);
    }

    /**
     * @param string[]|null $statuses
     *
     * @return array<Email>
     *
     * @throws \Exception
     */
    public function getFilteredBy(
        int $limit = 30,
        ?int $startAtId = null,
        ?string $type = null,
        ?int $userId = null,
        ?string $toAddress = null,
        ?string $fromAddress = null,
        ?array $statuses = null,
        ?string $subject = null,
        ?string $fromCreatedAt = null,
        ?string $untilCreatedAt = null,
        ?string $fromSentAt = null,
        ?string $untilSentAt = null,
    ): array {
        $queryBuilder = $this
            ->createQueryBuilder('e')
            ->orderBy('e.id', 'DESC')
            ->setMaxResults($limit);

        if ($startAtId !== null) {
            $queryBuilder
                ->andWhere('e.id <= :startAtId')
                ->setParameter('startAtId', $startAtId);
        }

        if ($type === EmailType::SYSTEM->value && $userId !== null) {
            throw new NotFoundHttpException('It is not possible to query for system user emails and an userId');
        }

        if ($type !== null) {
            $type = EmailType::from($type);
            $condition = match ($type) {
                EmailType::SYSTEM => 'IS NULL',
                EmailType::USER => 'IS NOT NULL',
            };
            $queryBuilder
                ->andWhere("e.userId {$condition}");
        }

        if ($userId !== null) {
            $queryBuilder
                ->andWhere('e.userId = :userId')
                ->setParameter('userId', $userId);
        }

        if ($toAddress !== null) {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->like('e.toAddresses', ':toAddress'))
                ->setParameter('toAddress', "%{$toAddress}%");
        }

        if ($fromAddress !== null) {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->like('e.fromAddress', ':fromAddress'))
                ->setParameter('fromAddress', "%{$fromAddress}%");
        }

        if ($statuses !== null) {
            $emailStatuses = array_map(
                static fn(string $status): string => EmailStatus::from($status)->value,
                $statuses
            );

            $queryBuilder
                ->andWhere('e.status in (:status)')
                ->setParameter('status', $emailStatuses);
        }

        if ($subject !== null) {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->like('e.subject', ':subject'))
                ->setParameter('subject', "%{$subject}%");
        }

        if ($fromCreatedAt !== null) {
            $queryBuilder
                ->andWhere('e.created_at > :fromCreatedAt')
                ->setParameter('fromCreatedAt', $fromCreatedAt);
        }

        if ($untilCreatedAt !== null) {
            $queryBuilder
                ->andWhere('e.created_at < :untilCreatedAt')
                ->setParameter('untilCreatedAt', $untilCreatedAt);
        }

        if ($fromSentAt !== null) {
            $queryBuilder
                ->andWhere('e.sent_at > :fromSentAt')
                ->setParameter('fromSentAt', $fromSentAt);
        }

        if ($untilSentAt !== null) {
            $queryBuilder
                ->andWhere('e.sent_at < :untilSentAt')
                ->setParameter('untilSentAt', $untilSentAt);
        }

        $results = $queryBuilder->getQuery()->getResult();
        $this->checkType($results);

        return $results;
    }

    /**
     * @phpstan-assert Email[] $array
     */
    private function checkType(mixed $array): void
    {
        if (!is_array($array)) {
            throw new \Exception('The provided parameter is no array');
        }

        foreach ($array as $element) {
            if (!$element instanceof Email) {
                throw new \Exception('The provided array contains different elements then Email entities');
            }
        }
    }
}
