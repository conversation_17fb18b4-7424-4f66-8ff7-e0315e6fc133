<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\OAuth2Connection;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OAuth2Connection>
 * @method OAuth2Connection|null find($id, $lockMode = null, $lockVersion = null)
 * @method OAuth2Connection|null findOneBy(array $criteria, array $orderBy = null)
 * @method OAuth2Connection[] findAll()
 * @method OAuth2Connection[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OAuth2ConnectionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OAuth2Connection::class);
    }

    public function add(OAuth2Connection $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(OAuth2Connection $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
