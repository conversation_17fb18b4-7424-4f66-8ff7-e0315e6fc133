<?php

declare(strict_types=1);

namespace App\Error;

interface ErrorInterface extends \JsonSerializable
{
    public function getKind(): string;

    public function getTypeString(): string;

    public function getCause(): ?string;

    /**
     * Determines how the error is presented when it's logged to a file
     *
     * @return array{kind:string, type?:string, cause:string|null}
     */
    public function jsonSerialize(): array;
}
