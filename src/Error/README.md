## Fehler-Behandlung

-----

### Wo ist der Fehler aufgetreten?
Die ErrorKlassen stellen die Kontexte dar, in denen die Fehler aufgetreten sind.

### Was ist passiert?
Jede ErrorKlasse hat einen ErrorTyp als Eigenschaft, dessen verschiedene Varianten in einem Enum zusammengefasst sind. Er stellt eine erste Einordnung des Fehlers dar.

### Details
Jeder ErrorKlasse kann ein `?string $cause` mitgegeben werden.

### Besonderheiten
#### Logging
Um eine doppelte Verarbeitung von ErrorMessages zu vermeiden, werden sie nur innerhalb von ErrorKlassen an das Logging übergeben:
```php
try {
    // zum Beispiel beim Versuch zu senden
} catch (Exception $e) {
    $error = new TransportError(TransportErrorType::NO_TRANSPORT_AVAILABLE, $e->getMessage())
                                                                         ^^^^^^^^^^^^^^^^ hier
    $this->logger->error('', $error);
                         ^^ nicht hier
}
```
