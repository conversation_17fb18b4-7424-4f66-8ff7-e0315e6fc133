<?php

declare(strict_types=1);

namespace App\Error;

class GatewayError implements ErrorInterface
{
    public function __construct(private readonly GatewayErrorType $type, private readonly ?string $cause = null)
    {
    }

    public function getKind(): string
    {
        return 'GatewayError';
    }

    public function getTypeString(): string
    {
        return $this->type->value;
    }

    public function getCause(): ?string
    {
        return $this->cause;
    }

    public function jsonSerialize(): array
    {
        return [
            "kind" => $this->getKind(),
            "type" => $this->getTypeString(),
            "cause" => $this->getCause(),
        ];
    }
}
