<?php

declare(strict_types=1);

namespace App\Error;

readonly class BounceError implements ErrorInterface
{
    public function __construct(private BounceErrorType $type, private ?string $cause = null)
    {
    }

    public function getKind(): string
    {
        return 'BounceError';
    }

    public function getTypeString(): string
    {
        return $this->type->value;
    }

    public function getCause(): ?string
    {
        return $this->cause;
    }

    public function jsonSerialize(): array
    {
        return [
            "kind" => $this->getKind(),
            "type" => $this->getTypeString(),
            "cause" => $this->getCause(),
        ];
    }
}
