<?php

declare(strict_types=1);

namespace App\Error;

enum ValidationErrorType: string
{
    case UNKNOWN = 'unknown';

    case FROM_HEADER_INVALID = 'from_header_invalid';
    case TO_HEADER_INVALID = 'to_header_invalid';
    case REPLY_TO_HEADER_INVALID = 'reply_to_header_invalid';
    case CC_HEADER_INVALID = 'cc_header_invalid';
    case BCC_HEADER_INVALID = 'bcc_header_invalid';
    case RECIPIENTS_NOT_SET = 'recipients_not_set';
    case CONTENT_NOT_SET = 'content_not_set';
    case EMAIL_TOO_LARGE = 'email_too_large';
    case SIGNATURE_INVALID = 'signature_invalid';
    case PROPERTY_INVALID = 'property_invalid';
    case PROVIDER_CONFIG_CONSTRAINTS = 'provider_config_constraint';
}
