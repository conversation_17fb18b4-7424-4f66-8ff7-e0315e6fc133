<?php

declare(strict_types=1);

namespace App\Error;

enum TransportErrorType: string
{
    case UNKNOWN = 'unknown';

    case CLASSIFIED_AS_SPAM = 'classified_as_spam';
    case ATTACHMENT_TOO_LARGE = 'attachment_too_large';
    case NO_TRANSPORT_AVAILABLE = 'no_transport_available';
    case TIMEOUT_EXCEEDED = 'timeout_exceeded';
    case TIMEOUT_SES = 'timeout_ses';
    case RATE_LIMIT_HIT = 'rate_limit_hit';
}
