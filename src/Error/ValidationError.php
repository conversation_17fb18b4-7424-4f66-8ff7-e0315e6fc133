<?php

declare(strict_types=1);

namespace App\Error;

readonly class ValidationError implements ErrorInterface
{
    public function __construct(private ValidationErrorType $type, private ?string $cause = null)
    {
    }

    public function getKind(): string
    {
        return 'ValidationError';
    }

    public function getType(): ValidationErrorType
    {
        return $this->type;
    }

    public function getTypeString(): string
    {
        return $this->type->value;
    }

    public function getCause(): ?string
    {
        return $this->cause;
    }

    /**
     * @param string[] $causes
     * @param ValidationErrorType $type
     *
     * @return self[]
     */
    public static function createFromArray(array $causes, ValidationErrorType $type): array
    {
        $results = [];

        foreach ($causes as $cause) {
            $results[] = new self($type, $cause);
        }

        return $results;
    }

    public function jsonSerialize(): array
    {
        return [
            "kind" => $this->getKind(),
            "type" => $this->getTypeString(),
            "cause" => $this->getCause(),
        ];
    }
}
