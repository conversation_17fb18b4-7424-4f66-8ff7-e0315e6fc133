<?php

declare(strict_types=1);

namespace App\Error;

readonly class AuthorizationError implements ErrorInterface
{
    public function __construct(private AuthorizationErrorType $type, private ?string $cause = null)
    {
    }

    public function getKind(): string
    {
        return 'AuthorizationError';
    }

    public function getType(): AuthorizationErrorType
    {
        return $this->type;
    }

    public function getTypeString(): string
    {
        return $this->type->value;
    }

    public function getCause(): ?string
    {
        return $this->cause;
    }

    public function jsonSerialize(): array
    {
        return [
            "kind" => $this->getKind(),
            "type" => $this->getTypeString(),
            "cause" => $this->getCause(),
        ];
    }
}
