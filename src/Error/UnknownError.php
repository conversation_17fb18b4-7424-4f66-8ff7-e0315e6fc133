<?php

declare(strict_types=1);

namespace App\Error;

readonly class UnknownError implements ErrorInterface
{
    public function __construct(private ?string $cause = null)
    {
    }

    public function getKind(): string
    {
        return 'UnknownError';
    }

    public function getTypeString(): string
    {
        return '';
    }

    public function getCause(): ?string
    {
        return $this->cause;
    }

    public function jsonSerialize(): array
    {
        return [
            "kind" => $this->getKind(),
            "cause" => $this->getCause(),
        ];
    }
}
