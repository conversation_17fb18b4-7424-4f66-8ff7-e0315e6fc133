<?php

declare(strict_types=1);

namespace App\Error;

class ProcessError implements ErrorInterface
{
    public function __construct(private readonly ProcessErrorType $type, private readonly ?string $cause = null)
    {
    }

    public function getKind(): string
    {
        return 'ProcessError';
    }

    public function getTypeString(): string
    {
        return $this->type->value;
    }

    public function getCause(): ?string
    {
        return $this->cause;
    }

    public function jsonSerialize(): array
    {
        return [
            "kind" => $this->getKind(),
            "cause" => $this->getCause(),
        ];
    }
}
