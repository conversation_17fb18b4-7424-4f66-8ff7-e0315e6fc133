<?php

declare(strict_types=1);

namespace App\Error;

readonly class ConnectionError implements ErrorInterface
{
    public function __construct(private ConnectionErrorType $type, private ?string $cause = null)
    {
    }

    public function getKind(): string
    {
        return 'ConnectionError';
    }

    public function getTypeString(): string
    {
        return $this->type->value;
    }

    public function getCause(): ?string
    {
        return $this->cause;
    }

    public function jsonSerialize(): array
    {
        return [
            "kind" => $this->getKind(),
            "type" => $this->getTypeString(),
            "cause" => $this->getCause(),
        ];
    }
}
