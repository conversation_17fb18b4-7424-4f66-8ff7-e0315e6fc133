<?php

declare(strict_types=1);

namespace App\Controller;

use App\Converter\EmailEntityConverter;
use App\Converter\Response\EmailResponseConverter;
use App\DTO;
use App\DTO\Response\PaginatedDto;
use App\Entity\Email;
use App\Enum\EmailStatus;
use App\Enum\EmailType;
use App\Exception\MailerValidation\MailerValidationException;
use App\Exception\WithStatusCode\ForbiddenActionException;
use App\Logging\EmailUuidProcessor;
use App\Message\PersistEmailMessage;
use App\Message\SendEmailMessage;
use App\Repository\EmailRepository;
use App\Security\User;
use App\Security\UserInterface;
use App\Service\EmailPermissionService;
use App\Service\EmailService;
use App\Service\SendEmailService;
use App\Service\SuppressionListService;
use App\Util\EmailHistoryLogger;
use App\Validator\EmailValidator;
use Doctrine\ORM\EntityManagerInterface;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryParameter;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Uid\Uuid;
use Throwable;

use function Sentry\captureException;

use const FILTER_VALIDATE_EMAIL as EMAIL;
use const FILTER_VALIDATE_REGEXP as REGEX;

final class EmailController extends AbstractController
{
    private const YMDHIS_REGEX = '/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/';

    public function __construct(
        private readonly EmailRepository $emailRepository,
        private readonly MessageBusInterface $messageBus,
        private readonly EmailResponseConverter $emailResponseConverter,
        private readonly EmailEntityConverter $emailEntityConverter,
        private readonly EmailPermissionService $emailPermissionService,
        private readonly SuppressionListService $suppressionListService,
        private readonly EmailValidator $emailValidator,
    ) {
    }

    /**
     * @throws \App\Exception\WithStatusCode\ForbiddenActionException
     */
    #[OA\Post(
        path: '/v1/emails',
        description: 'Send e-mails as the logged in user. This uses the saved SMTP/OAuth-settings of the user.',
        summary: '/v1/emails',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: "#/components/schemas/email_batch")
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Emails request processed',
                content: new OA\JsonContent(ref: "#/components/schemas/queued_email_batch_response")
            ),
            new OA\Response(
                response: 400,
                description: 'Invalid input',
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
            new OA\Response(
                response: 413,
                description: <<<'EOF'
                            One or more emails in this request are too large to send.
                            Single emails are not allowed to be bigger than 20MB.
                            EOF,
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
            new OA\Response(
                response: 503,
                description: 'Error: Emails could not be sent',
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
        ]
    )]
    #[Route('/v1/emails', name: 'send_mail', methods: ['POST'], format: 'json')]
    public function sendEmailBatch(
        #[CurrentUser] UserInterface $user,
        #[MapRequestPayload] DTO\Request\EmailBatch $emailBatchDto
    ): JsonResponse {
        if (count($emailBatchDto->emails) === 0) {
            return new JsonResponse(
                DTO\Response::error('Keine Emails zum Versenden angegeben'),
                400
            );
        }

        if (!$this->emailPermissionService->hasPermission($user, $emailBatchDto->alsoSendIfUserStatusIsIn)) {
            throw new ForbiddenActionException('User not authorized to send emails');
        }

        $queued = [];
        /** @var array<int, array{reason:string, email:\App\DTO\Request\Email}> $skipped */
        $skipped = [];
        /** @var int $userId */
        $userId = $user->getUserId();

        foreach ($emailBatchDto->emails as $emailDto) {
            try {
                $this->emailValidator->validateEmail($emailDto);
            } catch (MailerValidationException $exception) {
                $emailArray = (array) $emailDto;
                unset($emailArray['uuid'], $emailArray['useFailsafeTransport']);
                $skipped[] = ['email' => $emailDto, 'reason' => $exception->getMessage()];
                captureException($exception);

                continue;
            }

            $emailDto->useFailsafeTransport = $emailBatchDto->useFailsafeTransport;

            $queued[] = $emailDto;
            $this->messageBus->dispatch(
                new PersistEmailMessage(
                    userId: $userId,
                    serviceId: $user->getServiceId(),
                    emailDto: $emailDto
                )
            );
        }

        return new JsonResponse($this->emailResponseConverter->toAsyncEmailResponse($queued, $skipped));
    }

    #[Route('/v1/emails/{idOrUuid}/resend', name: 'resend-email', methods: ['POST'], format: 'json')]
    public function resendEmail(
        string $idOrUuid,
        EmailHistoryLogger $logger,
        EntityManagerInterface $entityManager
    ): JsonResponse {
        $email = Uuid::isValid($idOrUuid) ?
            $this->emailRepository->findOneBy(['uuid' => Uuid::fromString($idOrUuid)]) :
            $this->emailRepository->find((int)$idOrUuid);

        if ($email === null) {
            return new JsonResponse(status: Response::HTTP_NOT_FOUND);
        }

        EmailUuidProcessor::setProcessedMailUuid($email->getUuid());

        $previousState = $email->getStatus();
        $email->setStatus(EmailStatus::QUEUED);
        $logger->transition($previousState->value, $email->getStatus()->value);
        $entityManager->flush();

        /** @var int $emailId */
        $emailId = $email->getId();

        $this->messageBus->dispatch(new SendEmailMessage($emailId));

        return new JsonResponse(DTO\Response::success([]));
    }

    /**
     * @throws \App\Exception\MailerValidation\EmptyEmailException
     * @throws \App\Exception\MailerValidation\InvalidRecipientException
     * @throws \App\Exception\MailerValidation\MaxSizeExceededException
     * @throws \App\Exception\MailerValidation\NoRecipientsException
     * @throws \App\Exception\WithStatusCode\ForbiddenActionException
     */
    #[OA\Post(
        path: '/v1/emails/synchronous',
        description: 'Send e-mail as the logged in user. This uses the saved SMTP/OAuth-settings of the user.',
        summary: '/v1/emails/synchronous',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: "#/components/schemas/send_email")
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: 'Email request processed',
                content: new OA\JsonContent(ref: "#/components/schemas/email_sent")
            ),
            new OA\Response(
                response: 400,
                description: 'Invalid input',
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
            new OA\Response(
                response: 413,
                description: 'Single emails are not allowed to be bigger than 20MB.',
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
            new OA\Response(
                response: 503,
                description: 'Error: Email could not be sent',
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
        ]
    )]
    #[Route('/v1/emails/synchronous', name: 'send_mail_synchronously', methods: ['POST'], format: 'json')]
    public function sendEmailSynchronously(
        EmailService $emailService,
        SendEmailService $sendEmailService,
        #[CurrentUser] UserInterface $user,
        #[MapRequestPayload] DTO\Request\SendEmailRequest $sendEmailRequestDto,
        EmailEntityConverter $emailEntityConverter
    ): JsonResponse {
        if (!$this->emailPermissionService->hasPermission($user, $sendEmailRequestDto->alsoSendIfUserStatusIsIn)) {
            throw new ForbiddenActionException('User not authorized to send emails');
        }

        $this->emailValidator->validateEmail($sendEmailRequestDto->email);

        // Backward compatibility: we wire useFailsafeTransport through the request container
        $emailDto = $sendEmailRequestDto->email;
        $emailDto->useFailsafeTransport = $sendEmailRequestDto->useFailsafeTransport;

        $emailEntity = $emailService->createEmailEntityFromDto($sendEmailRequestDto->email, $user);
        $sendEmailService->handle($emailEntity);

        return new JsonResponse($emailEntityConverter->toResponseDto($emailEntity));
    }

    /**
     * @param string[]|null $status
     */
    #[OA\Get(
        path: '/v1/emails',
        description: 'Show sent emails (paginated) – for users only their own, for admin system users all of them.',
        summary: '/v1/emails',
        parameters: [
            new OA\Parameter(
                name: 'userId',
                description: 'Filter the result by userId',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 1),
            ),
            new OA\Parameter(
                name: 'resultsPerPage',
                description: 'Specify how many results are included on one page',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 30),
            ),
            new OA\Parameter(
                name: 'startAtId',
                description: 'Specify where the new page starts (included)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'integer', example: 6),
            ),
            new OA\Parameter(
                name: 'type',
                description: 'Filter by pw or system users',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'system'),
            ),
            new OA\Parameter(
                name: 'toAddress',
                description: 'Filter by recipients',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '<EMAIL>'),
            ),
            new OA\Parameter(
                name: 'fromAddress',
                description: 'Filter by sender',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '<EMAIL>'),
            ),
            new OA\Parameter(
                name: 'status',
                description: 'Filter by status',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '<EMAIL>'),
            ),
            new OA\Parameter(
                name: 'subject',
                description: 'Filter subject by a string included',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'Topic XY'),
            ),
            new OA\Parameter(
                name: 'fromCreatedAt',
                description: 'Filter by createdAt as preceding limit (excluded)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '2023-02-01 09:30:00'),
            ),
            new OA\Parameter(
                name: 'untilCreatedAt',
                description: 'Filter by createdAt as following limit (excluded)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '2023-02-01 09:30:00'),
            ),
            new OA\Parameter(
                name: 'fromSentAt',
                description: 'Filter by sentAt as preceding limit (excluded)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '2023-02-01 09:30:00'),
            ),
            new OA\Parameter(
                name: 'untilSentAt',
                description: 'Filter by sentAt as following limit (excluded)',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '2023-02-01 09:30:00'),
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Mails',
                content: new OA\JsonContent(properties: [
                    new OA\Property(property: 'status', type: 'string', enum: ['success', 'error']),
                    new OA\Property(property: 'Response', properties: [
                        new OA\Property(property: 'total', type: 'int'),
                        new OA\Property(property: 'pagecount', type: 'int'),
                        new OA\Property(property: 'currentpage', type: 'int'),
                        new OA\Property(
                            property: 'resources',
                            type: 'array',
                            items: new OA\Items(ref: '#/components/schemas/email_sent')
                        ),
                    ], type: 'object'),
                ])
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
            new OA\Response(
                response: 403,
                description: "Fetching emails is only allowed for pw users or admin system users",
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
        ]
    )]
    #[Route('/v1/emails', name: 'get_emails', methods: ['GET'], format: 'json')]
    public function emails(
        #[CurrentUser] User $currentUser,
        Request $request,
        #[MapQueryParameter] ?int $userId = null,
        #[MapQueryParameter] int $resultsPerPage = 30,
        #[MapQueryParameter] ?int $startAtId = null,
        #[MapQueryParameter(filter: REGEX, options: ['regexp' => EmailType::REGEX])] ?string $type = null,
        #[MapQueryParameter(filter: EMAIL)] ?string $toAddress = null,
        #[MapQueryParameter(filter: EMAIL)] ?string $fromAddress = null,
        #[MapQueryParameter(filter: REGEX, options: ['regexp' => EmailStatus::REGEX])] ?array $status = null,
        #[MapQueryParameter] ?string $subject = null,
        #[MapQueryParameter(filter: REGEX, options: ['regexp' => self::YMDHIS_REGEX])] ?string $fromCreatedAt = null,
        #[MapQueryParameter(filter: REGEX, options: ['regexp' => self::YMDHIS_REGEX])] ?string $untilCreatedAt = null,
        #[MapQueryParameter(filter: REGEX, options: ['regexp' => self::YMDHIS_REGEX])] ?string $fromSentAt = null,
        #[MapQueryParameter(filter: REGEX, options: ['regexp' => self::YMDHIS_REGEX])] ?string $untilSentAt = null,
    ): JsonResponse {
        if (!$this->isGranted('ROLE_USER') && !($this->isGranted('ROLE_ADMIN') && $this->isGranted('ROLE_SYSTEM'))) {
            return new JsonResponse(
                Dto\Response::error("Fetching emails is only allowed for pw users or admin system users."),
                403
            );
        }

        // currently normal pw users are only allowed to see their own emails
        if ($currentUser->getUserId() !== null) {
            $userId = $currentUser->getUserId();
        }

        try {
            $emails = $this->emailRepository->getFilteredBy(
                limit: $resultsPerPage,
                startAtId: $startAtId,
                type: $type,
                userId: $userId,
                toAddress: $toAddress,
                fromAddress: $fromAddress,
                statuses: $status,
                subject: $subject,
                fromCreatedAt: $fromCreatedAt,
                untilCreatedAt: $untilCreatedAt,
                fromSentAt: $fromSentAt,
                untilSentAt: $untilSentAt,
            );

            // I would have put it in the loop, but codesniffer doesn't like it
            /** @var DTO\Response\EmailSent[] $emailResponseDtos */
            $emailResponseDtos = [];

            foreach ($emails as $email) {
                $emailResponseDtos[] = $this->emailEntityConverter->toResponseDto($email);
            }
        } catch (HttpException $e) {
            return new JsonResponse(DTO\Response::error($e->getMessage()), $e->getStatusCode());
        } catch (Throwable $e) {
            return new JsonResponse(DTO\Response::error($e->getMessage()), 500);
        }

        return new JsonResponse(
            DTO\Response::success(
                new PaginatedDto(
                    total: count($emailResponseDtos),
                    firstEmailId: reset($emailResponseDtos) !== false ? reset($emailResponseDtos)->id : null,
                    lastEmailId: end($emailResponseDtos) !== false ? end($emailResponseDtos)->id : null,
                    resources: $emailResponseDtos,
                )
            )
        );
    }

    #[OA\Get(
        path: '/v1/emails/{email_id}',
        description: 'Show email sent by the currently logged in user',
        summary: '/v1/emails/{email_id}',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Email found',
                content: new OA\JsonContent(properties: [
                    new OA\Property(property: 'status', type: 'string', enum: ['success', 'error']),
                    new OA\Property(property: 'Response', ref: '#/components/schemas/email_sent'),
                ])
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
            new OA\Response(
                response: 403,
                description: "Fetching emails is only allowed for pw users or admin system users",
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
        ]
    )]
    #[Route('/v1/emails/{email}', name: 'get_email', methods: ['GET'], format: 'json')]
    public function email(#[CurrentUser] User $currentUser, ?Email $email = null): JsonResponse
    {
        if (!$this->isGranted('ROLE_USER') && !$this->isGranted('ROLE_ADMIN')) {
            return new JsonResponse(
                Dto\Response::error("Fetching emails is only allowed for pw users or admin system users."),
                403
            );
        }

        if ($email === null || ($email->getUserId() !== $currentUser->getUserId() && !$this->isGranted('ROLE_ADMIN'))) {
            return new JsonResponse(DTO\Response::error('Not found'), 404);
        }

        return new JsonResponse(
            DTO\Response::success(
                $this->emailEntityConverter->toResponseDto($email, true)
            )
        );
    }

    #[OA\Get(
        path: '/v1/emails/status/{email_uuid}',
        description: 'Show email sent by the currently logged in user',
        summary: '/v1/emails/{email_id}',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Email found',
                content: new OA\JsonContent(properties: [
                    new OA\Property(property: 'status', type: 'string', enum: ['success', 'error']),
                    new OA\Property(property: 'Response', ref: '#/components/schemas/email_sent'),
                ])
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
            new OA\Response(
                response: 403,
                description: "Fetching emails is only allowed for pw users or admin system users",
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
        ]
    )]
    #[Route('/v1/emails/status/{uuid}', name: 'get_email_status', methods: ['GET'], format: 'json')]
    public function getEmailStatus(Uuid $uuid): JsonResponse
    {
        if (!$this->isGranted('ROLE_USER') && !$this->isGranted('ROLE_ADMIN')) {
            return new JsonResponse(
                Dto\Response::error("Fetching emails is only allowed for pw users or admin system users."),
                403
            );
        }

        $email = $this->emailRepository->findOneBy(['uuid' => $uuid]);
        $status = $email === null ? EmailStatus::QUEUED : $email->getStatus();

        return new JsonResponse(new DTO\Response\EmailStatusDto($uuid, $status));
    }

    #[OA\Delete(
        path: '/v1/emails/{id}/suppressionlist',
        description: 'Remove all associated email addresses of the specified email from the suppression list',
        summary: 'Remove email from suppression list',
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'The ID of the email entity',
                required: true,
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successfully removed email addresses from the suppression list',
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found - Email with provided ID does not exist',
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
        ],
    )]
    #[IsGranted("ROLE_ADMIN")]
    #[Route('/v1/emails/{id<\d+>}/suppressionlist', name: 'remove_email_suppression_list', methods: ['DELETE'])]
    public function removeFromSuppressionList(int $id): JsonResponse
    {
        $email = $this->emailRepository->find(['id' => $id]);

        if ($email === null) {
            return new JsonResponse(DTO\Response::error('Not Found - Email with provided ID does not exist'), 404);
        }

        foreach ($email->getToAddresses() as $toAddress) {
            $this->suppressionListService->removeByEmailAddress($toAddress->address);
        }

        return new JsonResponse(DTO\Response::success());
    }
}
