<?php

declare(strict_types=1);

namespace App\Controller;

use App\DTO;
use App\Mailbox\MailboxInterface;
use DateInterval;
use DateTimeImmutable;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryParameter;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;

use function Sentry\captureException;

class InboxController extends AbstractController
{
    private const NO_MAILBOX_CONNECTION = 'Es konnte keine Verbindung zum Email Postfach aufgebaut werden';

    #[OA\Get(
        path: '/v1/inbox',
        description: 'Show incoming emails of the currently logged in user.',
        summary: '/v1/inbox',
        parameters: [
            new OA\Parameter(
                name: 'searchSubject',
                description: 'Filter the result by a subject string or substring',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: 'MAG-'),
            ),
            new OA\Parameter(
                name: 'since',
                description: 'Filter the result to include only mails after the specified date',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'string', example: '2023-07-25'),
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'List of emails',
                content: new OA\JsonContent(properties: [
                    new OA\Property(property: 'status', type: 'string', enum: ['success', 'error']),
                    new OA\Property(property: 'Response', ref: '#/components/schemas/inbox_email_list'),
                ])
            ),
        ]
    )]
    #[Route('/v1/inbox', name: 'inbox', methods: ['GET'], format: 'json')]
    public function getLatestEmails(
        ?MailboxInterface $mailbox,
        #[MapQueryParameter] ?string $searchSubject,
        #[MapQueryParameter] ?string $since
    ): JsonResponse {
        if ($mailbox === null) {
            return new JsonResponse(DTO\Response::error(self::NO_MAILBOX_CONNECTION), 422);
        }

        try {
            $sinceDate = $since !== null
                ? new DateTimeImmutable($since)
                : (new DateTimeImmutable())->sub(new DateInterval('P1W'));

            $emails = $mailbox->searchEmailsSince(
                since: $sinceDate,
                searchSubject: $searchSubject,
            );
        } catch (HttpException $e) {
            return new JsonResponse(DTO\Response::error($e->getMessage()), $e->getStatusCode());
        } catch (Throwable $e) {
            captureException($e);

            return new JsonResponse(DTO\Response::error("Fetching mails via IMAP failed"), 500);
        }

        return new JsonResponse(
            DTO\Response::success(new DTO\Response\EmailReceivedSummaryList(emails: $emails)),
            200
        );
    }

    #[OA\Get(
        path: '/v1/inbox/{emailId}',
        description: 'Details of one email in Mailbox',
        summary: '/v1/inbox/{emailId}',
        parameters: [
            new OA\Parameter(
                name: 'emailId',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'string', example: '1690285127:5'),
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Email details',
                content: new OA\JsonContent(properties: [
                    new OA\Property(property: 'status', type: 'string', enum: ['success', 'error']),
                    new OA\Property(property: 'Response', ref: '#/components/schemas/email_received'),
                ])
            ),
        ]
    )]
    #[Route('/v1/inbox/{emailId}', name: 'inbox_mail', methods: ['GET'], format: 'json')]
    public function getEmail(?MailboxInterface $mailbox, string $emailId): JsonResponse
    {
        if ($mailbox === null) {
            return new JsonResponse(DTO\Response::error(self::NO_MAILBOX_CONNECTION), 422);
        }

        try {
            $incomingEmail = $mailbox->getMailById($emailId);
        } catch (HttpException $e) {
            return new JsonResponse(DTO\Response::error($e->getMessage()), $e->getStatusCode());
        } catch (Throwable $e) {
            return new JsonResponse(DTO\Response::error("Fetching mail via IMAP failed: " . $e->getMessage()), 500);
        }

        return new JsonResponse(DTO\Response::success($incomingEmail), 200);
    }

    #[OA\Get(
        path: '/v1/inbox/{emailId}/raw',
        description: 'Raw mime email',
        summary: '/v1/inbox/{emailId}/raw',
        parameters: [
            new OA\Parameter(
                name: 'emailId',
                in: 'path',
                required: true,
                schema: new OA\Schema(type: 'string', example: '1690285127:5'),
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Raw mime email',
                content: new OA\MediaType(
                    mediaType: 'text/plain',
                    schema: new OA\Schema(type: 'string'),
                    example: <<<END
                                From: Some One <<EMAIL>>
                                MIME-Version: 1.0
                                Content-Type: multipart/mixed;
                                        boundary="XXXXboundary text"

                                This is a multipart message in MIME format.

                                --XXXXboundary text
                                Content-Type: text/plain

                                this is the body text

                                --XXXXboundary text
                                Content-Type: text/plain;
                                Content-Disposition: attachment;
                                        filename="test.txt"

                                this is the attachment text

                                --XXXXboundary text--
                                END
                )
            ),
        ]
    )]
    #[Route('/v1/inbox/{emailId}/raw', name: 'inbox_mail_raw', methods: ['GET'], format: 'json')]
    public function getRawEmail(?MailboxInterface $mailbox, string $emailId): Response
    {
        if ($mailbox === null) {
            return new JsonResponse(DTO\Response::error(self::NO_MAILBOX_CONNECTION), 422);
        }

        try {
            $response = new Response($mailbox->getRawMailById($emailId));
            $response->headers->set('Content-Type', 'text/plain');

            return $response;
        } catch (HttpException $e) {
            return new JsonResponse(DTO\Response::error($e->getMessage()), $e->getStatusCode());
        } catch (Throwable $e) {
            return new JsonResponse(DTO\Response::error("Fetching raw mail via IMAP failed: " . $e->getMessage()), 500);
        }
    }
}
