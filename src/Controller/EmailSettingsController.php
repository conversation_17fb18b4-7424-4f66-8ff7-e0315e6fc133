<?php

declare(strict_types=1);

namespace App\Controller;

use App\Converter\EmailSettingsEntityConverter;
use App\Converter\EmailSettingsRequestDTOConverter;
use App\DTO;
use App\Entity\EmailSettings;
use App\Mailbox\MailboxService;
use App\Security\SystemUser;
use App\Security\User;
use App\Service\EmailTransportService;
use App\Util\EmailHistoryLogger;
use App\Util\ExceptionHelper;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;

class EmailSettingsController extends AbstractController
{
    public function __construct(
        private readonly EmailTransportService $emailTransportService,
        private readonly MailboxService $mailboxService,
        private readonly EmailSettingsRequestDTOConverter $emailSettingsDTOConverter,
        private readonly EntityManagerInterface $entityManager,
        private readonly EmailHistoryLogger $logger
    ) {
    }

    #[OA\Get(
        path: '/v1/users/email-settings',
        description: 'Get email settings for user (passwords/tokens are excluded)',
        summary: '/v1/users/email-settings',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Email settings',
                content: new OA\JsonContent(properties: [
                    new OA\Property(property: 'status', type: 'string', enum: ['success', 'error']),
                    new OA\Property(
                        property: 'response',
                        type: 'array',
                        items: new OA\Items(ref: '#/components/schemas/email_settings_response')
                    ),
                ]),
            ),
        ],
    )]
    #[Route(path: '/v1/users/email-settings', name: 'get_all_email_settings', methods: ['GET'], format: 'json')]
    public function allEmailSettings(#[CurrentUser] User $currentUser): JsonResponse
    {
        if (!($currentUser instanceof SystemUser) || !$currentUser->isAdmin) {
            return new JsonResponse(
                Dto\Response::error("Getting all user email settings is only allowed for system users"),
                400
            );
        }

        return new JsonResponse(DTO\Response::success(
            array_map(
                static fn(EmailSettings $emailSettings) =>
                    EmailSettingsEntityConverter::toEmailSettingsResponseDTO($emailSettings),
                $this->entityManager->getRepository(EmailSettings::class)->findAll()
            )
        ), 200);
    }

    #[OA\Get(
        path: '/v1/users/{user}/email-settings',
        description: 'Get email settings for user (passwords/tokens are excluded)',
        summary: '/v1/users/{user}/email-settings',
        parameters: [
            new OA\Parameter(
                name: 'User',
                in: 'path',
                required: true,
                examples: [
                    'me' => new OA\Examples(
                        example: 'me',
                        summary: '"me" is the user currently logged in via the access token',
                        value: 'me'
                    ),
                ]
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Email settings',
                content: new OA\JsonContent(properties: [
                    new OA\Property(property: 'status', type: 'string', enum: ['success', 'error']),
                    new OA\Property(property: 'response', ref: '#/components/schemas/email_settings_response'),
                ]),
            ),
        ],
    )]
    #[Route(path: '/v1/users/{user}/email-settings', name: 'get_email_settings', methods: ['GET'], format: 'json')]
    public function emailSettings(#[CurrentUser] User $currentUser, int|string $user): JsonResponse
    {
        if ($currentUser->getUserId() === null) {
            return new JsonResponse(
                Dto\Response::error("Currently it's only possible to get email settings for users with a userId."),
                500
            );
        }

        if (!is_numeric($user) && $user !== 'me') {
            return new JsonResponse(DTO\Response::error(['validation' => 'User needs to be "me" or an integer']), 400);
        }

        if ($user !== 'me') {
            // We might change this at some point:
            return new JsonResponse(
                DTO\Response::error(['validation' => 'Getting email settings for others users is not supported']),
                403
            );
        }

        $emailSettings = $this->entityManager->getRepository(EmailSettings::class)->find($currentUser->getUserId());

        if ($emailSettings === null) {
            return new JsonResponse(status: 404);
        }

        $emailSettingsResponseDto = EmailSettingsEntityConverter::toEmailSettingsResponseDTO($emailSettings);

        return new JsonResponse(DTO\Response::success($emailSettingsResponseDto), 200);
    }

    #[OA\Put(
        path: '/v1/users/{user}/email-settings',
        description: 'Save or update email settings for user',
        summary: '/v1/users/{user}/email-settings',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: "#/components/schemas/email_settings_request")
        ),
        parameters: [
            new OA\Parameter(
                name: 'User',
                in: 'path',
                required: true,
                examples: [
                    'me' => new OA\Examples(
                        example: 'me',
                        summary: '"me" is the user currently logged in via the access token',
                        value: 'me'
                    ),
                ],
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Email settings saved',
                content: new OA\JsonContent(properties: [
                    new OA\Property(property: 'status', type: 'string', enum: ['success', 'error']),
                    new OA\Property(property: 'response', ref: '#/components/schemas/email_settings_response'),
                ]),
            ),
            new OA\Response(
                response: 403,
                description: 'Invalid input - Updating email settings for others users is not supported',
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
            new OA\Response(
                response: 501,
                description: "Not implemented - It's only possible to set email settings for users with a userId",
                content: new OA\JsonContent(ref: "#/components/schemas/response")
            ),
        ]
    )]
    #[Route(path: '/v1/users/{user}/email-settings', name: 'save_email_settings', methods: ['PUT'], format: 'json')]
    public function saveEmailSettings(
        #[CurrentUser] User $currentUser,
        int|string $user,
        #[MapRequestPayload] DTO\Request\EmailSettings $emailSettingsDto
    ): JsonResponse {
        if ($currentUser->getUserId() === null) {
            return new JsonResponse(
                Dto\Response::error("Currently it's only possible to set email settings for users with a userId."),
                500
            );
        }

        if (!is_numeric($user) && $user !== 'me') {
            return new JsonResponse(DTO\Response::error(['validation' => 'User needs to be "me" or an integer']), 400);
        }

        if ($user !== 'me') {
            // We might change this at some point:
            return new JsonResponse(
                DTO\Response::error(['validation' => 'Updating email settings for others users is not supported']),
                403
            );
        }

        $oldEmailSettings = $this->entityManager->getRepository(EmailSettings::class)->find($currentUser->getUserId());

        try {
            $this->entityManager->getConnection()->setNestTransactionsWithSavepoints(true);
            $this->entityManager->getConnection()->beginTransaction();

            // Because the type of the entity might change, we always create a
            // new one and have to delete the old one first
            if ($oldEmailSettings !== null) {
                $this->entityManager->remove($oldEmailSettings);
                $this->entityManager->flush();
            }

            try {
                $emailSettings = $this->emailSettingsDTOConverter->toEmailSettingsEntity(
                    $emailSettingsDto,
                    $currentUser->getUserId()
                );

                $this->entityManager->persist($emailSettings);
                $this->entityManager->flush();

                // We need to get the transport and mailbox for the user to test the connection.
                //  This will also update the sending and receiving status of the email settings
                $this->emailTransportService->getCheckAndUpdateTransportForUser($currentUser);
                $this->mailboxService->getAndCheckMailboxForUser($currentUser, failOnError: true);

                $this->entityManager->flush();
            } catch (Exception $e) {
                $cause = 'An error occurred while preparing the email settings for persisting';
                $this->logger->unknownError(
                    $cause . ': ' . $e->getMessage(),
                    ExceptionHelper::getMessageChain($e)
                );

                return new JsonResponse(DTO\Response::error($cause), 500);
            }

            $this->entityManager->flush();

            $this->entityManager->getConnection()->commit();
        } catch (\Doctrine\DBAL\Exception $exception) {
            $cause = 'An error occurred while interacting with the database';
            $this->entityManager->getConnection()->rollBack();
            $this->logger->unknownError($cause, ExceptionHelper::getMessageChain($exception));

            return new JsonResponse(
                DTO\Response::error(
                    ['database' => $cause]
                ),
                500
            );
        }

        $newEmailSettings = $this->entityManager->getRepository(EmailSettings::class)->find($currentUser->getUserId());

        if ($newEmailSettings === null) {
            return new JsonResponse(
                DTO\Response::error(
                    ['database' => 'An error occurred while interacting with the database']
                ),
                500
            );
        }

        return new JsonResponse(
            DTO\Response::success(EmailSettingsEntityConverter::toEmailSettingsResponseDTO($newEmailSettings)),
            200
        );
    }

    #[OA\Delete(
        path: '/v1/users/{user}/email-settings',
        description: 'Delete email settings for user',
        summary: '/v1/users/{user}/email-settings',
        parameters: [
            new OA\Parameter(
                name: 'User',
                in: 'path',
                required: true,
                examples: [
                    'me' => new OA\Examples(
                        example: 'me',
                        summary: '"me" is the user currently logged in via the access token',
                        value: 'me'
                    ),
                ],
            ),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Email settings',
            ),
            new OA\Response(
                response: 400,
                description: 'User needs to be "me" or an integer',
            ),
            new OA\Response(
                response: 403,
                description: 'Deleting email settings for other users is not supported',
            ),
            new OA\Response(
                response: 404,
                description: 'No email settings found for the user',
            ),
        ],
    )]
    #[Route(path: '/v1/users/{user}/email-settings', name: 'delete_email_settings', methods: ['DELETE'])]
    public function deleteEmailSettings(#[CurrentUser] User $currentUser, int|string $user): JsonResponse
    {
        if (!is_numeric($user) && $user !== 'me') {
            return new JsonResponse(DTO\Response::error(['validation' => 'User needs to be "me" or an integer']), 400);
        }

        if ($user !== 'me') {
            return new JsonResponse(
                DTO\Response::error(['validation' => 'Deleting email settings for others users is not supported']),
                403
            );
        }

        $emailSettings = $this->entityManager->getRepository(EmailSettings::class)->find($currentUser->getUserId());

        if ($emailSettings === null) {
            return new JsonResponse(DTO\Response::error('No email settings found for the user'), 404);
        }

        try {
            $this->entityManager->remove($emailSettings);
            $this->entityManager->flush();
        } catch (Exception $e) {
            $cause = 'An error occurred while deleting the email settings';
            $this->logger->unknownError(
                $cause . ': ' . $e->getMessage(),
                ExceptionHelper::getMessageChain($e)
            );

            return new JsonResponse(DTO\Response::error($cause), 500);
        }

        return new JsonResponse();
    }
}
