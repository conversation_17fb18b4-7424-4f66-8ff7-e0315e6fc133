<?php

declare(strict_types=1);

namespace App\Security;

class SystemUser extends User
{
    /** @param array<string> $rights */
    private function __construct(
        /** @var non-empty-string $serviceId */
        public readonly string $serviceId,
        public readonly bool $isAdmin = false,
        array $rights = [User::RIGHT_SEND_EMAIL],
    ) {
        parent::__construct($rights);
    }

    /**
     * @param non-empty-string $serviceId
     * @param array<mixed> $payload
     */
    public static function create(string $serviceId, array $payload): self
    {
        return new self(
            $serviceId,
            (bool) $payload[self::KEY_ADMIN]
        );
    }

    public function getRoles(): array
    {
        $roles = [self::ROLE_SYSTEM];

        if ($this->isAdmin) {
            $roles[] = self::ROLE_ADMIN;
        }

        return $roles;
    }

    public function getUserId(): ?int
    {
        return null;
    }

    /**
     * @return non-empty-string
     */
    public function getServiceId(): string
    {
        return $this->serviceId;
    }

    /**
     * @return non-empty-string
     */
    public function getUserIdentifier(): string
    {
        return $this->getServiceId();
    }

    public function mayUseFailsafeTransport(): bool
    {
        return false;
    }

    public function mayUseCustomFromAddress(): bool
    {
        return true;
    }

    public function getStatus(): UserStatus
    {
        return UserStatus::ACTIVE;
    }
}
