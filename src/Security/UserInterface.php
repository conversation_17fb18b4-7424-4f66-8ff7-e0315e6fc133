<?php

declare(strict_types=1);

namespace App\Security;

use Symfony\Component\Security\Core\User\UserInterface as SystemUserInterface;

interface UserInterface extends SystemUserInterface
{
    /**
     * Returns the ProfessionalWorks user id of this user
     */
    public function getUserId(): ?int;

    /**
     * Returns the id of the service action for this user
     *
     * If it's accompanied by a userId, it is a service acting on behalf of a ProfessionalWorks user
     * If it's not, it's a service acting on its own
     */
    public function getServiceId(): string;

    /**
     * Returns a boolean if this user can use a specific from address or not
     */
    public function mayUseCustomFromAddress(): bool;

    /**
     * Returns a boolean if this user can use the failsafe logic or not
     */
    public function mayUseFailsafeTransport(): bool;

    /** @return array<string> */
    public function getRights(): array;

    /** @return UserStatus */
    public function getStatus(): UserStatus;
}
