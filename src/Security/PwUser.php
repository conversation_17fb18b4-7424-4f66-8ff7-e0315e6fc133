<?php

declare(strict_types=1);

namespace App\Security;

use BackedEnum;
use Lexik\Bundle\JWTAuthenticationBundle\Exception\InvalidTokenException;

class PwUser extends User
{
    protected const KEY_USER_ID = 'user_id';
    private const KEY_FIRSTNAME = 'firstname';
    private const KEY_LASTNAME = 'lastname';
    private const KEY_AGENCY_ID = 'agency_id';
    private const KEY_AGENCY_NAME = 'agency_name';
    private const KEY_RIGHTS = 'rights';
    private const KEY_STATUS = 'status';

    private const MANDATORY_PROPERTIES_WITH_TYPES = [
        self::KEY_USER_ID => 'integer',
        self::KEY_LASTNAME => 'string',
        self::KEY_AGENCY_ID => 'integer',
        self::KEY_AGENCY_NAME => 'string',
        self::KEY_RIGHTS => 'array<string>',
        self::KEY_STATUS => 'value-of<' . UserStatus::class . '>',
    ];

    /**
     * @param array<string> $rights
     */
    private function __construct(
        public readonly string $serviceId,
        public readonly int $userId,
        public readonly ?string $firstname,
        public readonly string $lastname,
        public readonly int $agencyId,
        public readonly string $agencyName,
        public readonly UserStatus $status,
        public readonly bool $isAdmin = false,
        array $rights = [],
    ) {
        parent::__construct($rights);
    }

    /**
     * @param array<mixed> $payload
     */
    public static function create(string $serviceId, array $payload): self
    {
        self::checkPayload($payload);

        return new self(
            serviceId: $serviceId,
            userId: $payload[self::KEY_USER_ID],
            firstname: $payload[self::KEY_FIRSTNAME] ?? null,
            lastname: $payload[self::KEY_LASTNAME],
            agencyId: $payload[self::KEY_AGENCY_ID],
            agencyName: $payload[self::KEY_AGENCY_NAME],
            status: UserStatus::from($payload[self::KEY_STATUS]),
            isAdmin: $payload[self::KEY_ADMIN] ?? false,
            rights: $payload[self::KEY_RIGHTS],
        );
    }

    public static function createForMailer(int $userId, string $serviceId = 'mailer'): self
    {
        return new self(
            serviceId: $serviceId,
            userId: $userId,
            firstname: '',
            lastname: '',
            agencyId: 0,
            agencyName: '',
            status: UserStatus::UNKNOWN,
            isAdmin: false,
        );
    }

    /**
     * @param array<mixed> $payload
     *
     * @phpstan-assert array{
     *     user_id: int,
     *     firstname?: string|null,
     *     lastname: string,
     *     agency_id: int,
     *     agency_name: string,
     *     admin?: bool,
     *     rights: array<string>,
     *     status: value-of<UserStatus>,
     *  } $payload
     */
    private static function checkPayload(array $payload): void
    {
        foreach (self::MANDATORY_PROPERTIES_WITH_TYPES as $property => $expectedType) {
            if (!array_key_exists($property, $payload)) {
                throw new InvalidTokenException("Cannot create User without {$property}.");
            }

            $actualType = gettype($payload[$property]);

            if (str_starts_with($expectedType, 'value-of')) {
                $valueOfType = rtrim(ltrim($expectedType, 'value-of<'), '>');

                if (
                    is_subclass_of($valueOfType, BackedEnum::class)
                    && (is_string($payload[$property])
                        || is_int($payload[$property]))
                ) {
                    if ($valueOfType::tryFrom($payload[$property]) !== null) {
                        continue;
                    }
                }
            }

            if (is_array($payload[$property])) {
                $actualType = 'array<' . implode('|', array_unique(array_map('gettype', $payload[$property]))) . '>';

                // Empty arrays are allowed if the expected type is any array and do comply any array value type
                if ($actualType === 'array<>' && str_starts_with($expectedType, 'array')) {
                    continue;
                }
            }

            if ($actualType !== $expectedType) {
                throw new InvalidTokenException(
                    "Property {$property} must be of type {$expectedType}, but is of type {$actualType}."
                );
            }
        }
    }

    public function getRoles(): array
    {
        $roles = [self::ROLE_USER];

        if ($this->isAdmin) {
            $roles[] = self::ROLE_ADMIN;
        }

        return $roles;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getServiceId(): string
    {
        return $this->serviceId;
    }

    public function getUserIdentifier(): string
    {
        return (string) $this->getUserId();
    }

    public function mayUseFailsafeTransport(): bool
    {
        return true;
    }

    public function mayUseCustomFromAddress(): bool
    {
        return false;
    }

    public function getStatus(): UserStatus
    {
        return $this->status;
    }
}
