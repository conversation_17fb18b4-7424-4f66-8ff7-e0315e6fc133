<?php

declare(strict_types=1);

namespace App\Security;

use App\Entity\Email;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * @phpstan-extends Voter<User::RIGHT_SEND_EMAIL, Email>
 */
class SendEmailVoter extends Voter
{
    protected function supports(string $attribute, mixed $subject): bool
    {
        return $attribute === User::RIGHT_SEND_EMAIL;
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        $user = $token->getUser();

        if (!($user instanceof UserInterface)) {
            // the user must be logged in; if not, deny access
            return false;
        }

        return in_array(User::RIGHT_SEND_EMAIL, $user->getRights(), strict: true);
    }
}
