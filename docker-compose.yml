services:
    nginx:
        build: ./.docker/nginx
        restart: unless-stopped
        depends_on:
            - php-fpm
        volumes:
            - ./public:/var/www/html/public:delegated
            - ./.docker/nginx/site.conf:/etc/nginx/conf.d/default.conf:ro
        labels:
            - "traefik.enable=true"
            - "traefik.http.routers.mailer-nginx.rule=Host(`mailer.demv.internal`)"
            - "traefik.http.routers.mailer-nginx.entrypoints=web,websecure"
            - "traefik.docker.network=local-docker-network_default"
        networks:
            - traefik
            - default
        ports:
            - "8999:80"

    php-fpm:
        container_name: mailer
        restart: unless-stopped
        build:
            context: ./.docker/php
            args:
                GITHUB_TOKEN: $GITHUB_TOKEN
                GROUP_ID: $GROUP_ID
                USER_ID: $USER_ID
        environment:
            PHP_IDE_CONFIG: 'serverName=mailer.demv.internal'
        image: demvsystems/mailer-php-fpm
        volumes:
            - .:/var/www/html:delegated
            - ./.docker/php/config/php.ini:/usr/local/etc/php/conf.d/php.ini
        depends_on:
            - mariadb_mailer
        extra_hosts:
            - "host.docker.internal:host-gateway"
        networks:
            - traefik
            - default

    mariadb_mailer:
        image: mariadb:10.10
        restart: unless-stopped
        environment:
            MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
            MYSQL_DATABASE: '${DB_DATABASE}'
            MYSQL_USER: '${DB_USERNAME}'
            MYSQL_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
        volumes:
            - mariadb:/var/lib/mysql
            - .docker/provision/mariadb/init:/docker-entrypoint-initdb.d
        ports:
            - "33330:3306"
        healthcheck:
            test: [ "CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "-p${DB_PASSWORD}" ]
            start_period: 10s
            start_interval: 1s
            timeout: 1s
        command: mysqld --sql-mode=TRADITIONAL,ONLY_FULL_GROUP_BY,NO_ENGINE_SUBSTITUTION

    redis:
        image: redis:7.4-alpine
        restart: unless-stopped
        ports:
            - "6400:6379"
        volumes:
            - redis:/data
            - .docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
        command: redis-server /usr/local/etc/redis/redis.conf
        environment:
            REDIS_PORT: 6379
            REDIS_DATABASES: 0
        networks:
            - default

    openapi:
        image: redocly/redoc
        environment:
            - SPEC_URL=openapi/openapi.yaml
            - PAGE_TITLE="OpenApi - Mailer"
        volumes:
            - ./documentation:/usr/share/nginx/html/openapi:delegated
        labels:
            - "traefik.enable=true"
            - "traefik.http.routers.mailer-openapi.rule=Host(`openapi.mailer.demv.internal`)"
            - "traefik.http.routers.mailer-openapi.entrypoints=web,websecure"
            - "traefik.docker.network=local-docker-network_default"
        networks:
            - traefik

    # Local s3 instance using minio
    # !!! name MUST NOT BE s3 because it would then interfere with the pw s3 minio container
    mailer_s3:
        image: minio/minio:RELEASE.2021-08-25T00-41-18Z
        restart: unless-stopped
        ports:
            - "9004:9000"
            - "9044:9044"
        volumes:
            - minio:/data
        environment:
            MINIO_ROOT_USER: $AWS_KEY
            MINIO_ROOT_PASSWORD: $AWS_SECRET
        command: server /data --console-address=":9044"
        labels:
            - "traefik.enable=true"
            - "traefik.http.routers.mailer-s3.rule=Host(`s3.mailer.demv.internal`)"
            - "traefik.http.routers.mailer-s3.entrypoints=web,websecure"
            - "traefik.docker.network=local-docker-network_default"
        networks:
            - default
            - traefik

    # Run once on startup to create the necessary buckets
    mailer_s3_install:
        image: minio/mc:RELEASE.2021-07-27T06-46-19Z
        depends_on:
            - mailer_s3
        entrypoint: >
            /bin/sh -c "
            until (/usr/bin/mc config host add myminio http://mailer_s3:9000 $AWS_KEY $AWS_SECRET) do echo '...waiting...' && sleep 1; done;
            /usr/bin/mc mb --ignore-existing --region=$AWS_DEFAULT_REGION myminio/demv-dev-mailer;
            exit 0;
            "

networks:
    traefik:
        external: true
        name: local-docker-network_default

volumes:
    mariadb:
    minio:
    redis:
