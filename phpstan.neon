includes:
  - vendor/phpstan/phpstan-deprecation-rules/rules.neon
  - vendor/phpstan/phpstan-doctrine/extension.neon
  - vendor/phpstan/phpstan-phpunit/extension.neon
  - vendor/phpstan/phpstan-phpunit/rules.neon
  - vendor/phpstan/phpstan-strict-rules/rules.neon
  - vendor/phpstan/phpstan-symfony/extension.neon

parameters:
  level: max
  tipsOfTheDay: false
  symfony:
    containerXmlPath: var/cache/dev/App_KernelDevDebugContainer.xml
  paths:
    - src
    - tests
  typeAliases:
    MailInfo: 'object{subject?: string, from?: string, sender?: string, to?: string, date?: mixed, message_id?: string, references?: mixed, in_reply_to?: mixed, size?: int, uid: int, msgno: int, flagged: int, answered: int, deleted: int, seen: int, draft: int, udate?: int}'
  stubFiles:
    - stubs/Mailbox.stub
  ignoreErrors:
    -
      message: "#^Unreachable statement - code above always terminates.$#"
      path: tests
